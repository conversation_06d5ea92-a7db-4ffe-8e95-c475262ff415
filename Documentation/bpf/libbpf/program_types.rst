.. SPDX-License-Identifier: (LGPL-2.1 OR BSD-2-Clause)

.. _program_types_and_elf:

Program Types and ELF Sections
==============================

The table below lists the program types, their attach types where relevant and the ELF section
names supported by libbpf for them. The ELF section names follow these rules:

- ``type`` is an exact match, e.g. ``SEC("socket")``
- ``type+`` means it can be either exact ``SEC("type")`` or well-formed ``SEC("type/extras")``
  with a '``/``' separator between ``type`` and ``extras``.

When ``extras`` are specified, they provide details of how to auto-attach the BPF program.  The
format of ``extras`` depends on the program type, e.g. ``SEC("tracepoint/<category>/<name>")``
for tracepoints or ``SEC("usdt/<path>:<provider>:<name>")`` for USDT probes. The extras are
described in more detail in the footnotes.


+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| Program Type                              | Attach Type                            | ELF Section Name                 | Sleepable |
+===========================================+========================================+==================================+===========+
| ``BPF_PROG_TYPE_CGROUP_DEVICE``           | ``BPF_CGROUP_DEVICE``                  | ``cgroup/dev``                   |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_CGROUP_SKB``              |                                        | ``cgroup/skb``                   |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET_EGRESS``             | ``cgroup_skb/egress``            |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET_INGRESS``            | ``cgroup_skb/ingress``           |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_CGROUP_SOCKOPT``          | ``BPF_CGROUP_GETSOCKOPT``              | ``cgroup/getsockopt``            |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_SETSOCKOPT``              | ``cgroup/setsockopt``            |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_CGROUP_SOCK_ADDR``        | ``BPF_CGROUP_INET4_BIND``              | ``cgroup/bind4``                 |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET4_CONNECT``           | ``cgroup/connect4``              |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET4_GETPEERNAME``       | ``cgroup/getpeername4``          |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET4_GETSOCKNAME``       | ``cgroup/getsockname4``          |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET6_BIND``              | ``cgroup/bind6``                 |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET6_CONNECT``           | ``cgroup/connect6``              |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET6_GETPEERNAME``       | ``cgroup/getpeername6``          |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET6_GETSOCKNAME``       | ``cgroup/getsockname6``          |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_UDP4_RECVMSG``            | ``cgroup/recvmsg4``              |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_UDP4_SENDMSG``            | ``cgroup/sendmsg4``              |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_UDP6_RECVMSG``            | ``cgroup/recvmsg6``              |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_UDP6_SENDMSG``            | ``cgroup/sendmsg6``              |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_CGROUP_SOCK``             | ``BPF_CGROUP_INET4_POST_BIND``         | ``cgroup/post_bind4``            |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET6_POST_BIND``         | ``cgroup/post_bind6``            |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET_SOCK_CREATE``        | ``cgroup/sock_create``           |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``cgroup/sock``                  |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_CGROUP_INET_SOCK_RELEASE``       | ``cgroup/sock_release``          |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_CGROUP_SYSCTL``           | ``BPF_CGROUP_SYSCTL``                  | ``cgroup/sysctl``                |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_EXT``                     |                                        | ``freplace+`` [#fentry]_         |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_FLOW_DISSECTOR``          | ``BPF_FLOW_DISSECTOR``                 | ``flow_dissector``               |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_KPROBE``                  |                                        | ``kprobe+`` [#kprobe]_           |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``kretprobe+`` [#kprobe]_        |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``ksyscall+`` [#ksyscall]_       |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        |  ``kretsyscall+`` [#ksyscall]_   |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``uprobe+`` [#uprobe]_           |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``uprobe.s+`` [#uprobe]_         | Yes       |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``uretprobe+`` [#uprobe]_        |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``uretprobe.s+`` [#uprobe]_      | Yes       |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``usdt+`` [#usdt]_               |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_TRACE_KPROBE_MULTI``             | ``kprobe.multi+`` [#kpmulti]_    |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``kretprobe.multi+`` [#kpmulti]_ |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_LIRC_MODE2``              | ``BPF_LIRC_MODE2``                     | ``lirc_mode2``                   |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_LSM``                     | ``BPF_LSM_CGROUP``                     | ``lsm_cgroup+``                  |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_LSM_MAC``                        | ``lsm+`` [#lsm]_                 |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``lsm.s+`` [#lsm]_               | Yes       |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_LWT_IN``                  |                                        | ``lwt_in``                       |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_LWT_OUT``                 |                                        | ``lwt_out``                      |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_LWT_SEG6LOCAL``           |                                        | ``lwt_seg6local``                |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_LWT_XMIT``                |                                        | ``lwt_xmit``                     |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_PERF_EVENT``              |                                        | ``perf_event``                   |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_RAW_TRACEPOINT_WRITABLE`` |                                        | ``raw_tp.w+`` [#rawtp]_          |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``raw_tracepoint.w+``            |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_RAW_TRACEPOINT``          |                                        | ``raw_tp+`` [#rawtp]_            |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``raw_tracepoint+``              |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SCHED_ACT``               |                                        | ``action``                       |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SCHED_CLS``               |                                        | ``classifier``                   |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``tc``                           |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SK_LOOKUP``               | ``BPF_SK_LOOKUP``                      | ``sk_lookup``                    |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SK_MSG``                  | ``BPF_SK_MSG_VERDICT``                 | ``sk_msg``                       |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SK_REUSEPORT``            | ``BPF_SK_REUSEPORT_SELECT_OR_MIGRATE`` | ``sk_reuseport/migrate``         |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_SK_REUSEPORT_SELECT``            | ``sk_reuseport``                 |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SK_SKB``                  |                                        | ``sk_skb``                       |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_SK_SKB_STREAM_PARSER``           | ``sk_skb/stream_parser``         |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_SK_SKB_STREAM_VERDICT``          | ``sk_skb/stream_verdict``        |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SOCKET_FILTER``           |                                        | ``socket``                       |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SOCK_OPS``                | ``BPF_CGROUP_SOCK_OPS``                | ``sockops``                      |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_STRUCT_OPS``              |                                        | ``struct_ops+``                  |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_SYSCALL``                 |                                        | ``syscall``                      | Yes       |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_TRACEPOINT``              |                                        | ``tp+`` [#tp]_                   |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``tracepoint+`` [#tp]_           |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_TRACING``                 | ``BPF_MODIFY_RETURN``                  | ``fmod_ret+`` [#fentry]_         |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``fmod_ret.s+`` [#fentry]_       | Yes       |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_TRACE_FENTRY``                   | ``fentry+`` [#fentry]_           |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``fentry.s+`` [#fentry]_         | Yes       |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_TRACE_FEXIT``                    | ``fexit+`` [#fentry]_            |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``fexit.s+`` [#fentry]_          | Yes       |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_TRACE_ITER``                     | ``iter+`` [#iter]_               |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``iter.s+`` [#iter]_             | Yes       |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_TRACE_RAW_TP``                   | ``tp_btf+`` [#fentry]_           |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+
| ``BPF_PROG_TYPE_XDP``                     | ``BPF_XDP_CPUMAP``                     | ``xdp.frags/cpumap``             |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``xdp/cpumap``                   |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_XDP_DEVMAP``                     | ``xdp.frags/devmap``             |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``xdp/devmap``                   |           |
+                                           +----------------------------------------+----------------------------------+-----------+
|                                           | ``BPF_XDP``                            | ``xdp.frags``                    |           |
+                                           +                                        +----------------------------------+-----------+
|                                           |                                        | ``xdp``                          |           |
+-------------------------------------------+----------------------------------------+----------------------------------+-----------+


.. rubric:: Footnotes

.. [#fentry] The ``fentry`` attach format is ``fentry[.s]/<function>``.
.. [#kprobe] The ``kprobe`` attach format is ``kprobe/<function>[+<offset>]``. Valid
             characters for ``function`` are ``a-zA-Z0-9_.`` and ``offset`` must be a valid
             non-negative integer.
.. [#ksyscall] The ``ksyscall`` attach format is ``ksyscall/<syscall>``.
.. [#uprobe] The ``uprobe`` attach format is ``uprobe[.s]/<path>:<function>[+<offset>]``.
.. [#usdt] The ``usdt`` attach format is ``usdt/<path>:<provider>:<name>``.
.. [#kpmulti] The ``kprobe.multi`` attach format is ``kprobe.multi/<pattern>`` where ``pattern``
              supports ``*`` and ``?`` wildcards. Valid characters for pattern are
              ``a-zA-Z0-9_.*?``.
.. [#lsm] The ``lsm`` attachment format is ``lsm[.s]/<hook>``.
.. [#rawtp] The ``raw_tp`` attach format is ``raw_tracepoint[.w]/<tracepoint>``.
.. [#tp] The ``tracepoint`` attach format is ``tracepoint/<category>/<name>``.
.. [#iter] The ``iter`` attach format is ``iter[.s]/<struct-name>``.
