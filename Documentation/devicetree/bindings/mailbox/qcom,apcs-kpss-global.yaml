# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/mailbox/qcom,apcs-kpss-global.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm APCS global block

description:
  This binding describes the APCS "global" block found in various Qualcomm
  platforms.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,ipq5018-apcs-apps-global
              - qcom,ipq5332-apcs-apps-global
              - qcom,ipq8074-apcs-apps-global
              - qcom,ipq9574-apcs-apps-global
          - const: qcom,ipq6018-apcs-apps-global
      - items:
          - enum:
              - qcom,sc7180-apss-shared
              - qcom,sc8180x-apss-shared
              - qcom,sm8150-apss-shared
          - const: qcom,sdm845-apss-shared
      - items:
          - enum:
              - qcom,msm8916-apcs-kpss-global
              - qcom,msm8939-apcs-kpss-global
              - qcom,msm8953-apcs-kpss-global
              - qcom,msm8976-apcs-kpss-global
              - qcom,msm8994-apcs-kpss-global
              - qcom,qcs404-apcs-apps-global
              - qcom,sdx55-apcs-gcc
          - const: syscon
      - enum:
          - qcom,ipq6018-apcs-apps-global
          - qcom,ipq8074-apcs-apps-global
          - qcom,msm8996-apcs-hmss-global
          - qcom,msm8998-apcs-hmss-global
          - qcom,qcm2290-apcs-hmss-global
          - qcom,sdm660-apcs-hmss-global
          - qcom,sdm845-apss-shared
          - qcom,sm4250-apcs-hmss-global
          - qcom,sm6115-apcs-hmss-global
          - qcom,sm6125-apcs-hmss-global

  reg:
    maxItems: 1

  clocks:
    description: phandles to the parent clocks of the clock driver
    minItems: 2
    maxItems: 3

  '#mbox-cells':
    const: 1

  '#clock-cells':
    enum: [0, 1]

  clock-names:
    minItems: 2
    maxItems: 3

required:
  - compatible
  - reg
  - '#mbox-cells'

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          enum:
            - qcom,msm8916-apcs-kpss-global
            - qcom,msm8939-apcs-kpss-global
            - qcom,qcs404-apcs-apps-global
    then:
      properties:
        clocks:
          items:
            - description: primary pll parent of the clock driver
            - description: auxiliary parent
        clock-names:
          items:
            - const: pll
            - const: aux

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdx55-apcs-gcc
    then:
      properties:
        clocks:
          items:
            - description: reference clock
            - description: primary pll parent of the clock driver
            - description: auxiliary parent
        clock-names:
          items:
            - const: ref
            - const: pll
            - const: aux
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,ipq6018-apcs-apps-global
    then:
      properties:
        clocks:
          items:
            - description: primary pll parent of the clock driver
            - description: XO clock
        clock-names:
          items:
            - const: pll
            - const: xo

  - if:
      properties:
        compatible:
          enum:
            - qcom,msm8953-apcs-kpss-global
            - qcom,msm8976-apcs-kpss-global
            - qcom,msm8994-apcs-kpss-global
            - qcom,msm8996-apcs-hmss-global
            - qcom,msm8998-apcs-hmss-global
            - qcom,qcm2290-apcs-hmss-global
            - qcom,sdm660-apcs-hmss-global
            - qcom,sdm845-apss-shared
            - qcom,sm4250-apcs-hmss-global
            - qcom,sm6115-apcs-hmss-global
            - qcom,sm6125-apcs-hmss-global
    then:
      properties:
        clocks: false
        clock-names: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,ipq6018-apcs-apps-global
    then:
      properties:
        '#clock-cells':
          const: 1
    else:
      properties:
        '#clock-cells':
          const: 0

examples:

  # Example apcs with msm8996
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    apcs_glb: mailbox@9820000 {
        compatible = "qcom,msm8996-apcs-hmss-global";
        reg = <0x9820000 0x1000>;

        #mbox-cells = <1>;
        #clock-cells = <0>;
    };

    rpm-glink {
        compatible = "qcom,glink-rpm";
        interrupts = <GIC_SPI 168 IRQ_TYPE_EDGE_RISING>;
        qcom,rpm-msg-ram = <&rpm_msg_ram>;
        mboxes = <&apcs_glb 0>;
    };

  # Example apcs with qcs404
  - |
    #define GCC_APSS_AHB_CLK_SRC  1
    #define GCC_GPLL0_AO_OUT_MAIN 123
    apcs: mailbox@b011000 {
        compatible = "qcom,qcs404-apcs-apps-global", "syscon";
        reg = <0x0b011000 0x1000>;
        #mbox-cells = <1>;
        clocks = <&apcs_hfpll>, <&gcc GCC_GPLL0_AO_OUT_MAIN>;
        clock-names = "pll", "aux";
        #clock-cells = <0>;
    };
