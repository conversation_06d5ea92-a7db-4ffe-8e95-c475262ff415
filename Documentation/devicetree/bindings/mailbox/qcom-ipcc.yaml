# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/mailbox/qcom-ipcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. Inter-Processor Communication Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The Inter-Processor Communication Controller (IPCC) is a centralized hardware
  to route interrupts across various subsystems. It involves a three-level
  addressing scheme called protocol, client and signal. For example, consider an
  entity on the Application Processor Subsystem (APSS) that wants to listen to
  Mode<PERSON>'s interrupts via Shared Memory Point to Point (SMP2P) interface. In such
  a case, the client would be Modem (client-id is 2) and the signal would be
  SMP2P (signal-id is 2). The SMP2P itself falls under the Multiprocessor (MPROC)
  protocol (protocol-id is 0). Refer include/dt-bindings/mailbox/qcom-ipcc.h
  for the list of such IDs.

properties:
  compatible:
    items:
      - enum:
          - qcom,qdu1000-ipcc
          - qcom,sa8775p-ipcc
          - qcom,sc7280-ipcc
          - qcom,sc8280xp-ipcc
          - qcom,sm6350-ipcc
          - qcom,sm6375-ipcc
          - qcom,sm8250-ipcc
          - qcom,sm8350-ipcc
          - qcom,sm8450-ipcc
          - qcom,sm8550-ipcc
      - const: qcom,ipcc

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  interrupt-controller: true

  "#interrupt-cells":
    const: 3
    description:
      The first cell is the client-id, the second cell is the signal-id and the
      third cell is the interrupt type.

  "#mbox-cells":
    const: 2
    description:
      The first cell is the client-id, and the second cell is the signal-id.

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - "#interrupt-cells"
  - "#mbox-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/mailbox/qcom-ipcc.h>

    mailbox@408000 {
        compatible = "qcom,sm8250-ipcc", "qcom,ipcc";
        reg = <0x408000 0x1000>;
        interrupts = <GIC_SPI 229 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <3>;
        #mbox-cells = <2>;
    };
