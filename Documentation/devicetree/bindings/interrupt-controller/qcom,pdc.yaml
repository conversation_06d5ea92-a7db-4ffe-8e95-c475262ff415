# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/qcom,pdc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: PDC interrupt controller

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  Qualcomm Technologies Inc. SoCs based on the RPM Hardened architecture have a
  Power Domain Controller (PDC) that is on always-on domain. In addition to
  providing power control for the power domains, the hardware also has an
  interrupt controller that can be used to help detect edge low interrupts as
  well detect interrupts when the GIC is non-operational.

  GIC is parent interrupt controller at the highest level. Platform interrupt
  controller PDC is next in hierarchy, followed by others. Drivers requiring
  wakeup capabilities of their device interrupts routed through the PDC, must
  specify PDC as their interrupt controller and request the PDC port associated
  with the GIC interrupt. See example below.

properties:
  compatible:
    items:
      - enum:
          - qcom,qdu1000-pdc
          - qcom,sa8775p-pdc
          - qcom,sc7180-pdc
          - qcom,sc7280-pdc
          - qcom,sc8280xp-pdc
          - qcom,sdm670-pdc
          - qcom,sdm845-pdc
          - qcom,sdx55-pdc
          - qcom,sdx65-pdc
          - qcom,sm6350-pdc
          - qcom,sm8150-pdc
          - qcom,sm8250-pdc
          - qcom,sm8350-pdc
          - qcom,sm8450-pdc
      - const: qcom,pdc

  reg:
    minItems: 1
    items:
      - description: PDC base register region
      - description: Edge or Level config register for SPI interrupts

  '#interrupt-cells':
    const: 2

  interrupt-controller: true

  qcom,pdc-ranges:
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    minItems: 1
    maxItems: 128 # no hard limit
    items:
      items:
        - description: starting PDC port
        - description: GIC hwirq number for the PDC port
        - description: number of interrupts in sequence
    description: |
      Specifies the PDC pin offset and the number of PDC ports.
      The tuples indicates the valid mapping of valid PDC ports
      and their hwirq mapping.

required:
  - compatible
  - reg
  - '#interrupt-cells'
  - interrupt-controller
  - qcom,pdc-ranges

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    pdc: interrupt-controller@b220000 {
        compatible = "qcom,sdm845-pdc", "qcom,pdc";
        reg = <0xb220000 0x30000>;
        qcom,pdc-ranges = <0 512 94>, <94 641 15>, <115 662 7>;
        #interrupt-cells = <2>;
        interrupt-parent = <&intc>;
        interrupt-controller;
    };

    wake-device {
        interrupts-extended = <&pdc 2 IRQ_TYPE_LEVEL_HIGH>;
    };
