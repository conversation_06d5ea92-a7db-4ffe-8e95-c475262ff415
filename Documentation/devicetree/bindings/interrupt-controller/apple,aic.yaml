# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/apple,aic.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Apple Interrupt Controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Apple Interrupt Controller is a simple interrupt controller present on
  Apple ARM SoC platforms, including various iPhone and iPad devices and the
  "Apple Silicon" Macs.

  It provides the following features:

  - Level-triggered hardware IRQs wired to SoC blocks
    - Single mask bit per IRQ
    - Per-IRQ affinity setting
    - Automatic masking on event delivery (auto-ack)
    - Software triggering (ORed with hw line)
  - 2 per-CPU IPIs (meant as "self" and "other", but they are interchangeable
    if not symmetric)
  - Automatic prioritization (single event/ack register per CPU, lower IRQs =
    higher priority)
  - Automatic masking on ack
  - Default "this CPU" register view and explicit per-CPU views

  This device also represents the FIQ interrupt sources on platforms using AIC,
  which do not go through a discrete interrupt controller.

allOf:
  - $ref: /schemas/interrupt-controller.yaml#

properties:
  compatible:
    items:
      - const: apple,t8103-aic
      - const: apple,aic

  interrupt-controller: true

  '#interrupt-cells':
    const: 3
    description: |
      The 1st cell contains the interrupt type:
        - 0: Hardware IRQ
        - 1: FIQ

      The 2nd cell contains the interrupt number.
        - HW IRQs: interrupt number
        - FIQs:
          - 0: physical HV timer
          - 1: virtual HV timer
          - 2: physical guest timer
          - 3: virtual guest timer
          - 4: 'efficient' CPU PMU
          - 5: 'performance' CPU PMU

      The 3rd cell contains the interrupt flags. This is normally
      IRQ_TYPE_LEVEL_HIGH (4).

  reg:
    description: |
      Specifies base physical address and size of the AIC registers.
    maxItems: 1

  power-domains:
    maxItems: 1

  affinities:
    type: object
    additionalProperties: false
    description:
      FIQ affinity can be expressed as a single "affinities" node,
      containing a set of sub-nodes, one per FIQ with a non-default
      affinity.
    patternProperties:
      "^.+-affinity$":
        type: object
        additionalProperties: false
        properties:
          apple,fiq-index:
            description:
              The interrupt number specified as a FIQ, and for which
              the affinity is not the default.
            $ref: /schemas/types.yaml#/definitions/uint32
            maximum: 5

          cpus:
            description:
              Should be a list of phandles to CPU nodes (as described in
              Documentation/devicetree/bindings/arm/cpus.yaml).

        required:
          - apple,fiq-index
          - cpus

required:
  - compatible
  - '#interrupt-cells'
  - interrupt-controller
  - reg

additionalProperties: false

examples:
  - |
    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        aic: interrupt-controller@23b100000 {
            compatible = "apple,t8103-aic", "apple,aic";
            #interrupt-cells = <3>;
            interrupt-controller;
            reg = <0x2 0x3b100000 0x0 0x8000>;
        };
    };
