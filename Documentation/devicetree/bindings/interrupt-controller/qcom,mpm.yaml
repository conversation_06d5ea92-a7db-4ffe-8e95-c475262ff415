# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/qcom,mpm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcom MPM Interrupt Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Qualcomm Technologies Inc. SoCs based on the RPM architecture have a
  MSM Power Manager (MPM) that is in always-on domain. In addition to managing
  resources during sleep, the hardware also has an interrupt controller that
  monitors the interrupts when the system is asleep, wakes up the APSS when
  one of these interrupts occur and replays it to GIC interrupt controller
  after GIC becomes operational.

allOf:
  - $ref: /schemas/interrupt-controller.yaml#

properties:
  compatible:
    items:
      - const: qcom,mpm

  reg:
    maxItems: 1
    description:
      Specifies the base address and size of vMPM registers in RPM MSG RAM.

  interrupts:
    maxItems: 1
    description:
      Specify the IRQ used by RPM to wakeup APSS.

  mboxes:
    maxItems: 1
    description:
      Specify the mailbox used to notify RPM for writing vMPM registers.

  interrupt-controller: true

  '#interrupt-cells':
    const: 2
    description:
      The first cell is the MPM pin number for the interrupt, and the second
      is the trigger type.

  qcom,mpm-pin-count:
    description:
      Specify the total MPM pin count that a SoC supports.
    $ref: /schemas/types.yaml#/definitions/uint32

  qcom,mpm-pin-map:
    description:
      A set of MPM pin numbers and the corresponding GIC SPIs.
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    items:
      items:
        - description: MPM pin number
        - description: GIC SPI number for the MPM pin

required:
  - compatible
  - reg
  - interrupts
  - mboxes
  - interrupt-controller
  - '#interrupt-cells'
  - qcom,mpm-pin-count
  - qcom,mpm-pin-map

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    mpm: interrupt-controller@45f01b8 {
        compatible = "qcom,mpm";
        interrupts = <GIC_SPI 197 IRQ_TYPE_EDGE_RISING>;
        reg = <0x45f01b8 0x1000>;
        mboxes = <&apcs_glb 1>;
        interrupt-controller;
        #interrupt-cells = <2>;
        interrupt-parent = <&intc>;
        qcom,mpm-pin-count = <96>;
        qcom,mpm-pin-map = <2 275>,
                           <5 296>,
                           <12 422>,
                           <24 79>,
                           <86 183>,
                           <90 260>,
                           <91 260>;
    };
