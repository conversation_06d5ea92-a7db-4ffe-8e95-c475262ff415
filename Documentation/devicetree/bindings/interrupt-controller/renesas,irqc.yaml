# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/renesas,irqc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: R-Mobile/R-Car/RZ/G interrupt controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - renesas,irqc-r8a73a4        # R-Mobile APE6
          - renesas,irqc-r8a7742        # RZ/G1H
          - renesas,irqc-r8a7743        # RZ/G1M
          - renesas,irqc-r8a7744        # RZ/G1N
          - renesas,irqc-r8a7745        # RZ/G1E
          - renesas,irqc-r8a77470       # RZ/G1C
          - renesas,irqc-r8a7790        # R-Car H2
          - renesas,irqc-r8a7791        # R-Car M2-W
          - renesas,irqc-r8a7792        # R-Car V2H
          - renesas,irqc-r8a7793        # R-Car M2-N
          - renesas,irqc-r8a7794        # R-Car E2
          - renesas,intc-ex-r8a774a1    # RZ/G2M
          - renesas,intc-ex-r8a774b1    # RZ/G2N
          - renesas,intc-ex-r8a774c0    # RZ/G2E
          - renesas,intc-ex-r8a774e1    # RZ/G2H
          - renesas,intc-ex-r8a7795     # R-Car H3
          - renesas,intc-ex-r8a7796     # R-Car M3-W
          - renesas,intc-ex-r8a77961    # R-Car M3-W+
          - renesas,intc-ex-r8a77965    # R-Car M3-N
          - renesas,intc-ex-r8a77970    # R-Car V3M
          - renesas,intc-ex-r8a77980    # R-Car V3H
          - renesas,intc-ex-r8a77990    # R-Car E3
          - renesas,intc-ex-r8a77995    # R-Car D3
          - renesas,intc-ex-r8a779a0    # R-Car V3U
          - renesas,intc-ex-r8a779f0    # R-Car S4-8
          - renesas,intc-ex-r8a779g0    # R-Car V4H
      - const: renesas,irqc

  '#interrupt-cells':
    # an interrupt index and flags, as defined in interrupts.txt in
    # this directory
    const: 2

  interrupt-controller: true

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 32

  clocks:
    maxItems: 1

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

required:
  - compatible
  - '#interrupt-cells'
  - interrupt-controller
  - reg
  - interrupts
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/r8a7790-cpg-mssr.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    irqc0: interrupt-controller@e61c0000 {
        compatible = "renesas,irqc-r8a7790", "renesas,irqc";
        #interrupt-cells = <2>;
        interrupt-controller;
        reg = <0xe61c0000 0x200>;
        interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&cpg CPG_MOD 407>;
    };
