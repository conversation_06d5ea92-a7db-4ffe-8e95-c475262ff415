# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/amlogic,meson-gpio-intc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson GPIO interrupt controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Meson SoCs contains an interrupt controller which is able to watch the SoC
  pads and generate an interrupt on edge or level. The controller is essentially
  a 256 pads to 8 or 12 GIC interrupt multiplexer, with a filter block to select
  edge or level and polarity. It does not expose all 256 mux inputs because the
  documentation shows that the upper part is not mapped to any pad. The actual
  number of interrupts exposed depends on the SoC.

allOf:
  - $ref: /schemas/interrupt-controller.yaml#

properties:
  compatible:
    oneOf:
      - const: amlogic,meson-gpio-intc
      - items:
          - enum:
              - amlogic,meson8-gpio-intc
              - amlogic,meson8b-gpio-intc
              - amlogic,meson-gxbb-gpio-intc
              - amlogic,meson-gxl-gpio-intc
              - amlogic,meson-axg-gpio-intc
              - amlogic,meson-g12a-gpio-intc
              - amlogic,meson-sm1-gpio-intc
              - amlogic,meson-a1-gpio-intc
              - amlogic,meson-s4-gpio-intc
              - amlogic,c3-gpio-intc
          - const: amlogic,meson-gpio-intc

  reg:
    maxItems: 1

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  amlogic,channel-interrupts:
    description: Array with the upstream hwirq numbers
    minItems: 8
    maxItems: 12
    $ref: /schemas/types.yaml#/definitions/uint32-array

required:
  - compatible
  - reg
  - interrupt-controller
  - "#interrupt-cells"
  - amlogic,channel-interrupts

additionalProperties: false

examples:
  - |
    interrupt-controller@9880 {
      compatible = "amlogic,meson-gxbb-gpio-intc",
                   "amlogic,meson-gpio-intc";
      reg = <0x9880 0x10>;
      interrupt-controller;
      #interrupt-cells = <2>;
      amlogic,channel-interrupts = <64 65 66 67 68 69 70 71>;
    };
