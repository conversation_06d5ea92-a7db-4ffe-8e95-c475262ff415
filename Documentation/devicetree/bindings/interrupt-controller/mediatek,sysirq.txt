MediaTek sysirq

MediaTek SOCs sysirq support controllable irq inverter for each GIC SPI
interrupt.

Required properties:
- compatible: should be
	"mediatek,mt8516-sysirq", "mediatek,mt6577-sysirq": for MT8516
	"mediatek,mt8183-sysirq", "mediatek,mt6577-sysirq": for MT8183
	"mediatek,mt8173-sysirq", "mediatek,mt6577-sysirq": for MT8173
	"mediatek,mt8135-sysirq", "mediatek,mt6577-sysirq": for MT8135
	"mediatek,mt8127-sysirq", "mediatek,mt6577-sysirq": for MT8127
	"mediatek,mt7622-sysirq", "mediatek,mt6577-sysirq": for MT7622
	"mediatek,mt7623-sysirq", "mediatek,mt6577-sysirq": for MT7623
	"mediatek,mt7629-sysirq", "mediatek,mt6577-sysirq": for MT7629
	"mediatek,mt6795-sysirq", "mediatek,mt6577-sysirq": for MT6795
	"mediatek,mt6797-sysirq", "mediatek,mt6577-sysirq": for MT6797
	"mediatek,mt6779-sysirq", "mediatek,mt6577-sysirq": for MT6779
	"mediatek,mt6765-sysirq", "mediatek,mt6577-sysirq": for MT6765
	"mediatek,mt6755-sysirq", "mediatek,mt6577-sysirq": for MT6755
	"mediatek,mt6592-sysirq", "mediatek,mt6577-sysirq": for MT6592
	"mediatek,mt6589-sysirq", "mediatek,mt6577-sysirq": for MT6589
	"mediatek,mt6582-sysirq", "mediatek,mt6577-sysirq": for MT6582
	"mediatek,mt6580-sysirq", "mediatek,mt6577-sysirq": for MT6580
	"mediatek,mt6577-sysirq": for MT6577
	"mediatek,mt2712-sysirq", "mediatek,mt6577-sysirq": for MT2712
	"mediatek,mt2701-sysirq", "mediatek,mt6577-sysirq": for MT2701
	"mediatek,mt8365-sysirq", "mediatek,mt6577-sysirq": for MT8365
- interrupt-controller : Identifies the node as an interrupt controller
- #interrupt-cells : Use the same format as specified by GIC in arm,gic.txt.
- reg: Physical base address of the intpol registers and length of memory
  mapped region. Could be multiple bases here. Ex: mt6797 needs 2 reg, others
  need 1.

Example:
	sysirq: intpol-controller@10200620 {
		compatible = "mediatek,mt6797-sysirq",
			     "mediatek,mt6577-sysirq";
		interrupt-controller;
		#interrupt-cells = <3>;
		interrupt-parent = <&gic>;
		reg = <0 0x10220620 0 0x20>,
		      <0 0x10220690 0 0x10>;
	};
