# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/fsl,intmux.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale INTMUX interrupt multiplexer

maintainers:
  - <PERSON> <<EMAIL>>
  - NXP Linux Team <<EMAIL>>

properties:
  compatible:
    const: fsl,imx-intmux

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 8
    description: |
      Should contain the parent interrupt lines (up to 8) used to multiplex
      the input interrupts.

  interrupt-controller: true

  '#interrupt-cells':
    const: 2
    description: |
      The 1st cell is hw interrupt number, the 2nd cell is channel index.

  clocks:
    maxItems: 1

  clock-names:
    const: ipg

required:
  - compatible
  - reg
  - interrupts
  - interrupt-controller
  - '#interrupt-cells'
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    interrupt-controller@37400000 {
        compatible = "fsl,imx-intmux";
        reg = <0x37400000 0x1000>;
        interrupts = <0 16 4>,
                     <0 17 4>,
                     <0 18 4>,
                     <0 19 4>,
                     <0 20 4>,
                     <0 21 4>,
                     <0 22 4>,
                     <0 23 4>;
        interrupt-controller;
        interrupt-parent = <&gic>;
        #interrupt-cells = <2>;
        clocks = <&clk>;
        clock-names = "ipg";
    };
