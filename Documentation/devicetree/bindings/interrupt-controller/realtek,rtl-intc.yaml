# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/realtek,rtl-intc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Realtek RTL SoC interrupt controller

description:
  Interrupt controller and router for Realtek MIPS SoCs, allowing each SoC
  interrupt to be routed to one parent CPU (hardware) interrupt, or left
  disconnected.
  All connected input lines from SoC peripherals can be masked individually,
  and an interrupt status register is present to indicate which interrupts are
  pending.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - realtek,rtl8380-intc
          - const: realtek,rtl-intc
      - const: realtek,rtl-intc
        deprecated: true

  "#interrupt-cells":
    description:
      SoC interrupt line index.
    const: 1

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 15
    description:
      List of parent interrupts, in the order that they are connected to this
      interrupt router's outputs, starting at the first output.

  interrupt-controller: true

  interrupt-map:
    deprecated: true
    description: Describes mapping from SoC interrupts to CPU interrupts

required:
  - compatible
  - reg
  - "#interrupt-cells"
  - interrupt-controller

allOf:
  - if:
      properties:
        compatible:
          const: realtek,rtl-intc
    then:
      properties:
        "#address-cells":
          const: 0
      required:
        - "#address-cells"
        - interrupt-map
    else:
      required:
        - interrupts

additionalProperties: false

examples:
  - |
    interrupt-controller@3000 {
      compatible = "realtek,rtl8380-intc", "realtek,rtl-intc";
      #interrupt-cells = <1>;
      interrupt-controller;
      reg = <0x3000 0x18>;

      interrupt-parent = <&cpuintc>;
      interrupts = <2>, <3>, <4>, <5>, <6>;
    };
