# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interrupt-controller/renesas,rzg2l-irqc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/G2L (and alike SoC's) Interrupt Controller (IA55)

maintainers:
  - Lad <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  IA55 performs various interrupt controls including synchronization for the external
  interrupts of NMI, IRQ, and GPIOINT and the interrupts of the built-in peripheral
  interrupts output by each IP. And it notifies the interrupt to the GIC
    - IRQ sense select for 8 external interrupts, mapped to 8 GIC SPI interrupts
    - GPIO pins used as external interrupt input pins, mapped to 32 GIC SPI interrupts
    - NMI edge select (NMI is not treated as NMI exception and supports fall edge and
      stand-up edge detection interrupts)

properties:
  compatible:
    items:
      - enum:
          - renesas,r9a07g043u-irqc   # RZ/G2UL
          - renesas,r9a07g044-irqc    # RZ/G2{L,LC}
          - renesas,r9a07g054-irqc    # RZ/V2L
      - const: renesas,rzg2l-irqc

  '#interrupt-cells':
    description: The first cell should contain a macro RZG2L_{NMI,IRQX} included in the
                 include/dt-bindings/interrupt-controller/irqc-rzg2l.h and the second
                 cell is used to specify the flag.
    const: 2

  '#address-cells':
    const: 0

  interrupt-controller: true

  reg:
    maxItems: 1

  interrupts:
    minItems: 41
    items:
      - description: NMI interrupt
      - description: IRQ0 interrupt
      - description: IRQ1 interrupt
      - description: IRQ2 interrupt
      - description: IRQ3 interrupt
      - description: IRQ4 interrupt
      - description: IRQ5 interrupt
      - description: IRQ6 interrupt
      - description: IRQ7 interrupt
      - description: GPIO interrupt, TINT0
      - description: GPIO interrupt, TINT1
      - description: GPIO interrupt, TINT2
      - description: GPIO interrupt, TINT3
      - description: GPIO interrupt, TINT4
      - description: GPIO interrupt, TINT5
      - description: GPIO interrupt, TINT6
      - description: GPIO interrupt, TINT7
      - description: GPIO interrupt, TINT8
      - description: GPIO interrupt, TINT9
      - description: GPIO interrupt, TINT10
      - description: GPIO interrupt, TINT11
      - description: GPIO interrupt, TINT12
      - description: GPIO interrupt, TINT13
      - description: GPIO interrupt, TINT14
      - description: GPIO interrupt, TINT15
      - description: GPIO interrupt, TINT16
      - description: GPIO interrupt, TINT17
      - description: GPIO interrupt, TINT18
      - description: GPIO interrupt, TINT19
      - description: GPIO interrupt, TINT20
      - description: GPIO interrupt, TINT21
      - description: GPIO interrupt, TINT22
      - description: GPIO interrupt, TINT23
      - description: GPIO interrupt, TINT24
      - description: GPIO interrupt, TINT25
      - description: GPIO interrupt, TINT26
      - description: GPIO interrupt, TINT27
      - description: GPIO interrupt, TINT28
      - description: GPIO interrupt, TINT29
      - description: GPIO interrupt, TINT30
      - description: GPIO interrupt, TINT31
      - description: Bus error interrupt

  interrupt-names:
    minItems: 41
    items:
      - const: nmi
      - const: irq0
      - const: irq1
      - const: irq2
      - const: irq3
      - const: irq4
      - const: irq5
      - const: irq6
      - const: irq7
      - const: tint0
      - const: tint1
      - const: tint2
      - const: tint3
      - const: tint4
      - const: tint5
      - const: tint6
      - const: tint7
      - const: tint8
      - const: tint9
      - const: tint10
      - const: tint11
      - const: tint12
      - const: tint13
      - const: tint14
      - const: tint15
      - const: tint16
      - const: tint17
      - const: tint18
      - const: tint19
      - const: tint20
      - const: tint21
      - const: tint22
      - const: tint23
      - const: tint24
      - const: tint25
      - const: tint26
      - const: tint27
      - const: tint28
      - const: tint29
      - const: tint30
      - const: tint31
      - const: bus-err

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: clk
      - const: pclk

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

required:
  - compatible
  - '#interrupt-cells'
  - '#address-cells'
  - interrupt-controller
  - reg
  - interrupts
  - clocks
  - clock-names
  - power-domains
  - resets

allOf:
  - $ref: /schemas/interrupt-controller.yaml#

  - if:
      properties:
        compatible:
          contains:
            const: renesas,r9a07g043u-irqc
    then:
      properties:
        interrupts:
          minItems: 42
        interrupt-names:
          minItems: 42
      required:
        - interrupt-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/r9a07g044-cpg.h>

    irqc: interrupt-controller@110a0000 {
        compatible = "renesas,r9a07g044-irqc", "renesas,rzg2l-irqc";
        reg = <0x110a0000 0x10000>;
        #interrupt-cells = <2>;
        #address-cells = <0>;
        interrupt-controller;
        interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 444 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 445 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 446 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 447 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 448 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 449 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 450 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 451 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 452 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 453 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 454 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 455 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 456 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 457 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 458 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 459 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 460 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 461 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 462 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 463 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 464 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 465 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 466 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 467 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 468 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 469 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 470 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 471 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 472 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 473 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 474 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 475 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-names = "nmi",
                          "irq0", "irq1", "irq2", "irq3",
                          "irq4", "irq5", "irq6", "irq7",
                          "tint0", "tint1", "tint2", "tint3",
                          "tint4", "tint5", "tint6", "tint7",
                          "tint8", "tint9", "tint10", "tint11",
                          "tint12", "tint13", "tint14", "tint15",
                          "tint16", "tint17", "tint18", "tint19",
                          "tint20", "tint21", "tint22", "tint23",
                          "tint24", "tint25", "tint26", "tint27",
                          "tint28", "tint29", "tint30", "tint31";
        clocks = <&cpg CPG_MOD R9A07G044_IA55_CLK>,
                 <&cpg CPG_MOD R9A07G044_IA55_PCLK>;
        clock-names = "clk", "pclk";
        power-domains = <&cpg>;
        resets = <&cpg R9A07G044_IA55_RESETN>;
    };
