# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/i3c/aspeed,ast2600-i3c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ASPEED AST2600 i3c controller

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: i3c.yaml#

properties:
  compatible:
    const: aspeed,ast2600-i3c

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  resets:
    maxItems: 1

  interrupts:
    maxItems: 1

  sda-pullup-ohms:
    enum: [545, 750, 2000]
    default: 2000
    description: |
      Value to configure SDA pullup resistor, in Ohms.

  aspeed,global-regs:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: phandle to i3c global register syscon node
          - description: index of this i3c controller in the global register set
    description: |
      A (phandle, controller index) reference to the i3c global register set
      used for this device.

required:
  - compatible
  - reg
  - clocks
  - interrupts
  - aspeed,global-regs

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    i3c-master@2000 {
        compatible = "aspeed,ast2600-i3c";
        reg = <0x2000 0x1000>;
        #address-cells = <3>;
        #size-cells = <0>;
        clocks = <&syscon 0>;
        resets = <&syscon 0>;
        aspeed,global-regs = <&i3c_global 0>;
        pinctrl-names = "default";
        pinctrl-0 = <&pinctrl_i3c1_default>;
        interrupts = <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
    };
...
