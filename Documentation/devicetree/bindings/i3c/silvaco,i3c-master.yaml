# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/i3c/silvaco,i3c-master.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Silvaco I3C master

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: i3c.yaml#

properties:
  compatible:
    const: silvaco,i3c-master-v1

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: system clock
      - description: bus clock
      - description: other (slower) events clock

  clock-names:
    items:
      - const: pclk
      - const: fast_clk
      - const: slow_clk

  resets:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clock-names
  - clocks

unevaluatedProperties: false

examples:
  - |
    i3c-master@a0000000 {
        compatible = "silvaco,i3c-master-v1";
        clocks = <&zynqmp_clk 71>, <&fclk>, <&sclk>;
        clock-names = "pclk", "fast_clk", "slow_clk";
        interrupt-parent = <&gic>;
        interrupts = <0 89 4>;
        reg = <0xa0000000 0x1000>;
        #address-cells = <3>;
        #size-cells = <0>;
    };
