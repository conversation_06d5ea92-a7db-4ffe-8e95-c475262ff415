# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i3c/mipi-i3c-hci.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MIPI I3C HCI

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i3c/i3c.yaml#

description: |
  MIPI I3C Host Controller Interface

  The MIPI I3C HCI (Host Controller Interface) specification defines
  a common software driver interface to support compliant MIPI I3C
  host controller hardware implementations from multiple vendors.

  The hardware is self-advertising for differences in implementation
  capabilities, including the spec version it is based on, so there
  isn't much to describe here (yet).

  For details, please see:
  https://www.mipi.org/specifications/i3c-hci

properties:
  compatible:
    const: mipi-i3c-hci
  reg:
    maxItems: 1
  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

unevaluatedProperties: false

examples:
  - |
    i3c-master@a0000000 {
      compatible = "mipi-i3c-hci";
      reg = <0xa0000000 0x2000>;
      interrupts = <89>;
      #address-cells = <3>;
      #size-cells = <0>;
    };
