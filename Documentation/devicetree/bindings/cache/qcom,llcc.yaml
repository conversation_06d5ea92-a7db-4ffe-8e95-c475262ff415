# SPDX-License-Identifier: (GPL-2.0-or-later OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/cache/qcom,llcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Last Level Cache Controller

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description: |
  LLCC (Last Level Cache Controller) provides last level of cache memory in SoC,
  that can be shared by multiple clients. Clients here are different cores in the
  SoC, the idea is to minimize the local caches at the clients and migrate to
  common pool of memory. Cache memory is divided into partitions called slices
  which are assigned to clients. Clients can query the slice details, activate
  and deactivate them.

properties:
  compatible:
    enum:
      - qcom,sc7180-llcc
      - qcom,sc7280-llcc
      - qcom,sc8180x-llcc
      - qcom,sc8280xp-llcc
      - qcom,sdm845-llcc
      - qcom,sm6350-llcc
      - qcom,sm7150-llcc
      - qcom,sm8150-llcc
      - qcom,sm8250-llcc
      - qcom,sm8350-llcc
      - qcom,sm8450-llcc
      - qcom,sm8550-llcc

  reg:
    minItems: 2
    maxItems: 9

  reg-names:
    minItems: 2
    maxItems: 9

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - reg-names

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc7180-llcc
              - qcom,sm6350-llcc
    then:
      properties:
        reg:
          items:
            - description: LLCC0 base register region
            - description: LLCC broadcast base register region
        reg-names:
          items:
            - const: llcc0_base
            - const: llcc_broadcast_base

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc7280-llcc
    then:
      properties:
        reg:
          items:
            - description: LLCC0 base register region
            - description: LLCC1 base register region
            - description: LLCC broadcast base register region
        reg-names:
          items:
            - const: llcc0_base
            - const: llcc1_base
            - const: llcc_broadcast_base

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc8180x-llcc
              - qcom,sc8280xp-llcc
    then:
      properties:
        reg:
          items:
            - description: LLCC0 base register region
            - description: LLCC1 base register region
            - description: LLCC2 base register region
            - description: LLCC3 base register region
            - description: LLCC4 base register region
            - description: LLCC5 base register region
            - description: LLCC6 base register region
            - description: LLCC7 base register region
            - description: LLCC broadcast base register region
        reg-names:
          items:
            - const: llcc0_base
            - const: llcc1_base
            - const: llcc2_base
            - const: llcc3_base
            - const: llcc4_base
            - const: llcc5_base
            - const: llcc6_base
            - const: llcc7_base
            - const: llcc_broadcast_base

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdm845-llcc
              - qcom,sm8150-llcc
              - qcom,sm8250-llcc
              - qcom,sm8350-llcc
              - qcom,sm8450-llcc
              - qcom,sm8550-llcc
    then:
      properties:
        reg:
          items:
            - description: LLCC0 base register region
            - description: LLCC1 base register region
            - description: LLCC2 base register region
            - description: LLCC3 base register region
            - description: LLCC broadcast base register region
        reg-names:
          items:
            - const: llcc0_base
            - const: llcc1_base
            - const: llcc2_base
            - const: llcc3_base
            - const: llcc_broadcast_base

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        system-cache-controller@1100000 {
            compatible = "qcom,sdm845-llcc";
            reg = <0 0x01100000 0 0x50000>, <0 0x01180000 0 0x50000>,
                <0 0x01200000 0 0x50000>, <0 0x01280000 0 0x50000>,
                <0 0x01300000 0 0x50000>;
            reg-names = "llcc0_base", "llcc1_base", "llcc2_base",
                "llcc3_base", "llcc_broadcast_base";
            interrupts = <GIC_SPI 582 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
