# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright (C) 2020 SiFive, Inc.
%YAML 1.2
---
$id: http://devicetree.org/schemas/cache/sifive,ccache0.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SiFive Composable Cache Controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The SiFive Composable Cache Controller is used to provide access to fast copies
  of memory for masters in a Core Complex. The Composable Cache Controller also
  acts as directory-based coherency manager.
  All the properties in ePAPR/DeviceTree specification applies for this platform.

select:
  properties:
    compatible:
      contains:
        enum:
          - sifive,ccache0
          - sifive,fu540-c000-ccache
          - sifive,fu740-c000-ccache

  required:
    - compatible

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - sifive,ccache0
              - sifive,fu540-c000-ccache
              - sifive,fu740-c000-ccache
          - const: cache
      - items:
          - const: starfive,jh7110-ccache
          - const: sifive,ccache0
          - const: cache
      - items:
          - const: microchip,mpfs-ccache
          - const: sifive,fu540-c000-ccache
          - const: cache

  cache-block-size:
    const: 64

  cache-level:
    enum: [2, 3]

  cache-sets:
    enum: [1024, 2048]

  cache-size:
    const: 2097152

  cache-unified: true

  interrupts:
    minItems: 3
    items:
      - description: DirError interrupt
      - description: DataError interrupt
      - description: DataFail interrupt
      - description: DirFail interrupt

  reg:
    maxItems: 1

  next-level-cache: true

  memory-region:
    maxItems: 1
    description: |
      The reference to the reserved-memory for the L2 Loosely Integrated Memory region.
      The reserved memory node should be defined as per the bindings in reserved-memory.txt.

allOf:
  - $ref: /schemas/cache-controller.yaml#

  - if:
      properties:
        compatible:
          contains:
            enum:
              - sifive,fu740-c000-ccache
              - starfive,jh7110-ccache
              - microchip,mpfs-ccache

    then:
      properties:
        interrupts:
          description: |
            Must contain entries for DirError, DataError, DataFail, DirFail signals.
          minItems: 4

    else:
      properties:
        interrupts:
          description: |
            Must contain entries for DirError, DataError and DataFail signals.
          maxItems: 3

  - if:
      properties:
        compatible:
          contains:
            enum:
              - sifive,fu740-c000-ccache
              - starfive,jh7110-ccache

    then:
      properties:
        cache-sets:
          const: 2048

    else:
      properties:
        cache-sets:
          const: 1024

  - if:
      properties:
        compatible:
          contains:
            const: sifive,ccache0

    then:
      properties:
        cache-level:
          enum: [2, 3]

    else:
      properties:
        cache-level:
          const: 2

additionalProperties: false

required:
  - compatible
  - cache-block-size
  - cache-level
  - cache-sets
  - cache-size
  - cache-unified
  - interrupts
  - reg

examples:
  - |
    cache-controller@2010000 {
        compatible = "sifive,fu540-c000-ccache", "cache";
        cache-block-size = <64>;
        cache-level = <2>;
        cache-sets = <1024>;
        cache-size = <2097152>;
        cache-unified;
        reg = <0x2010000 0x1000>;
        interrupt-parent = <&plic0>;
        interrupts = <1>,
                     <2>,
                     <3>;
        next-level-cache = <&L25>;
        memory-region = <&l2_lim>;
    };
