# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/cpu/nvidia,tegra186-ccplex-cluster.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra186 CCPLEX Cluster

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: nvidia,tegra186-ccplex-cluster

  reg:
    maxItems: 1

  nvidia,bpmp:
    description: phandle to the BPMP used to query CPU frequency tables
    $ref: /schemas/types.yaml#/definitions/phandle

additionalProperties: false

required:
  - compatible
  - reg
  - nvidia,bpmp

examples:
  - |
    ccplex@e000000 {
        compatible = "nvidia,tegra186-ccplex-cluster";
        reg = <0x0e000000 0x400000>;
        nvidia,bpmp = <&bpmp>;
    };
