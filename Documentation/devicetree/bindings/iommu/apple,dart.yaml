# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iommu/apple,dart.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Apple DART IOMMU

maintainers:
  - <PERSON> <<EMAIL>>

description: |+
  Apple SoCs may contain an implementation of their Device Address
  Resolution Table which provides a mandatory layer of address
  translations for various masters.

  Each DART instance is capable of handling up to 16 different streams
  with individual pagetables and page-level read/write protection flags.

  This DART IOMMU also raises interrupts in response to various
  fault conditions.

properties:
  compatible:
    enum:
      - apple,t8103-dart
      - apple,t8110-dart
      - apple,t6000-dart

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    description:
      Reference to the gate clock phandle if required for this IOMMU.
      Optional since not all IOMMUs are attached to a clock gate.

  '#iommu-cells':
    const: 1
    description:
      Has to be one. The single cell describes the stream id emitted by
      a master to the IOMMU.

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - '#iommu-cells'
  - interrupts

additionalProperties: false

examples:
  - |+
    dart1: iommu@82f80000 {
      compatible = "apple,t8103-dart";
      reg = <0x82f80000 0x4000>;
      interrupts = <1 781 4>;
      #iommu-cells = <1>;
    };

    master1 {
      iommus = <&dart1 0>;
    };

  - |+
    dart2a: iommu@82f00000 {
      compatible = "apple,t8103-dart";
      reg = <0x82f00000 0x4000>;
      interrupts = <1 781 4>;
      #iommu-cells = <1>;
    };
    dart2b: iommu@82f80000 {
      compatible = "apple,t8103-dart";
      reg = <0x82f80000 0x4000>;
      interrupts = <1 781 4>;
      #iommu-cells = <1>;
    };

    master2 {
      iommus = <&dart2a 0>, <&dart2b 1>;
    };
