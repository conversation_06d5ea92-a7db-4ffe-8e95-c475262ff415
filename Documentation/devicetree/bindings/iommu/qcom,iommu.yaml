# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iommu/qcom,iommu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies legacy IOMMU implementations

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm "B" family devices which are not compatible with arm-smmu have
  a similar looking IOMMU, but without access to the global register space
  and optionally requiring additional configuration to route context IRQs
  to non-secure vs secure interrupt line.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,msm8916-iommu
              - qcom,msm8953-iommu
          - const: qcom,msm-iommu-v1
      - items:
          - enum:
              - qcom,msm8976-iommu
          - const: qcom,msm-iommu-v2

  clocks:
    items:
      - description: Clock required for IOMMU register group access
      - description: Clock required for underlying bus access

  clock-names:
    items:
      - const: iface
      - const: bus

  power-domains:
    maxItems: 1

  reg:
    maxItems: 1

  ranges: true

  qcom,iommu-secure-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      The SCM secure ID of the IOMMU instance.

  '#address-cells':
    const: 1

  '#size-cells':
    const: 1

  '#iommu-cells':
    const: 1

patternProperties:
  "^iommu-ctx@[0-9a-f]+$":
    type: object
    additionalProperties: false
    properties:
      compatible:
        enum:
          - qcom,msm-iommu-v1-ns
          - qcom,msm-iommu-v1-sec
          - qcom,msm-iommu-v2-ns
          - qcom,msm-iommu-v2-sec

      interrupts:
        maxItems: 1

      reg:
        maxItems: 1

      qcom,ctx-asid:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          The ASID number associated to the context bank.

    required:
      - compatible
      - interrupts
      - reg

required:
  - compatible
  - clocks
  - clock-names
  - ranges
  - '#address-cells'
  - '#size-cells'
  - '#iommu-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8916.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    apps_iommu: iommu@1e20000 {
      compatible = "qcom,msm8916-iommu", "qcom,msm-iommu-v1";
      reg = <0x01ef0000 0x3000>;
      clocks = <&gcc GCC_SMMU_CFG_CLK>,
               <&gcc GCC_APSS_TCU_CLK>;
      clock-names = "iface", "bus";
      qcom,iommu-secure-id = <17>;
      #address-cells = <1>;
      #size-cells = <1>;
      #iommu-cells = <1>;
      ranges = <0 0x01e20000 0x40000>;

      /* mdp_0: */
      iommu-ctx@4000 {
        compatible = "qcom,msm-iommu-v1-ns";
        reg = <0x4000 0x1000>;
        interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>;
      };
    };
