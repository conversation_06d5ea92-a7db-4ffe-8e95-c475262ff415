# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2022 Microchip Technology, Inc. and its subsidiaries
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/atmel,at91sam-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: I2C for Atmel/Microchip platforms

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - atmel,at91rm9200-i2c
              - atmel,at91sam9261-i2c
              - atmel,at91sam9260-i2c
              - atmel,at91sam9g20-i2c
              - atmel,at91sam9g10-i2c
              - atmel,at91sam9x5-i2c
              - atmel,sama5d4-i2c
              - atmel,sama5d2-i2c
              - microchip,sam9x60-i2c
      - items:
          - const: microchip,sama7g5-i2c
          - const: microchip,sam9x60-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  clocks:
    maxItems: 1

  clock-frequency:
    default: 100000

  dmas:
    items:
      - description: TX DMA Channel Specifier
      - description: RX DMA Channel Specifier

  dma-names:
    items:
      - const: tx
      - const: rx

  atmel,fifo-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Maximum number of data the RX and TX FIFOs can store for
      FIFO capable I2C controllers.

  scl-gpios: true

  sda-gpios: true

required:
  - compatible
  - reg
  - interrupts
  - "#address-cells"
  - "#size-cells"
  - clocks

allOf:
  - $ref: i2c-controller.yaml
  - if:
      properties:
        compatible:
          contains:
            enum:
              - atmel,sama5d4-i2c
              - atmel,sama5d2-i2c
              - microchip,sam9x60-i2c
              - microchip,sama7g5-i2c
    then:
      properties:
        i2c-sda-hold-time-ns:
          description:
            TWD hold time
          maxItems: 1

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/dma/at91.h>
    #include <dt-bindings/gpio/gpio.h>

    i2c0: i2c@fff84000 {
        compatible = "atmel,at91sam9g20-i2c";
        reg = <0xfff84000 0x100>;
        interrupts = <12 IRQ_TYPE_LEVEL_HIGH 6>;
        #address-cells = <1>;
        #size-cells = <0>;
        clocks = <&twi0_clk>;
        clock-frequency = <400000>;

        eeprom@50 {
            compatible = "atmel,24c512";
            reg = <0x50>;
            pagesize = <128>;
        };
    };

    i2c1: i2c@f8034600 {
        compatible = "atmel,sama5d2-i2c";
        reg = <0xf8034600 0x100>;
        interrupts = <19 IRQ_TYPE_LEVEL_HIGH 7>;
        dmas = <&dma0
            (AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1))
            AT91_XDMAC_DT_PERID(11)>,
               <&dma0
            (AT91_XDMAC_DT_MEM_IF(0) | AT91_XDMAC_DT_PER_IF(1))
            AT91_XDMAC_DT_PERID(12)>;
        dma-names = "tx", "rx";
        #address-cells = <1>;
        #size-cells = <0>;
        clocks = <&flx0>;
        atmel,fifo-size = <16>;
        i2c-sda-hold-time-ns = <336>;
        pinctrl-names = "default", "gpio";
        pinctrl-0 = <&pinctrl_i2c0>;
        pinctrl-1 = <&pinctrl_i2c0_gpio>;
        sda-gpios = <&pioA 30 GPIO_ACTIVE_HIGH>;
        scl-gpios = <&pioA 31 (GPIO_ACTIVE_HIGH | GPIO_OPEN_DRAIN)>;

        eeprom@54 {
            compatible = "atmel,24c02";
            reg = <0x54>;
            pagesize = <16>;
        };
    };
