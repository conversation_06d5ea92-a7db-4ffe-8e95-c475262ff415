# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Bay<PERSON>ibre, SAS
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/amlogic,meson6-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson I2C Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    enum:
      - amlogic,meson6-i2c # Meson6, Meson8 and compatible SoCs
      - amlogic,meson-gxbb-i2c # GXBB and compatible SoCs
      - amlogic,meson-axg-i2c # AXG and compatible SoCs

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks

unevaluatedProperties: false

examples:
  - |
    i2c@c8100500 {
        compatible = "amlogic,meson6-i2c";
        reg = <0xc8100500 0x20>;
        interrupts = <92>;
        clocks = <&clk81>;
        #address-cells = <1>;
        #size-cells = <0>;

        eeprom@52 {
            compatible = "atmel,24c32";
            reg = <0x52>;
        };
    };
