# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---

$id: http://devicetree.org/schemas/i2c/google,cros-ec-i2c-tunnel.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: I2C bus that tunnels through the ChromeOS EC (cros-ec)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  On some ChromeOS board designs we've got a connection to the EC
  (embedded controller) but no direct connection to some devices on the
  other side of the EC (like a battery and PMIC).  To get access to
  those devices we need to tunnel our i2c commands through the EC.

  The node for this device should be under a cros-ec node like
  google,cros-ec-spi or google,cros-ec-i2c.

allOf:
  - $ref: i2c-controller.yaml#

properties:
  compatible:
    const: google,cros-ec-i2c-tunnel

  google,remote-bus:
    description: The EC bus we'd like to talk to.
    $ref: /schemas/types.yaml#/definitions/uint32

required:
  - compatible
  - google,remote-bus

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        cros-ec@0 {
            compatible = "google,cros-ec-spi";
            reg = <0>;
            spi-max-frequency = <5000000>;
            interrupts = <99 0>;

            i2c-tunnel {
                compatible = "google,cros-ec-i2c-tunnel";
                #address-cells = <1>;
                #size-cells = <0>;

                google,remote-bus = <0>;

                battery: sbs-battery@b {
                    compatible = "sbs,sbs-battery";
                    reg = <0xb>;
                    sbs,poll-retry-count = <1>;
                };
            };
        };
    };
