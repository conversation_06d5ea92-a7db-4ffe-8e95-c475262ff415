# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/st,nomadik-i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ST Microelectronics Nomadik I2C

description: The Nomadik I2C host controller began its life in the ST
  Microelectronics STn8800 SoC, and was then inherited into STn8810 and
  STn8815. It was part of the prototype STn8500 which then became ST-Ericsson
  DB8500 after the merge of these two companies wireless divisions.

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

# Need a custom select here or 'arm,primecell' will match on lots of nodes
select:
  properties:
    compatible:
      contains:
        enum:
          - st,nomadik-i2c
  required:
    - compatible

properties:
  compatible:
    oneOf:
      # The variant found in STn8815
      - items:
          - const: st,nomadik-i2c
          - const: arm,primecell
      # The variant found in DB8500
      - items:
          - const: ster<PERSON><PERSON>,db8500-i2c
          - const: st,nomadik-i2c
          - const: arm,primecell

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 2

  clock-names:
    oneOf:
      # Clock name in STn8815
      - items:
          - const: mclk
          - const: apb_pclk
      # Clock name in DB8500
      - items:
          - const: i2cclk
          - const: apb_pclk

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  clock-frequency:
    minimum: 1
    maximum: 400000

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/reset/stericsson,db8500-prcc-reset.h>
    #include <dt-bindings/arm/ux500_pm_domains.h>
    i2c@80004000 {
      compatible = "stericsson,db8500-i2c", "st,nomadik-i2c", "arm,primecell";
      reg = <0x80004000 0x1000>;
      interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;

      #address-cells = <1>;
      #size-cells = <0>;

      clock-frequency = <400000>;
      clocks = <&prcc_kclk 3 3>, <&prcc_pclk 3 3>;
      clock-names = "i2cclk", "apb_pclk";
      power-domains = <&pm_domains DOMAIN_VAPE>;
      resets = <&prcc_reset DB8500_PRCC_3 DB8500_PRCC_3_RESET_I2C0>;
    };

    i2c@101f8000 {
      compatible = "st,nomadik-i2c", "arm,primecell";
      reg = <0x101f8000 0x1000>;
      interrupt-parent = <&vica>;
      interrupts = <20>;
      clock-frequency = <100000>;
      #address-cells = <1>;
      #size-cells = <0>;
      clocks = <&i2c0clk>, <&pclki2c0>;
      clock-names = "mclk", "apb_pclk";
    };

...
