# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/i2c-mux-pca954x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP PCA954x I2C and compatible bus switches

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The NXP PCA954x and compatible devices are I2C bus
  multiplexer/switches that share the same functionality
  and register layout.
  The devices usually have 4 or 8 child buses, which are
  attached to the parent bus by using the SMBus "Send Byte"
  command.

properties:
  compatible:
    oneOf:
      - enum:
          - maxim,max7356
          - maxim,max7357
          - maxim,max7358
          - maxim,max7367
          - maxim,max7368
          - maxim,max7369
          - nxp,pca9540
          - nxp,pca9542
          - nxp,pca9543
          - nxp,pca9544
          - nxp,pca9545
          - nxp,pca9546
          - nxp,pca9547
          - nxp,pca9548
          - nxp,pca9846
          - nxp,pca9847
          - nxp,pca9848
          - nxp,pca9849
      - items:
          - const: nxp,pca9646
          - const: nxp,pca9546

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  "#interrupt-cells":
    const: 2

  interrupt-controller: true

  reset-gpios:
    maxItems: 1

  i2c-mux-idle-disconnect:
    type: boolean
    description: Forces mux to disconnect all children in idle state. This is
      necessary for example, if there are several multiplexers on the bus and
      the devices behind them use same I2C addresses.

  idle-state:
    description: if present, overrides i2c-mux-idle-disconnect
    $ref: /schemas/mux/mux-controller.yaml#/properties/idle-state

  vdd-supply:
    description: A voltage regulator supplying power to the chip. On PCA9846
      the regulator supplies power to VDD2 (core logic) and optionally to VDD1.

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/i2c/i2c-mux.yaml#
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - maxim,max7367
                - maxim,max7369
                - nxp,pca9542
                - nxp,pca9543
                - nxp,pca9544
                - nxp,pca9545
    then:
      properties:
        interrupts: false
        "#interrupt-cells": false
        interrupt-controller: false

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        i2c-mux@74 {
            compatible = "nxp,pca9545";
            #address-cells = <1>;
            #size-cells = <0>;
            reg = <0x74>;

            vdd-supply = <&p3v3>;

            interrupt-parent = <&ipic>;
            interrupts = <17 IRQ_TYPE_LEVEL_LOW>;
            interrupt-controller;
            #interrupt-cells = <2>;

            i2c@2 {
                #address-cells = <1>;
                #size-cells = <0>;
                reg = <2>;

                eeprom@54 {
                    compatible = "atmel,24c08";
                    reg = <0x54>;
                };
            };

            i2c@4 {
                #address-cells = <1>;
                #size-cells = <0>;
                reg = <4>;

                rtc@51 {
                    compatible = "nxp,pcf8563";
                    reg = <0x51>;
                };
            };
        };
    };
...
