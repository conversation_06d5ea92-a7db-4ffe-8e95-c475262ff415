# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/renesas,riic.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/A and RZ/G2L I2C Bus Interface (RIIC)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    items:
      - enum:
          - renesas,riic-r7s72100   # RZ/A1H
          - renesas,riic-r7s9210    # RZ/A2M
          - renesas,riic-r9a07g043  # RZ/G2UL and RZ/Five
          - renesas,riic-r9a07g044  # RZ/G2{L,LC}
          - renesas,riic-r9a07g054  # RZ/V2L
      - const: renesas,riic-rz      # RZ/A or RZ/G2L

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: Transmit End Interrupt
      - description: Receive Data Full Interrupt
      - description: Transmit Data Empty Interrupt
      - description: Stop Condition Detection Interrupt
      - description: Start Condition Detection Interrupt
      - description: NACK Reception Interrupt
      - description: Arbitration-Lost Interrupt
      - description: Timeout Interrupt

  interrupt-names:
    items:
      - const: tei
      - const: ri
      - const: ti
      - const: spi
      - const: sti
      - const: naki
      - const: ali
      - const: tmoi

  clock-frequency:
    description:
      Desired I2C bus clock frequency in Hz. The absence of this property
      indicates the default frequency 100 kHz.

  clocks:
    maxItems: 1

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-frequency
  - power-domains
  - '#address-cells'
  - '#size-cells'

if:
  properties:
    compatible:
      contains:
        enum:
          - renesas,riic-r9a07g043
          - renesas,riic-r9a07g044
          - renesas,riic-r9a07g054
then:
  required:
    - resets

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/r7s72100-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    i2c0: i2c@fcfee000 {
            compatible = "renesas,riic-r7s72100", "renesas,riic-rz";
            reg = <0xfcfee000 0x44>;
            interrupts = <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 158 IRQ_TYPE_EDGE_RISING>,
                         <GIC_SPI 159 IRQ_TYPE_EDGE_RISING>,
                         <GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>;
            interrupt-names = "tei", "ri", "ti", "spi", "sti", "naki", "ali",
                              "tmoi";
            clocks = <&mstp9_clks R7S72100_CLK_I2C0>;
            clock-frequency = <100000>;
            power-domains = <&cpg_clocks>;
            #address-cells = <1>;
            #size-cells = <0>;
    };
