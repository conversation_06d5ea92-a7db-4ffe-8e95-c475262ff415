# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/aspeed,i2c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ASPEED I2C on the AST24XX, AST25XX, and AST26XX SoCs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#

properties:
  compatible:
    enum:
      - aspeed,ast2400-i2c-bus
      - aspeed,ast2500-i2c-bus
      - aspeed,ast2600-i2c-bus

  reg:
    minItems: 1
    items:
      - description: address offset and range of bus
      - description: address offset and range of bus buffer

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1
    description:
      root clock of bus, should reference the APB
      clock in the second cell

  resets:
    maxItems: 1

  bus-frequency:
    minimum: 500
    maximum: 4000000
    default: 100000
    description: frequency of the bus clock in Hz defaults to 100 kHz when not
      specified

  multi-master:
    type: boolean
    description:
      states that there is another master active on this bus

required:
  - reg
  - compatible
  - clocks
  - resets

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/aspeed-clock.h>
    i2c@40 {
      #address-cells = <1>;
      #size-cells = <0>;
      compatible = "aspeed,ast2500-i2c-bus";
      reg = <0x40 0x40>;
      clocks = <&syscon ASPEED_CLK_APB>;
      resets = <&syscon ASPEED_RESET_I2C>;
      bus-frequency = <100000>;
      interrupts = <0>;
      interrupt-parent = <&i2c_ic>;
    };
