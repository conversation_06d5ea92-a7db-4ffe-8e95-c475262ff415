# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/i2c/i2c-pxa.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON>l MMP I2C controller

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/i2c/i2c-controller.yaml#
  - if:
      not:
        required:
          - mrvl,i2c-polling
    then:
      required:
        - interrupts

properties:
  compatible:
    enum:
      - mrvl,mmp-twsi
      - mrvl,pxa-i2c
      - marvell,armada-3700-i2c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1

  resets:
    minItems: 1

  mrvl,i2c-polling:
    $ref: /schemas/types.yaml#/definitions/flag
    description: |
      Disable interrupt of i2c controller. Polling status register of i2c
      controller instead.

  mrvl,i2c-fast-mode:
    $ref: /schemas/types.yaml#/definitions/flag
    description: Enable fast mode of i2c controller.

unevaluatedProperties: false

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - '#address-cells'
  - '#size-cells'

examples:
  - |
    #include <dt-bindings/clock/marvell,mmp2.h>
    i2c@d4011000 {
        compatible = "mrvl,mmp-twsi";
        reg = <0xd4011000 0x1000>;
        interrupts = <7>;
        clocks = <&soc_clocks MMP2_CLK_TWSI1>;
        mrvl,i2c-fast-mode;
        #address-cells = <1>;
        #size-cells = <0>;
    };

...
