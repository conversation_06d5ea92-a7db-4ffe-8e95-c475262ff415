# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/interconnect/qcom,sc8280xp-rpmh.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm RPMh Network-On-Chip Interconnect on SC8280XP

maintainers:
  - <PERSON><PERSON><PERSON> <<PERSON><PERSON>@kernel.org>
  - <PERSON> <<EMAIL>>

description: |
  RPMh interconnect providers support system bandwidth requirements through
  RPMh hardware accelerators known as Bus Clock Manager (BCM).

  See also:: include/dt-bindings/interconnect/qcom,sc8280xp.h

properties:
  compatible:
    enum:
      - qcom,sc8280xp-aggre1-noc
      - qcom,sc8280xp-aggre2-noc
      - qcom,sc8280xp-clk-virt
      - qcom,sc8280xp-config-noc
      - qcom,sc8280xp-dc-noc
      - qcom,sc8280xp-gem-noc
      - qcom,sc8280xp-lpass-ag-noc
      - qcom,sc8280xp-mc-virt
      - qcom,sc8280xp-mmss-noc
      - qcom,sc8280xp-nspa-noc
      - qcom,sc8280xp-nspb-noc
      - qcom,sc8280xp-system-noc

required:
  - compatible

allOf:
  - $ref: qcom,rpmh-common.yaml#

unevaluatedProperties: false

examples:
  - |
    interconnect-0 {
        compatible = "qcom,sc8280xp-aggre1-noc";
        #interconnect-cells = <2>;
        qcom,bcm-voters = <&apps_bcm_voter>;
    };
