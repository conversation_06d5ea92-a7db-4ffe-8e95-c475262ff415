# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/interconnect/qcom,sm8450-rpmh.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm RPMh Network-On-Chip Interconnect on SM8450

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>
  - <PERSON> <<EMAIL>>

description: |
  RPMh interconnect providers support system bandwidth requirements through
  RPMh hardware accelerators known as Bus Clock Manager (BCM).

  See also:: include/dt-bindings/interconnect/qcom,sm8450.h

properties:
  compatible:
    enum:
      - qcom,sm8450-aggre1-noc
      - qcom,sm8450-aggre2-noc
      - qcom,sm8450-clk-virt
      - qcom,sm8450-config-noc
      - qcom,sm8450-gem-noc
      - qcom,sm8450-lpass-ag-noc
      - qcom,sm8450-mc-virt
      - qcom,sm8450-mmss-noc
      - qcom,sm8450-nsp-noc
      - qcom,sm8450-pcie-anoc
      - qcom,sm8450-system-noc

  reg:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 4

required:
  - compatible

allOf:
  - $ref: qcom,rpmh-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8450-clk-virt
              - qcom,sm8450-mc-virt
    then:
      properties:
        reg: false
    else:
      required:
        - reg

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8450-aggre1-noc
    then:
      properties:
        clocks:
          items:
            - description: aggre UFS PHY AXI clock
            - description: aggre USB3 PRIM AXI clock

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8450-aggre2-noc
    then:
      properties:
        clocks:
          items:
            - description: aggre-NOC PCIe 0 AXI clock
            - description: aggre-NOC PCIe 1 AXI clock
            - description: aggre UFS PHY AXI clock
            - description: RPMH CC IPA clock

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8450-aggre1-noc
              - qcom,sm8450-aggre2-noc
    then:
      required:
        - clocks
    else:
      properties:
        clocks: false

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sm8450.h>
    #include <dt-bindings/clock/qcom,rpmh.h>

    interconnect-0 {
        compatible = "qcom,sm8450-clk-virt";
        #interconnect-cells = <2>;
        qcom,bcm-voters = <&apps_bcm_voter>;
    };

    interconnect@1700000 {
        compatible = "qcom,sm8450-aggre2-noc";
        reg = <0x01700000 0x31080>;
        #interconnect-cells = <2>;
        qcom,bcm-voters = <&apps_bcm_voter>;
        clocks = <&gcc GCC_AGGRE_NOC_PCIE_0_AXI_CLK>,
                 <&gcc GCC_AGGRE_NOC_PCIE_1_AXI_CLK>,
                 <&gcc GCC_AGGRE_UFS_PHY_AXI_CLK>,
                 <&rpmhcc RPMH_IPA_CLK>;
    };
