# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/interconnect/qcom,sc7280-rpmh.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm RPMh Network-On-Chip Interconnect on SC7280

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>
  - <PERSON> <<EMAIL>>

description: |
  RPMh interconnect providers support system bandwidth requirements through
  RPMh hardware accelerators known as Bus Clock Manager (BCM).

  See also:: include/dt-bindings/interconnect/qcom,sc7280.h

properties:
  compatible:
    enum:
      - qcom,sc7280-aggre1-noc
      - qcom,sc7280-aggre2-noc
      - qcom,sc7280-clk-virt
      - qcom,sc7280-cnoc2
      - qcom,sc7280-cnoc3
      - qcom,sc7280-dc-noc
      - qcom,sc7280-gem-noc
      - qcom,sc7280-lpass-ag-noc
      - qcom,sc7280-mc-virt
      - qcom,sc7280-mmss-noc
      - qcom,sc7280-nsp-noc
      - qcom,sc7280-system-noc

  reg:
    maxItems: 1

required:
  - compatible

allOf:
  - $ref: qcom,rpmh-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc7280-clk-virt
    then:
      properties:
        reg: false
    else:
      required:
        - reg

unevaluatedProperties: false

examples:
  - |
    interconnect {
        compatible = "qcom,sc7280-clk-virt";
        #interconnect-cells = <2>;
        qcom,bcm-voters = <&apps_bcm_voter>;
    };

    interconnect@9100000 {
        reg = <0x9100000 0xe2200>;
        compatible = "qcom,sc7280-gem-noc";
        #interconnect-cells = <2>;
        qcom,bcm-voters = <&apps_bcm_voter>;
    };
