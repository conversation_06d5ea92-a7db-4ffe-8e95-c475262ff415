# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interconnect/qcom,rpmh.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm RPMh Network-On-Chip Interconnect

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
   RPMh interconnect providers support system bandwidth requirements through
   RPMh hardware accelerators known as Bus Clock Manager (BCM). The provider is
   able to communicate with the BCM through the Resource State Coordinator (RSC)
   associated with each execution environment. Provider nodes must point to at
   least one RPMh device child node pertaining to their RSC and each provider
   can map to multiple RPMh resources.

properties:
  reg:
    maxItems: 1

  compatible:
    enum:
      - qcom,sc7180-aggre1-noc
      - qcom,sc7180-aggre2-noc
      - qcom,sc7180-camnoc-virt
      - qcom,sc7180-compute-noc
      - qcom,sc7180-config-noc
      - qcom,sc7180-dc-noc
      - qcom,sc7180-gem-noc
      - qcom,sc7180-mc-virt
      - qcom,sc7180-mmss-noc
      - qcom,sc7180-npu-noc
      - qcom,sc7180-qup-virt
      - qcom,sc7180-system-noc
      - qcom,sc8180x-aggre1-noc
      - qcom,sc8180x-aggre2-noc
      - qcom,sc8180x-camnoc-virt
      - qcom,sc8180x-compute-noc
      - qcom,sc8180x-config-noc
      - qcom,sc8180x-dc-noc
      - qcom,sc8180x-gem-noc
      - qcom,sc8180x-mc-virt
      - qcom,sc8180x-mmss-noc
      - qcom,sc8180x-qup-virt
      - qcom,sc8180x-system-noc
      - qcom,sdm670-aggre1-noc
      - qcom,sdm670-aggre2-noc
      - qcom,sdm670-config-noc
      - qcom,sdm670-dc-noc
      - qcom,sdm670-gladiator-noc
      - qcom,sdm670-mem-noc
      - qcom,sdm670-mmss-noc
      - qcom,sdm670-system-noc
      - qcom,sdm845-aggre1-noc
      - qcom,sdm845-aggre2-noc
      - qcom,sdm845-config-noc
      - qcom,sdm845-dc-noc
      - qcom,sdm845-gladiator-noc
      - qcom,sdm845-mem-noc
      - qcom,sdm845-mmss-noc
      - qcom,sdm845-system-noc
      - qcom,sdx55-mc-virt
      - qcom,sdx55-mem-noc
      - qcom,sdx55-system-noc
      - qcom,sdx65-mc-virt
      - qcom,sdx65-mem-noc
      - qcom,sdx65-system-noc
      - qcom,sm8150-aggre1-noc
      - qcom,sm8150-aggre2-noc
      - qcom,sm8150-camnoc-noc
      - qcom,sm8150-compute-noc
      - qcom,sm8150-config-noc
      - qcom,sm8150-dc-noc
      - qcom,sm8150-gem-noc
      - qcom,sm8150-mc-virt
      - qcom,sm8150-mmss-noc
      - qcom,sm8150-system-noc
      - qcom,sm8250-aggre1-noc
      - qcom,sm8250-aggre2-noc
      - qcom,sm8250-compute-noc
      - qcom,sm8250-config-noc
      - qcom,sm8250-dc-noc
      - qcom,sm8250-gem-noc
      - qcom,sm8250-mc-virt
      - qcom,sm8250-mmss-noc
      - qcom,sm8250-npu-noc
      - qcom,sm8250-qup-virt
      - qcom,sm8250-system-noc
      - qcom,sm8350-aggre1-noc
      - qcom,sm8350-aggre2-noc
      - qcom,sm8350-config-noc
      - qcom,sm8350-dc-noc
      - qcom,sm8350-gem-noc
      - qcom,sm8350-lpass-ag-noc
      - qcom,sm8350-mc-virt
      - qcom,sm8350-mmss-noc
      - qcom,sm8350-compute-noc
      - qcom,sm8350-system-noc

  '#interconnect-cells': true

required:
  - compatible

allOf:
  - $ref: qcom,rpmh-common.yaml#
  - if:
      not:
        properties:
          compatible:
            enum:
              - qcom,sm8250-qup-virt
    then:
      required:
        - reg


unevaluatedProperties: false

examples:
  - |
      #include <dt-bindings/interconnect/qcom,sdm845.h>

      mem_noc: interconnect@1380000 {
             compatible = "qcom,sdm845-mem-noc";
             reg = <0x01380000 0x27200>;
             #interconnect-cells = <1>;
             qcom,bcm-voters = <&apps_bcm_voter>;
      };

      mmss_noc: interconnect@1740000 {
             compatible = "qcom,sdm845-mmss-noc";
             reg = <0x01740000 0x1c1000>;
             #interconnect-cells = <1>;
             qcom,bcm-voter-names = "apps", "disp";
             qcom,bcm-voters = <&apps_bcm_voter>, <&disp_bcm_voter>;
      };
