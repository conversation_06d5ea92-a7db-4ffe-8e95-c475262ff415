# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/interconnect/qcom,msm8998-bwmon.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Interconnect Bandwidth Monitor

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <krz<PERSON><PERSON>tof.kozlows<PERSON>@linaro.org>

description: |
  Bandwidth Monitor measures current throughput on buses between various NoC
  fabrics and provides information when it crosses configured thresholds.

  Certain SoCs might have more than one Bandwidth Monitors, for example on SDM845::
   - Measuring the bandwidth between CPUs and Last Level Cache Controller -
     called just BWMON,
   - Measuring the bandwidth between Last Level Cache Controller and memory
     (DDR) - called LLCC BWMON.

properties:
  compatible:
    oneOf:
      - const: qcom,msm8998-bwmon       # BWMON v4
      - items:
          - enum:
              - qcom,sc7180-cpu-bwmon
              - qcom,sc7280-cpu-bwmon
              - qcom,sc8280xp-cpu-bwmon
              - qcom,sdm845-cpu-bwmon
              - qcom,sm6350-llcc-bwmon
              - qcom,sm8250-cpu-bwmon
              - qcom,sm8550-cpu-bwmon
          - const: qcom,sdm845-bwmon    # BWMON v4, unified register space
      - items:
          - enum:
              - qcom,sc7180-llcc-bwmon
              - qcom,sc8280xp-llcc-bwmon
              - qcom,sm6350-cpu-bwmon
              - qcom,sm8250-llcc-bwmon
              - qcom,sm8550-llcc-bwmon
          - const: qcom,sc7280-llcc-bwmon
      - const: qcom,sc7280-llcc-bwmon   # BWMON v5
      - const: qcom,sdm845-llcc-bwmon   # BWMON v5

  interconnects:
    maxItems: 1

  interrupts:
    maxItems: 1

  operating-points-v2: true
  opp-table:
    type: object

  reg:
    # BWMON v5 uses one register address space, v1-v4 use one or two.
    minItems: 1
    maxItems: 2

  reg-names:
    minItems: 1
    maxItems: 2

required:
  - compatible
  - interconnects
  - interrupts
  - operating-points-v2
  - opp-table
  - reg

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          const: qcom,msm8998-bwmon
    then:
      properties:
        reg:
          minItems: 2

        reg-names:
          items:
            - const: monitor
            - const: global

    else:
      properties:
        reg:
          maxItems: 1

        reg-names:
          maxItems: 1

examples:
  - |
    #include <dt-bindings/interconnect/qcom,sdm845.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    pmu@1436400 {
        compatible = "qcom,sdm845-cpu-bwmon", "qcom,sdm845-bwmon";
        reg = <0x01436400 0x600>;
        interrupts = <GIC_SPI 581 IRQ_TYPE_LEVEL_HIGH>;
        interconnects = <&gladiator_noc MASTER_APPSS_PROC 3 &mem_noc SLAVE_LLCC 3>;

        operating-points-v2 = <&cpu_bwmon_opp_table>;

        cpu_bwmon_opp_table: opp-table {
            compatible = "operating-points-v2";
            opp-0 {
                opp-peak-kBps = <4800000>;
            };
            opp-1 {
                opp-peak-kBps = <9216000>;
            };
            opp-2 {
                opp-peak-kBps = <15052800>;
            };
            opp-3 {
                opp-peak-kBps = <20889600>;
            };
            opp-4 {
                opp-peak-kBps = <25497600>;
            };
        };
    };
