# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/interconnect/qcom,rpm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm RPM Network-On-Chip Interconnect

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  RPM interconnect providers support system bandwidth requirements through
  RPM processor. The provider is able to communicate with the RPM through
  the RPM shared memory device.

properties:
  reg:
    maxItems: 1

  compatible:
    enum:
      - qcom,msm8916-bimc
      - qcom,msm8916-pcnoc
      - qcom,msm8916-snoc
      - qcom,msm8939-bimc
      - qcom,msm8939-pcnoc
      - qcom,msm8939-snoc
      - qcom,msm8996-a0noc
      - qcom,msm8996-a1noc
      - qcom,msm8996-a2noc
      - qcom,msm8996-bimc
      - qcom,msm8996-cnoc
      - qcom,msm8996-mnoc
      - qcom,msm8996-pnoc
      - qcom,msm8996-snoc
      - qcom,qcs404-bimc
      - qcom,qcs404-pcnoc
      - qcom,qcs404-snoc
      - qcom,sdm660-a2noc
      - qcom,sdm660-bimc
      - qcom,sdm660-cnoc
      - qcom,sdm660-gnoc
      - qcom,sdm660-mnoc
      - qcom,sdm660-snoc

  '#interconnect-cells':
    description: |
      Value: <1> is one cell in an interconnect specifier for the
      interconnect node id, <2> requires the interconnect node id and an
      extra path tag.
    enum: [ 1, 2 ]

  clocks:
    minItems: 2
    maxItems: 7

  clock-names:
    minItems: 2
    maxItems: 7

  power-domains:
    maxItems: 1

# Child node's properties
patternProperties:
  '^interconnect-[a-z0-9]+$':
    type: object
    additionalProperties: false
    description:
      snoc-mm is a child of snoc, sharing snoc's register address space.

    properties:
      compatible:
        enum:
          - qcom,msm8939-snoc-mm

      '#interconnect-cells':
        const: 1

      clock-names:
        items:
          - const: bus
          - const: bus_a

      clocks:
        items:
          - description: Bus Clock
          - description: Bus A Clock

    required:
      - compatible
      - '#interconnect-cells'
      - clock-names
      - clocks

required:
  - compatible
  - reg
  - '#interconnect-cells'
  - clock-names
  - clocks

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8916-bimc
              - qcom,msm8916-pcnoc
              - qcom,msm8916-snoc
              - qcom,msm8939-bimc
              - qcom,msm8939-pcnoc
              - qcom,msm8939-snoc
              - qcom,msm8996-a1noc
              - qcom,msm8996-bimc
              - qcom,msm8996-cnoc
              - qcom,msm8996-pnoc
              - qcom,msm8996-snoc
              - qcom,qcs404-bimc
              - qcom,qcs404-pcnoc
              - qcom,qcs404-snoc
              - qcom,sdm660-bimc
              - qcom,sdm660-cnoc
              - qcom,sdm660-gnoc
              - qcom,sdm660-snoc

    then:
      properties:
        clock-names:
          items:
            - const: bus
            - const: bus_a

        clocks:
          items:
            - description: Bus Clock
            - description: Bus A Clock

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8996-mnoc
              - qcom,sdm660-mnoc

    then:
      properties:
        clock-names:
          items:
            - const: bus
            - const: bus_a
            - const: iface

        clocks:
          items:
            - description: Bus Clock.
            - description: Bus A Clock.
            - description: CPU-NoC High-performance Bus Clock.

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8996-a0noc

    then:
      properties:
        clock-names:
          items:
            - const: aggre0_snoc_axi
            - const: aggre0_cnoc_ahb
            - const: aggre0_noc_mpu_cfg

        clocks:
          items:
            - description: Aggregate0 System NoC AXI Clock.
            - description: Aggregate0 Config NoC AHB Clock.
            - description: Aggregate0 NoC MPU Clock.

      required:
        - power-domains

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8996-a2noc

    then:
      properties:
        clock-names:
          items:
            - const: bus
            - const: bus_a
            - const: aggre2_ufs_axi
            - const: ufs_axi

        clocks:
          items:
            - description: Bus Clock
            - description: Bus A Clock
            - description: Aggregate2 NoC UFS AXI Clock
            - description: UFS AXI Clock

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdm660-a2noc

    then:
      properties:
        clock-names:
          items:
            - const: bus
            - const: bus_a
            - const: ipa
            - const: ufs_axi
            - const: aggre2_ufs_axi
            - const: aggre2_usb3_axi
            - const: cfg_noc_usb2_axi

        clocks:
          items:
            - description: Bus Clock.
            - description: Bus A Clock.
            - description: IPA Clock.
            - description: UFS AXI Clock.
            - description: Aggregate2 UFS AXI Clock.
            - description: Aggregate2 USB3 AXI Clock.
            - description: Config NoC USB2 AXI Clock.

  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - qcom,msm8939-snoc
    then:
      patternProperties:
        '^interconnect-[a-z0-9]+$': false

examples:
  - |
      #include <dt-bindings/clock/qcom,rpmcc.h>

      bimc: interconnect@400000 {
              compatible = "qcom,msm8916-bimc";
              reg = <0x00400000 0x62000>;
              #interconnect-cells = <1>;
              clock-names = "bus", "bus_a";
              clocks = <&rpmcc RPM_SMD_BIMC_CLK>,
                       <&rpmcc RPM_SMD_BIMC_A_CLK>;
      };

      pcnoc: interconnect@500000 {
              compatible = "qcom,msm8916-pcnoc";
              reg = <0x00500000 0x11000>;
              #interconnect-cells = <1>;
              clock-names = "bus", "bus_a";
              clocks = <&rpmcc RPM_SMD_PCNOC_CLK>,
                       <&rpmcc RPM_SMD_PCNOC_A_CLK>;
      };

      snoc: interconnect@580000 {
              compatible = "qcom,msm8916-snoc";
              reg = <0x00580000 0x14000>;
              #interconnect-cells = <1>;
              clock-names = "bus", "bus_a";
              clocks = <&rpmcc RPM_SMD_SNOC_CLK>,
                       <&rpmcc RPM_SMD_SNOC_A_CLK>;
      };
