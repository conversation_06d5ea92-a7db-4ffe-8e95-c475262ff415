# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/ti,ads1015.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI ADS1015/ADS1115 4 channel I2C analog to digital converter

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Datasheet at: https://www.ti.com/lit/gpn/ads1015
  Supports both single ended and differential channels.

properties:
  compatible:
    enum:
      - ti,ads1015
      - ti,ads1115
      - ti,tla2024

  reg:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - "#address-cells"
  - "#size-cells"

additionalProperties: false

patternProperties:
  "^channel@[0-7]+$":
    type: object
    additionalProperties: false
    description:
      Child nodes needed for each channel that the platform uses.

    properties:
      reg:
        description: |
          0: Voltage over AIN0 and AIN1.
          1: Voltage over AIN0 and AIN3.
          2: Voltage over AIN1 and AIN3.
          3: Voltage over AIN2 and AIN3.
          4: Voltage over AIN0 and GND.
          5: Voltage over AIN1 and GND.
          6: Voltage over AIN2 and GND.
          7: Voltage over AIN3 and GND.
        items:
          - minimum: 0
            maximum: 7

      ti,gain:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 5
        description: |
          pga is the programmable gain amplifier (values are full scale)
          0: +/- 6.144 V
          1: +/- 4.096 V
          2: +/- 2.048 V (default)
          3: +/- 1.024 V
          4: +/- 0.512 V
          5: +/- 0.256 V

      ti,datarate:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 7
        description: |
          Data acquisition rate in samples per second for ADS1015, TLA2024
          0: 128
          1: 250
          2: 490
          3: 920
          4: 1600 (default)
          5: 2400
          6: 3300
          7: 3300

          Data acquisition rate in samples per second for ADS1115
          0: 8
          1: 16
          2: 32
          3: 64
          4: 128 (default)
          5: 250
          6: 475
          7: 860

    required:
      - reg

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@49 {
            compatible = "ti,ads1015";
            reg = <0x49>;
            #address-cells = <1>;
            #size-cells = <0>;
            channel@0 {
                reg = <0>;
            };
            channel@4 {
                reg = <4>;
                ti,gain = <3>;
                ti,datarate = <5>;
            };
        };
    };
...
