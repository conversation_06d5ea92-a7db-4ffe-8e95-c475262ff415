# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IIO Common Properties for ADC Channels

maintainers:
  - <PERSON> <<EMAIL>>

description:
  A few properties are defined in a common way ADC channels.

properties:
  $nodename:
    pattern: "^channel(@[0-9a-f]+)?$"
    description:
      A channel index should match reg.

  reg:
    maxItems: 1

  label:
    $ref: /schemas/types.yaml#/definitions/string
    description: Unique name to identify which channel this is.

  bipolar:
    $ref: /schemas/types.yaml#/definitions/flag
    description: If provided, the channel is to be used in bipolar mode.

  diff-channels:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    maxItems: 2
    minItems: 2
    description:
      Many ADCs have dual Muxes to allow different input pins to be routed
      to both the positive and negative inputs of a differential ADC.
      The first value specifies the positive input pin, the second
      specifies the negative input pin.

  settling-time-us:
    description:
      Time between enabling the channel and first stable readings.

  oversampling-ratio:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Oversampling is used as replacement of or addition to the low-pass filter.
      In some cases, the desired filtering characteristics are a function the
      device design and can interact with other characteristics such as
      settling time.

additionalProperties: true
