# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7091r5.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7091R5 4-Channel 12-Bit ADC

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices AD7091R5 4-Channel 12-Bit ADC
  https://www.analog.com/media/en/technical-documentation/data-sheets/ad7091r-5.pdf

properties:
  compatible:
    enum:
      - adi,ad7091r5

  reg:
    maxItems: 1

  vref-supply:
    description:
      <PERSON>and<PERSON> to the vref power supply

  interrupts:
    maxItems: 1


required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@2f {
            compatible = "adi,ad7091r5";
            reg = <0x2f>;

            interrupts = <25 IRQ_TYPE_EDGE_FALLING>;
            interrupt-parent = <&gpio>;
        };
    };
...
