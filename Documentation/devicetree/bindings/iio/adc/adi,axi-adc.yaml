# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,axi-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AXI ADC IP core

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices Generic AXI ADC IP core for interfacing an ADC device
  with a high speed serial (JESD204B/C) or source synchronous parallel
  interface (LVDS/CMOS).
  Usually, some other interface type (i.e SPI) is used as a control
  interface for the actual ADC, while this IP core will interface
  to the data-lines of the ADC and handle the streaming of data into
  memory via DMA.

  https://wiki.analog.com/resources/fpga/docs/axi_adc_ip

properties:
  compatible:
    enum:
      - adi,axi-adc-10.0.a

  reg:
    maxItems: 1

  dmas:
    maxItems: 1

  dma-names:
    items:
      - const: rx

  adi,adc-dev:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      A reference to a the actual ADC to which this FPGA ADC interfaces to.

required:
  - compatible
  - dmas
  - reg
  - adi,adc-dev

additionalProperties: false

examples:
  - |
    axi-adc@44a00000 {
        compatible = "adi,axi-adc-10.0.a";
        reg = <0x44a00000 0x10000>;
        dmas = <&rx_dma 0>;
        dma-names = "rx";

        adi,adc-dev = <&spi_adc>;
    };
...
