# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/adi,ad7606.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices AD7606 Simultaneous Sampling ADC

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices AD7606 Simultaneous Sampling ADC
  https://www.analog.com/media/en/technical-documentation/data-sheets/ad7606_7606-6_7606-4.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD7606B.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/AD7616.pdf

properties:
  compatible:
    enum:
      - adi,ad7605-4
      - adi,ad7606-8
      - adi,ad7606-6
      - adi,ad7606-4
      - adi,ad7606b
      - adi,ad7616

  reg:
    maxItems: 1

  spi-cpha: true

  spi-cpol: true

  avcc-supply: true

  interrupts:
    maxItems: 1

  adi,conversion-start-gpios:
    description:
      Must be the device tree identifier of the CONVST pin.
      This logic input is used to initiate conversions on the analog
      input channels. As the line is active high, it should be marked
      GPIO_ACTIVE_HIGH.
    maxItems: 1

  reset-gpios:
    description:
      Must be the device tree identifier of the RESET pin. If specified,
      it will be asserted during driver probe. As the line is active high,
      it should be marked GPIO_ACTIVE_HIGH.
    maxItems: 1

  standby-gpios:
    description:
      Must be the device tree identifier of the STBY pin. This pin is used
      to place the AD7606 into one of two power-down modes, Standby mode or
      Shutdown mode. As the line is active low, it should be marked
      GPIO_ACTIVE_LOW.
    maxItems: 1

  adi,first-data-gpios:
    description:
      Must be the device tree identifier of the FRSTDATA pin.
      The FRSTDATA output indicates when the first channel, V1, is
      being read back on either the parallel, byte or serial interface.
      As the line is active high, it should be marked GPIO_ACTIVE_HIGH.
    maxItems: 1

  adi,range-gpios:
    description:
      Must be the device tree identifier of the RANGE pin. The polarity on
      this pin determines the input range of the analog input channels. If
      this pin is tied to a logic high, the analog input range is ±10V for
      all channels. If this pin is tied to a logic low, the analog input range
      is ±5V for all channels. As the line is active high, it should be marked
      GPIO_ACTIVE_HIGH.
    maxItems: 1

  adi,oversampling-ratio-gpios:
    description:
      Must be the device tree identifier of the over-sampling
      mode pins. As the line is active high, it should be marked
      GPIO_ACTIVE_HIGH.
    maxItems: 3

  adi,sw-mode:
    description:
      Software mode of operation, so far available only for ad7616 and ad7606b.
      It is enabled when all three oversampling mode pins are connected to
      high level. The device is configured by the corresponding registers. If the
      adi,oversampling-ratio-gpios property is defined, then the driver will set the
      oversampling gpios to high. Otherwise, it is assumed that the pins are hardwired
      to VDD.
    type: boolean

required:
  - compatible
  - reg
  - spi-cpha
  - avcc-supply
  - interrupts
  - adi,conversion-start-gpios

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adc@0 {
            compatible = "adi,ad7606-8";
            reg = <0>;
            spi-max-frequency = <1000000>;
            spi-cpol;
            spi-cpha;

            avcc-supply = <&adc_vref>;

            interrupts = <25 IRQ_TYPE_EDGE_FALLING>;
            interrupt-parent = <&gpio>;

            adi,conversion-start-gpios = <&gpio 17 GPIO_ACTIVE_HIGH>;
            reset-gpios = <&gpio 27 GPIO_ACTIVE_HIGH>;
            adi,first-data-gpios = <&gpio 22 GPIO_ACTIVE_HIGH>;
            adi,oversampling-ratio-gpios = <&gpio 18 GPIO_ACTIVE_HIGH>,
                                           <&gpio 23 GPIO_ACTIVE_HIGH>,
                                           <&gpio 26 GPIO_ACTIVE_HIGH>;
            standby-gpios = <&gpio 24 GPIO_ACTIVE_LOW>;
            adi,sw-mode;
        };
    };
...
