# SPDX-License-Identifier: GPL-2.0 OR BSD-2-Clause
# Copyright 2019 <PERSON> <<EMAIL>>
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/microchip,mcp3911.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip MCP3911 Dual channel analog front end (ADC)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Bindings for the Microchip MCP3911 Dual channel ADC device. Datasheet can be
  found here: https://ww1.microchip.com/downloads/en/DeviceDoc/20002286C.pdf

properties:
  compatible:
    enum:
      - microchip,mcp3911

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 20000000

  clocks:
    description: |
      Phandle and clock identifier for external sampling clock.
      If not specified, the internal crystal oscillator will be used.
    maxItems: 1

  interrupts:
    description: IRQ line of the ADC
    maxItems: 1

  microchip,data-ready-hiz:
    description:
      Data Ready Pin Inactive State Control
      true = The DR pin state is high-impedance
      false = The DR pin state is logic high
    type: boolean

  microchip,device-addr:
    description: Device address when multiple MCP3911 chips are present on the same SPI bus.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]
    default: 0

  vref-supply:
    description: |
      Phandle to the external reference voltage supply.
      If not specified, the internal voltage reference (1.2V) will be used.

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      adc@0 {
        compatible = "microchip,mcp3911";
        reg = <0>;
        interrupt-parent = <&gpio5>;
        interrupts = <15 2>;
        spi-max-frequency = <20000000>;
        microchip,device-addr = <0>;
        vref-supply = <&vref_reg>;
        clocks = <&xtal>;
      };
    };
