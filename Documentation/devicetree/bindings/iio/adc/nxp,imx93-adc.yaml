# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/nxp,imx93-adc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP iMX93 ADC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  The ADC on iMX93 is a 8-channel 12-bit 1MS/s ADC with 4 channels
  connected to pins. it support normal and inject mode, include
  One-Shot and Scan (continuous) conversions. Programmable DMA
  enables for each channel  Also this ADC contain alternate analog
  watchdog thresholds, select threshold through input ports. And
  also has Self-test logic and Software-initiated calibration.

properties:
  compatible:
    const: nxp,imx93-adc

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: WDGnL, watchdog threshold interrupt requests.
      - description: WDGnH, watchdog threshold interrupt requests.
      - description: normal conversion, include E<PERSON> (End of Conversion),
          ECH (End of Chain), JEOC (End of Injected Conversion) and
          JEC<PERSON> (End of injected Chain).
      - description: Self-testing Interrupts.

  clocks:
    maxItems: 1

  clock-names:
    const: ipg

  vref-supply:
    description:
      The reference voltage which used to establish channel scaling.

  "#io-channel-cells":
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - vref-supply
  - "#io-channel-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/clock/imx93-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    soc {
        #address-cells = <1>;
        #size-cells = <1>;
        adc@44530000 {
            compatible = "nxp,imx93-adc";
            reg = <0x44530000 0x10000>;
            interrupts = <GIC_SPI 217 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 218 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>,
                         <GIC_SPI 268 IRQ_TYPE_LEVEL_HIGH>;
            clocks = <&clk IMX93_CLK_ADC1_GATE>;
            clock-names = "ipg";
            vref-supply = <&reg_vref_1v8>;
            #io-channel-cells = <1>;
        };
    };
...
