# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/adc/allwinner,sun20i-d1-gpadc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner D1 General Purpose ADC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - allwinner,sun20i-d1-gpadc

  "#io-channel-cells":
    const: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  clocks:
    maxItems: 1

  interrupts:
    maxItems: 1

  reg:
    maxItems: 1

  resets:
    maxItems: 1

patternProperties:
  "^channel@[0-9a-f]+$":
    $ref: adc.yaml
    type: object
    description:
      Represents the internal channels of the ADC.

    properties:
      reg:
        items:
          minimum: 0
          maximum: 15

    required:
      - reg

    unevaluatedProperties: false

required:
  - "#io-channel-cells"
  - clocks
  - compatible
  - interrupts
  - reg
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/sun20i-d1-ccu.h>
    #include <dt-bindings/reset/sun20i-d1-ccu.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    gpadc: adc@2009000 {
        compatible = "allwinner,sun20i-d1-gpadc";
        reg = <0x2009000 0x400>;
        clocks = <&ccu CLK_BUS_GPADC>;
        resets = <&ccu RST_BUS_GPADC>;
        interrupts = <73 IRQ_TYPE_LEVEL_HIGH>;
        #io-channel-cells = <1>;

        #address-cells = <1>;
        #size-cells = <0>;

        channel@0 {
            reg = <0>;
        };

        channel@1 {
            reg = <1>;
        };
    };
...
