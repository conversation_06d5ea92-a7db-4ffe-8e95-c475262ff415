# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/st,st-sensors.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics MEMS sensors

description: The STMicroelectronics sensor devices are pretty straight-forward
  I2C or SPI devices, all sharing the same device tree descriptions no matter
  what type of sensor it is.
  Note that whilst this covers many STMicro MEMs sensors, some more complex
  IMUs need their own bindings.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - description: STMicroelectronics Accelerometers
        enum:
          - st,h3lis331dl-accel
          - st,lis2de12
          - st,lis2dw12
          - st,lis2hh12
          - st,lis2dh12-accel
          - st,lis302dl
          - st,lis331dl-accel
          - st,lis331dlh-accel
          - st,lis3de
          - st,lis3dh-accel
          - st,lis3dhh
          - st,lis3l02dq
          - st,lis3lv02dl-accel
          - st,lng2dm-accel
          - st,lsm303agr-accel
          - st,lsm303c-accel
          - st,lsm303dl-accel
          - st,lsm303dlh-accel
          - st,lsm303dlhc-accel
          - st,lsm303dlm-accel
          - st,lsm330-accel
          - st,lsm330d-accel
          - st,lsm330dl-accel
          - st,lsm330dlc-accel
      - items:
          - const: st,iis328dq
          - const: st,h3lis331dl-accel
      - description: Silan Accelerometers
        enum:
          - silan,sc7a20
      - description: STMicroelectronics Gyroscopes
        enum:
          - st,l3g4200d-gyro
          - st,l3g4is-gyro
          - st,l3gd20-gyro
          - st,l3gd20h-gyro
          - st,lsm330-gyro
          - st,lsm330d-gyro
          - st,lsm330dl-gyro
          - st,lsm330dlc-gyro
          - st,lsm9ds0-gyro
      - description: STMicroelectronics Magnetometers
        enum:
          - st,lis2mdl
          - st,lis3mdl-magn
          - st,lsm303agr-magn
          - st,lsm303c-magn
          - st,lsm303dlh-magn
          - st,lsm303dlhc-magn
          - st,lsm303dlm-magn
          - st,lsm9ds1-magn
      - description: STMicroelectronics Pressure Sensors
        enum:
          - st,lps001wp-press
          - st,lps22df
          - st,lps22hb-press
          - st,lps22hh
          - st,lps25h-press
          - st,lps331ap-press
          - st,lps33hw
          - st,lps35hw
      - description: IMUs
        enum:
          - st,lsm303d-imu
          - st,lsm9ds0-imu
      - description: Deprecated bindings
        enum:
          - st,lis302dl-spi
          - st,lis3lv02d
        deprecated: true

  reg:
    maxItems: 1

  interrupts:
    description: interrupt line(s) connected to the DRDY line(s) and/or the
      Inertial interrupt lines INT1 and INT2 if these exist. This means up to
      three interrupts, and the DRDY must be the first one if it exists on
      the package. The trigger edge of the interrupts is sometimes software
      configurable in the hardware so the operating system should parse this
      flag and set up the trigger edge as indicated in the device tree.
    minItems: 1
    maxItems: 2

  vdd-supply: true
  vddio-supply: true

  st,drdy-int-pin:
    description: the pin on the package that will be used to signal
      "data ready" (valid values 1 or 2). This property is not configurable
      on all sensors.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2]

  drive-open-drain:
    $ref: /schemas/types.yaml#/definitions/flag
    description: the interrupt/data ready line will be configured
      as open drain, which is useful if several sensors share the same
      interrupt line. (This binding is taken from pinctrl.)

  mount-matrix:
    description: an optional 3x3 mounting rotation matrix.

allOf:
  - if:
      properties:
        compatible:
          enum:
            # These have no interrupts
            - st,lps001wp
    then:
      properties:
        interrupts: false
        st,drdy-int-pin: false
        drive-open-drain: false

  - if:
      properties:
        compatible:
          enum:
            # These have only DRDY
            - st,lis2mdl
            - st,lis3l02dq
            - st,lis3lv02dl-accel
            - st,lps22df
            - st,lps22hb-press
            - st,lps22hh
            - st,lps25h-press
            - st,lps33hw
            - st,lps35hw
            - st,lsm303agr-magn
            - st,lsm303dlh-magn
            - st,lsm303dlhc-magn
            - st,lsm303dlm-magn
    then:
      properties:
        interrupts:
          maxItems: 1
        st,drdy-int-pin: false

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      accelerometer@1c {
        compatible = "st,lis331dl-accel";
        reg = <0x1c>;
        st,drdy-int-pin = <1>;
        vdd-supply = <&ldo1>;
        vddio-supply = <&ldo2>;
        interrupt-parent = <&gpio>;
        interrupts = <18 IRQ_TYPE_EDGE_RISING>, <19 IRQ_TYPE_EDGE_RISING>;
      };
    };
    spi {
      #address-cells = <1>;
      #size-cells = <0>;
      num-cs = <1>;

      l3g4200d: gyroscope@0 {
        compatible = "st,l3g4200d-gyro";
        st,drdy-int-pin = <2>;
        reg = <0>;
        vdd-supply = <&vcc_io>;
        vddio-supply = <&vcc_io>;
      };
    };
...
