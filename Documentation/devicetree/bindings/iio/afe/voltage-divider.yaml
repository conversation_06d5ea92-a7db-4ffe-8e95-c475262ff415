# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/afe/voltage-divider.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Voltage divider

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  When an io-channel measures the midpoint of a voltage divider, the
  interesting voltage is often the voltage over the full resistance
  of the divider. This binding describes the voltage divider in such
  a circuit.

    Vin ----.
            |
         .-----.
         |  R  |
         '-----'
            |
            +---- Vout
            |
         .-----.
         | Rout|
         '-----'
            |
           GND


properties:
  compatible:
    const: voltage-divider

  io-channels:
    maxItems: 1
    description: |
      Channel node of a voltage io-channel.

  output-ohms:
    description:
      Resistance Rout over which the output voltage is measured. See full-ohms.

  full-ohms:
    description:
      Resistance R + Rout for the full divider. The io-channel is scaled by
      the Rout / (R + Rout) quotient.

required:
  - compatible
  - io-channels
  - output-ohms
  - full-ohms

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    /*
     * The system voltage is circa 12V, but divided down with a 22/222
     * voltage divider (R = 200 Ohms, Rout = 22 Ohms) and fed to an ADC.
     */
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        maxadc: adc@0 {
            compatible = "maxim,max1027";
            reg = <0>;
            #io-channel-cells = <1>;
            interrupt-parent = <&gpio5>;
            interrupts = <15 IRQ_TYPE_EDGE_RISING>;
            spi-max-frequency = <1000000>;
        };
    };
    sysv {
        compatible = "voltage-divider";
        io-channels = <&maxadc 1>;

        /* Scale the system voltage by 22/222 to fit the ADC range. */
        output-ohms = <22>;
        full-ohms = <222>; /* 200 + 22 */
    };
...
