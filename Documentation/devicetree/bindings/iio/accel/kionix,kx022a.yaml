# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/kionix,kx022a.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ROHM/Kionix KX022A Accelerometer

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  KX022A is a 3-axis accelerometer supporting +/- 2G, 4G, 8G and 16G ranges,
  output data-rates from 0.78Hz to 1600Hz and a hardware-fifo buffering.
  KX022A can be accessed either via I2C or SPI.

properties:
  compatible:
    const: kionix,kx022a

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 2

  interrupt-names:
    minItems: 1
    items:
      - enum: [INT1, INT2]
      - const: INT2

  vdd-supply: true
  io-vdd-supply: true

  mount-matrix:
    description: |
      an optional 3x3 mounting rotation matrix.

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;
        accel@1f {
            compatible = "kionix,kx022a";
            reg = <0x1f>;

            interrupt-parent = <&gpio1>;
            interrupts = <29 IRQ_TYPE_LEVEL_LOW>;
            interrupt-names = "INT1";

            io-vdd-supply = <&iovdd>;
            vdd-supply = <&vdd>;
        };
    };
