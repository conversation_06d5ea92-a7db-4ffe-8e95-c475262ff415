# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/accel/adi,adxl345.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADXL345/ADXL375 3-Axis Digital Accelerometers

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices ADXL345/ADXL375 3-Axis Digital Accelerometers that supports
  both I2C & SPI interfaces.
    https://www.analog.com/en/products/mems/accelerometers/adxl345.html
    https://www.analog.com/en/products/sensors-mems/accelerometers/adxl375.html

properties:
  compatible:
    oneOf:
      - items:
          - const: adi,adxl346
          - const: adi,adxl345
      - enum:
          - adi,adxl345
          - adi,adxl375

  reg:
    maxItems: 1

  spi-cpha: true

  spi-cpol: true

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        /* Example for a I2C device node */
        accelerometer@2a {
            compatible = "adi,adxl345";
            reg = <0x2a>;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        /* Example for a SPI device node */
        accelerometer@0 {
            compatible = "adi,adxl345";
            reg = <0>;
            spi-max-frequency = <5000000>;
            spi-cpol;
            spi-cpha;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
