# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/bosch,bmi160.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Bosch BMI160

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Inertial Measurement Unit with Accelerometer, Gyroscope and externally
  connectable Magnetometer
  https://www.bosch-sensortec.com/bst/products/all_products/bmi160

properties:
  compatible:
    const: bosch,bmi160

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  interrupt-names:
    enum:
      - INT1
      - INT2
    description: |
      set to "INT1" if INT1 pin should be used as interrupt input, set
      to "INT2" if INT2 pin should be used instead

  drive-open-drain:
    description: |
      set if the specified interrupt pin should be configured as
      open drain. If not set, defaults to push-pull.

  vdd-supply:
    description: provide VDD power to the sensor.

  vddio-supply:
    description: provide VDD IO power to the sensor.

  mount-matrix:
    description: an optional 3x3 mounting rotation matrix

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    // Example for I2C
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        bmi160@68 {
            compatible = "bosch,bmi160";
            reg = <0x68>;
            vdd-supply = <&pm8916_l17>;
            vddio-supply = <&pm8916_l6>;
            interrupt-parent = <&gpio4>;
            interrupts = <12 IRQ_TYPE_EDGE_RISING>;
            interrupt-names = "INT1";
            mount-matrix = "0", "1", "0",
                           "-1", "0", "0",
                           "0", "0", "1";
        };
    };
  - |
    // Example for SPI
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        bmi160@0 {
            compatible = "bosch,bmi160";
            reg = <0>;
            spi-max-frequency = <10000000>;
            interrupt-parent = <&gpio2>;
            interrupts = <12 IRQ_TYPE_EDGE_RISING>;
            interrupt-names = "INT2";
        };
    };
