# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/invensense,mpu6050.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: InvenSense MPU-6050 Six-Axis (Gyro + Accelerometer) MEMS MotionTracking Device

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  These devices support both I2C and SPI bus interfaces.

properties:
  compatible:
    oneOf:
      - enum:
          - invensense,iam20680
          - invensense,icm20608
          - invensense,icm20609
          - invensense,icm20689
          - invensense,icm20602
          - invensense,icm20690
          - invensense,mpu6000
          - invensense,mpu6050
          - invensense,mpu6500
          - invensense,mpu6515
          - invensense,mpu6880
          - invensense,mpu9150
          - invensense,mpu9250
          - invensense,mpu9255
      - items:
          - const: invensense,icm20600
          - const: invensense,icm20602
      - items:
          - const: invensense,icm20608d
          - const: invensense,icm20608

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vdd-supply: true
  vddio-supply: true

  mount-matrix: true

  i2c-gate:
    $ref: /schemas/i2c/i2c-controller.yaml
    unevaluatedProperties: false
    description: |
      These devices also support an auxiliary i2c bus via an i2c-gate.

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - invensense,mpu9150
                - invensense,mpu9250
                - invensense,mpu9255
    then:
      properties:
        i2c-gate: false

unevaluatedProperties: false

required:
  - compatible
  - reg
  - interrupts

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        imu@68 {
            compatible = "invensense,mpu9250";
            reg = <0x68>;
            interrupt-parent = <&gpio3>;
            interrupts = <21 IRQ_TYPE_LEVEL_HIGH>;
            mount-matrix = "-0.984807753012208",  /* x0 */
                           "0",                   /* y0 */
                           "-0.173648177666930",  /* z0 */
                           "0",                   /* x1 */
                           "-1",                  /* y1 */
                           "0",                   /* z1 */
                           "-0.173648177666930",  /* x2 */
                           "0",                   /* y2 */
                           "0.984807753012208";   /* z2 */
            i2c-gate {
                #address-cells = <1>;
                #size-cells = <0>;
                magnetometer@c {
                    compatible = "asahi-kasei,ak8975";
                    reg = <0x0c>;
                };
            };
        };
    };
...
