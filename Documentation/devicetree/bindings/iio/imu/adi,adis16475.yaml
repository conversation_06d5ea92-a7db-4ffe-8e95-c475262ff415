# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/adi,adis16475.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADIS16475 and similar IMUs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices ADIS16475 and similar IMUs
  https://www.analog.com/media/en/technical-documentation/data-sheets/ADIS16475.pdf

properties:
  compatible:
    enum:
      - adi,adis16475-1
      - adi,adis16475-2
      - adi,adis16475-3
      - adi,adis16477-1
      - adi,adis16477-2
      - adi,adis16477-3
      - adi,adis16470
      - adi,adis16465-1
      - adi,adis16465-2
      - adi,adis16465-3
      - adi,adis16467-1
      - adi,adis16467-2
      - adi,adis16467-3
      - adi,adis16500
      - adi,adis16505-1
      - adi,adis16505-2
      - adi,adis16505-3
      - adi,adis16507-1
      - adi,adis16507-2
      - adi,adis16507-3

  reg:
    maxItems: 1

  spi-cpha: true

  spi-cpol: true

  spi-max-frequency:
    maximum: 2000000

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  reset-gpios:
    description:
      Must be the device tree identifier of the RESET pin. If specified,
      it will be asserted during driver probe. As the line is active low,
      it should be marked GPIO_ACTIVE_LOW.
    maxItems: 1

  adi,sync-mode:
    description:
      Configures the device SYNC pin. The following modes are supported
      0 - output_sync
      1 - direct_sync
      2 - scaled_sync
      3 - pulse_sync
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 3

required:
  - compatible
  - reg
  - interrupts
  - spi-cpha
  - spi-cpol

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,adis16500
              - adi,adis16505-1
              - adi,adis16505-2
              - adi,adis16505-3
              - adi,adis16507-1
              - adi,adis16507-2
              - adi,adis16507-3

    then:
      properties:
        adi,sync-mode:
          minimum: 0
          maximum: 2

  - if:
      properties:
        adi,sync-mode:
          enum: [1, 2, 3]

    then:
      dependencies:
        adi,sync-mode: [ clocks ]

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        adis16475: adis16475-3@0 {
            compatible = "adi,adis16475-3";
            reg = <0>;
            spi-cpha;
            spi-cpol;
            spi-max-frequency = <2000000>;
            interrupts = <4 IRQ_TYPE_EDGE_RISING>;
            interrupt-parent = <&gpio>;
        };
    };
...
