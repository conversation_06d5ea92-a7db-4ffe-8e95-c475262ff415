# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/imu/adi,adis16460.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADIS16460 and similar IMUs

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices ADIS16460 and similar IMUs
  https://www.analog.com/media/en/technical-documentation/data-sheets/ADIS16460.pdf

properties:
  compatible:
    enum:
      - adi,adis16460

  reg:
    maxItems: 1

  spi-cpha: true

  spi-cpol: true

  interrupts:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        imu@0 {
            compatible = "adi,adis16460";
            reg = <0>;
            spi-max-frequency = <5000000>;
            spi-cpol;
            spi-cpha;
            interrupt-parent = <&gpio0>;
            interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
        };
    };
