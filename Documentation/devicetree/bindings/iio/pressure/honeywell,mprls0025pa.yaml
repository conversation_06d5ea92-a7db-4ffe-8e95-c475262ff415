# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/pressure/honeywell,mprls0025pa.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Honeywell mprls0025pa pressure sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Honeywell pressure sensor of model mprls0025pa.

  This sensor has an I2C and SPI interface. Only the I2C interface is
  implemented.

  There are many models with different pressure ranges available. The vendor
  calls them "mpr series". All of them have the identical programming model and
  differ in the pressure range, unit and transfer function.

  To support different models one need to specify the pressure range as well as
  the transfer function. Pressure range needs to be converted from its unit to
  pascal.

  The transfer function defines the ranges of numerical values delivered by the
  sensor. The minimal range value stands for the minimum pressure and the
  maximum value also for the maximum pressure with linear relation inside the
  range.

  Specifications about the devices can be found at:
    https://prod-edam.honeywell.com/content/dam/honeywell-edam/sps/siot/en-us/
      products/sensors/pressure-sensors/board-mount-pressure-sensors/
      micropressure-mpr-series/documents/
      sps-siot-mpr-series-datasheet-32332628-ciid-172626.pdf

properties:
  compatible:
    const: honeywell,mprls0025pa

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  reset-gpios:
    description:
      Optional GPIO for resetting the device.
      If not present the device is not reset during the probe.
    maxItems: 1

  honeywell,pmin-pascal:
    description:
      Minimum pressure value the sensor can measure in pascal.
    $ref: /schemas/types.yaml#/definitions/uint32

  honeywell,pmax-pascal:
    description:
      Maximum pressure value the sensor can measure in pascal.
    $ref: /schemas/types.yaml#/definitions/uint32

  honeywell,transfer-function:
    description: |
      Transfer function which defines the range of valid values delivered by the
      sensor.
      1 - A, 10% to 90% of 2^24 (1677722 .. 15099494)
      2 - B, 2.5% to 22.5% of 2^24 (419430 .. 3774874)
      3 - C, 20% to 80% of 2^24 (3355443 .. 13421773)
    $ref: /schemas/types.yaml#/definitions/uint32

  vdd-supply:
    description: provide VDD power to the sensor.

required:
  - compatible
  - reg
  - honeywell,pmin-pascal
  - honeywell,pmax-pascal
  - honeywell,transfer-function
  - vdd-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        pressure@18 {
            compatible = "honeywell,mprls0025pa";
            reg = <0x18>;
            reset-gpios = <&gpio3 19 GPIO_ACTIVE_HIGH>;
            interrupt-parent = <&gpio3>;
            interrupts = <21 IRQ_TYPE_EDGE_FALLING>;
            honeywell,pmin-pascal = <0>;
            honeywell,pmax-pascal = <172369>;
            honeywell,transfer-function = <1>;
            vdd-supply = <&vcc_3v3>;
        };
    };
