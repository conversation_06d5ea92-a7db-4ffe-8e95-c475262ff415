# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/health/ti,afe4404.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments AFE4404 Heart rate and Pulse Oximeter

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: ti,afe4404

  reg:
    maxItems: 1

  tx-supply:
    description: Supply to transmitting LEDs.

  interrupts:
    maxItems: 1
    description: Connected to ADC_RDY pin.

  reset-gpios: true

additionalProperties: false

required:
  - compatible
  - reg

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        heart-mon@58 {
            compatible = "ti,afe4404";
            reg = <0x58>;
            tx-supply = <&vbat>;
            interrupt-parent = <&gpio1>;
            interrupts = <28 IRQ_TYPE_EDGE_RISING>;
            reset-gpios = <&gpio1 16 GPIO_ACTIVE_LOW>;
        };
    };
...
