# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/humidity/ti,hdc2010.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: HDC2010/HDC2080 humidity and temperature iio sensors

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Relative humidity and temperature sensors on I2C bus

  Datasheets are available at:
    http://www.ti.com/product/HDC2010/datasheet
    http://www.ti.com/product/HDC2080/datasheet

properties:
  compatible:
    enum:
      - ti,hdc2010
      - ti,hdc2080

  vdd-supply: true

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        humidity@40 {
            compatible = "ti,hdc2010";
            reg = <0x40>;
        };
    };
