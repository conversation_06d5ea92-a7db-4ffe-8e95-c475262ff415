# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/temperature/adi,ltc2983.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices LTC2983, LTC2986, LTM2985 Multi-sensor Temperature system

maintainers:
  - Nuno Sá <<EMAIL>>

description: |
  Analog Devices LTC2983, LTC2984, LTC2986, LTM2985 Multi-Sensor Digital
  Temperature Measurement Systems

  https://www.analog.com/media/en/technical-documentation/data-sheets/2983fc.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/2984fb.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/29861fa.pdf
  https://www.analog.com/media/en/technical-documentation/data-sheets/ltm2985.pdf

$defs:
  sensor-node:
    type: object
    description: Sensor node common constraints

    properties:
      reg:
        description:
          Channel number. Connects the sensor to the channel with this number
          of the device.
        minimum: 1
        maximum: 20

      adi,sensor-type:
        description: Type of sensor connected to the device.
        $ref: /schemas/types.yaml#/definitions/uint32

    required:
      - reg
      - adi,sensor-type


properties:
  compatible:
    oneOf:
      - enum:
          - adi,ltc2983
          - adi,ltc2986
          - adi,ltm2985
      - items:
          - const: adi,ltc2984
          - const: adi,ltc2983

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  adi,mux-delay-config-us:
    description: |
      Extra delay prior to each conversion, in addition to the internal 1ms
      delay, for the multiplexer to switch input configurations and
      excitation values.

      This property is supposed to be in microseconds, but to maintain
      compatibility, this value will be multiplied by 100 before usage.
    maximum: 255
    default: 0

  adi,filter-notch-freq:
    description:
      Notch frequency of the digital filter.
      0 - 50/60Hz rejection
      1 - 60Hz rejection
      2 - 50Hz rejection
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 2
    default: 0

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

patternProperties:
  "^thermocouple@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false

    description: Thermocouple sensor.

    properties:
      adi,sensor-type:
        description: |
          1 - Type J Thermocouple
          2 - Type K Thermocouple
          3 - Type E Thermocouple
          4 - Type N Thermocouple
          5 - Type R Thermocouple
          6 - Type S Thermocouple
          7 - Type T Thermocouple
          8 - Type B Thermocouple
          9 - Custom Thermocouple
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 1
        maximum: 9

      adi,single-ended:
        description: Whether the sensor is single-ended.
        type: boolean

      adi,sensor-oc-current-microamp:
        description: Pulsed current value applied during open-circuit detect.
        enum: [10, 100, 500, 1000]
        default: 10

      adi,cold-junction-handle:
        description:
          Sensor responsible for measuring the thermocouple cold junction
          temperature.
        $ref: /schemas/types.yaml#/definitions/phandle

      adi,custom-thermocouple:
        description:
          Used for digitizing custom thermocouples.
          See Page 59 of the datasheet.
        $ref: /schemas/types.yaml#/definitions/int64-matrix
        minItems: 3
        maxItems: 64
        items:
          items:
            - description: Voltage point in nV, signed.
            - description: Temperature point in uK.

    allOf:
      - if:
          properties:
            adi,sensor-type:
              const: 9
        then:
          required:
            - adi,custom-thermocouple

  "^diode@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false

    description: Diode sensor.

    properties:
      adi,sensor-type:
        description: Sensor type for diodes.
        $ref: /schemas/types.yaml#/definitions/uint32
        const: 28

      adi,single-ended:
        description: Whether the sensor is single-ended.
        type: boolean

      adi,three-conversion-cycles:
        description:
          Whether to use three conversion cycles to remove parasitic
          resistance between the device and the diode.
        type: boolean

      adi,average-on:
        description:
          Whether to use a running average of the diode temperature
          reading to reduce the noise when the diode is used as a cold
          junction temperature element on an isothermal block where
          temperatures change slowly.
        type: boolean

      adi,excitation-current-microamp:
        description:
          Magnitude of the 1l excitation current applied to the diode.
          4l excitation current will be 4 times this value, and 8l
          excitation current will be 8 times value.
        enum: [10, 20, 40, 80]
        default: 10

      adi,ideal-factor-value:
        description:
          Diode ideality factor.
          Set this property to 1000000 times the real value.
        $ref: /schemas/types.yaml#/definitions/uint32
        default: 0

  "^rtd@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false
    description: RTD sensor.

    properties:
      reg:
        minimum: 2
        maximum: 20

      adi,sensor-type:
        description: |
          10 - RTD PT-10
          11 - RTD PT-50
          12 - RTD PT-100
          13 - RTD PT-200
          14 - RTD PT-500
          15 - RTD PT-1000
          16 - RTD PT-1000 (0.00375)
          17 - RTD NI-120
          18 - RTD Custom
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 10
        maximum: 18

      adi,rsense-handle:
        description: Associated sense resistor sensor.
        $ref: /schemas/types.yaml#/definitions/phandle

      adi,number-of-wires:
        description:
          Number of wires used by the RTD.
          5 means 4 wires with Kelvin sense resistor.
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [2, 3, 4, 5]
        default: 2

      adi,rsense-share:
        description:
          Whether to enable sense resistor sharing, where one sense
          resistor is used by multiple sensors.
        type: boolean

      adi,excitation-current-microamp:
        description: Excitation current applied to the RTD.
        enum: [5, 10, 25, 50, 100, 250, 500, 1000]
        default: 5

      adi,rtd-curve:
        description: |
          RTD curve and the corresponding Callendar-VanDusen constants.
          0 - European
          1 - American
          2 - Japanese
          3 - ITS-90
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 3
        default: 0

      adi,custom-rtd:
        description:
          Used for digitizing custom RTDs.
          See Page 62 of the datasheet.
        $ref: /schemas/types.yaml#/definitions/uint64-matrix
        minItems: 3
        maxItems: 64
        items:
          items:
            - description: Resistance point in uOhms.
            - description: Temperature point in uK.

    required:
      - adi,rsense-handle

    allOf:
      - if:
          properties:
            adi,number-of-wires:
              const: 4
        then:
          properties:
            adi,current-rotate:
              description:
                Whether to enable excitation current rotation to automatically
                remove parasitic thermocouple effects.
              type: boolean

          dependencies:
            adi,current-rotate: [ "adi,rsense-share" ]

      - if:
          properties:
            adi,sensor-type:
              const: 18
        then:
          required:
            - adi,custom-rtd

  "^thermistor@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false
    description: Thermistor sensor.

    properties:
      adi,sensor-type:
        description:
          19 - Thermistor 44004/44033 2.252kohm at 25°C
          20 - Thermistor 44005/44030 3kohm at 25°C
          21 - Thermistor 44007/44034 5kohm at 25°C
          22 - Thermistor 44006/44031 10kohm at 25°C
          23 - Thermistor 44008/44032 30kohm at 25°C
          24 - Thermistor YSI 400 2.252kohm at 25°C
          25 - Thermistor Spectrum 1003k 1kohm
          26 - Thermistor Custom Steinhart-Hart
          27 - Custom Thermistor
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 19
        maximum: 27

      adi,rsense-handle:
        description: Associated sense resistor sensor.
        $ref: /schemas/types.yaml#/definitions/phandle

      adi,single-ended:
        description: Whether the sensor is single-ended.
        type: boolean

      adi,rsense-share:
        description:
          Whether to enable sense resistor sharing, where one sense
          resistor is used by multiple sensors.
        type: boolean

      adi,current-rotate:
        description:
          Whether to enable excitation current rotation to automatically
          remove parasitic thermocouple effects.
        type: boolean

      adi,excitation-current-nanoamp:
        description:
          Excitation current applied to the thermistor.
          0 sets the sensor in auto-range mode.
        enum: [0, 250, 500, 1000, 5000, 10000, 25000, 50000, 100000, 250000,
               500000, 1000000]
        default: 0

      adi,custom-thermistor:
        description:
          Used for digitizing custom thermistors.
          See Page 65 of the datasheet.
        $ref: /schemas/types.yaml#/definitions/uint64-matrix
        minItems: 3
        maxItems: 64
        items:
          items:
            - description: Resistance point in uOhms.
            - description: Temperature point in uK.

      adi,custom-steinhart:
        description:
          Steinhart-Hart coefficients in raw format, used for digitizing
          custom thermistors.
          See Page 68 of the datasheet.
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 6
        maxItems: 6

    required:
      - adi,rsense-handle

    dependencies:
      adi,current-rotate: [ "adi,rsense-share" ]

    allOf:
      - if:
          properties:
            adi,sensor-type:
              const: 26
        then:
          properties:
            adi,excitation-current-nanoamp:
              enum: [250, 500, 1000, 5000, 10000, 25000, 50000, 100000,
                     250000, 500000, 1000000]
              default: 1000
          required:
            - adi,custom-steinhart
      - if:
          properties:
            adi,sensor-type:
              const: 27
        then:
          properties:
            adi,excitation-current-nanoamp:
              enum: [250, 500, 1000, 5000, 10000, 25000, 50000, 100000,
                     250000, 500000, 1000000]
              default: 1000
          required:
            - adi,custom-thermistor

  "^adc@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false
    description: Direct ADC sensor.

    properties:
      adi,sensor-type:
        description: Sensor type for direct ADC sensors.
        $ref: /schemas/types.yaml#/definitions/uint32
        const: 30

      adi,single-ended:
        description: Whether the sensor is single-ended.
        type: boolean

  "^temp@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false
    description: Active analog temperature sensor.

    properties:
      adi,sensor-type:
        description: Sensor type for active analog temperature sensors.
        $ref: /schemas/types.yaml#/definitions/uint32
        const: 31

      adi,single-ended:
        description: Whether the sensor is single-ended.
        type: boolean

      adi,custom-temp:
        description:
          Used for digitizing active analog temperature sensors.
          See Page 67 of the LTM2985 datasheet.
        $ref: /schemas/types.yaml#/definitions/uint64-matrix
        minItems: 3
        maxItems: 64
        items:
          items:
            - description: Voltage point in nV, signed.
            - description: Temperature point in uK.

    required:
      - adi,custom-temp

  "^rsense@":
    $ref: '#/$defs/sensor-node'
    unevaluatedProperties: false
    description: Sense resistor sensor.

    properties:
      reg:
        minimum: 2
        maximum: 20

      adi,sensor-type:
        description: Sensor type sense resistor sensors.
        $ref: /schemas/types.yaml#/definitions/uint32
        const: 29

      adi,rsense-val-milli-ohms:
        description: Value of the sense resistor.

    required:
      - adi,rsense-val-milli-ohms

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - adi,ltc2983
              - adi,ltc2984
    then:
      patternProperties:
        "^temp@": false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        temperature-sensor@0 {
            compatible = "adi,ltc2983";
            reg = <0>;

            #address-cells = <1>;
            #size-cells = <0>;

            interrupts = <20 IRQ_TYPE_EDGE_RISING>;
            interrupt-parent = <&gpio>;

            thermocouple@18 {
                reg = <18>;
                adi,sensor-type = <8>; //Type B
                adi,sensor-oc-current-microamp = <10>;
                adi,cold-junction-handle = <&diode5>;
            };

            diode5: diode@5 {
                reg = <5>;
                adi,sensor-type = <28>;
            };

            rsense2: rsense@2 {
                reg = <2>;
                adi,sensor-type = <29>;
                adi,rsense-val-milli-ohms = <1200000>; //1.2Kohms
            };

            rtd@14 {
                reg = <14>;
                adi,sensor-type = <15>; //PT1000
                /*2-wire, internal gnd, no current rotation*/
                adi,number-of-wires = <2>;
                adi,rsense-share;
                adi,excitation-current-microamp = <500>;
                adi,rsense-handle = <&rsense2>;
            };

            adc@10 {
                reg = <10>;
                adi,sensor-type = <30>;
                adi,single-ended;
            };

            thermistor@12 {
                reg = <12>;
                adi,sensor-type = <26>; //Steinhart
                adi,rsense-handle = <&rsense2>;
                adi,custom-steinhart = <0x00f371ec 0x12345678
                                0x2c0f8733 0x10018c66 0xa0feaccd
                                0x90021d99>; //6 entries
            };

            thermocouple@20 {
                reg = <20>;
                adi,sensor-type = <9>; //custom thermocouple
                adi,single-ended;
                adi,custom-thermocouple =
                         /bits/ 64 <(-50220000) 0>,
                         /bits/ 64 <(-30200000) 99100000>,
                         /bits/ 64 <(-5300000) 135400000>,
                         /bits/ 64 <0 273150000>,
                         /bits/ 64 <40200000 361200000>,
                         /bits/ 64 <55300000 522100000>,
                         /bits/ 64 <88300000 720300000>,
                         /bits/ 64 <132200000 811200000>,
                         /bits/ 64 <188700000 922500000>,
                         /bits/ 64 <460400000 1000000000>; //10 pairs
            };
        };
    };
...
