# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/temperature/ti,tmp117.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI TMP117 - Digital temperature sensor with integrated NV memory

description: |
    TI TMP116/117 - Digital temperature sensor with integrated NV memory that
    supports I2C interface.
      https://www.ti.com/lit/gpn/tmp116
      https://www.ti.com/lit/gpn/tmp117

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - ti,tmp116
      - ti,tmp117

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        tmp117@48 {
             compatible = "ti,tmp117";
             reg = <0x48>;
        };
    };
