# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/frequency/adi,adf4377.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ADF4377 Microwave Wideband Synthesizer with Integrated VCO

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
   The ADF4377 is a high performance, ultralow jitter, dual output integer-N
   phased locked loop (PLL) with integrated voltage controlled oscillator (VCO)
   ideally suited for data converter and mixed signal front end (MxFE) clock
   applications.

   https://www.analog.com/en/products/adf4377.html

properties:
  compatible:
    enum:
      - adi,adf4377
      - adi,adf4378

  reg:
    maxItems: 1

  spi-max-frequency:
    maximum: 10000000

  clocks:
    maxItems: 1

  clock-names:
    description:
      External clock that provides reference input frequency.
    items:
      - const: ref_in

  chip-enable-gpios:
    description:
      GPIO that controls the Chip Enable Pin.
    maxItems: 1

  clk1-enable-gpios:
    description:
      GPIO that controls the Enable Clock 1 Output Buffer Pin.
    maxItems: 1

  clk2-enable-gpios:
    description:
      GPIO that controls the Enable Clock 2 Output Buffer Pin.
    maxItems: 1

  adi,muxout-select:
    description:
      On chip multiplexer output selection.
      high_z - MUXOUT Pin set to high-Z.
      lock_detect - MUXOUT Pin set to lock detector output.
      muxout_low - MUXOUT Pin set to low.
      f_div_rclk_2 - MUXOUT Pin set to fDIV_RCLK/2.
      f_div_nclk_2 - MUXOUT Pin set to fDIV_NCLK/2.
      muxout_high - MUXOUT Pin set to high.
    enum: [high_z, lock_detect, muxout_low, f_div_rclk_2, f_div_nclk_2, muxout_high]

required:
  - compatible
  - reg
  - clocks
  - clock-names

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

unevaluatedProperties: false

examples:
  - |
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        frequency@0 {
            compatible = "adi,adf4377";
            reg = <0>;
            spi-max-frequency = <10000000>;
            clocks = <&adf4377_ref_in>;
            clock-names = "ref_in";
        };
    };
...
