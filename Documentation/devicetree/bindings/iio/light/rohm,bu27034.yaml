# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/iio/light/rohm,bu27034.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ROHM BU27034 ambient light sensor

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  ROHM BU27034 is an ambient light sesnor with 3 channels and 3 photo diodes
  capable of detecting a very wide range of illuminance. Typical application
  is adjusting LCD and backlight power of TVs and mobile phones.
  https://fscdn.rohm.com/en/products/databook/datasheet/ic/sensor/light/bu27034nuc-e.pdf

properties:
  compatible:
    const: rohm,bu27034

  reg:
    maxItems: 1

  vdd-supply: true

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      light-sensor@38 {
        compatible = "rohm,bu27034";
        reg = <0x38>;
        vdd-supply = <&vdd>;
      };
    };

...
