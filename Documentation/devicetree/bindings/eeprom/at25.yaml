# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/eeprom/at25.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SPI EEPROMs or FRAMs compatible with Atmel's AT25

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    anyOf:
      - pattern: "^eeprom@[0-9a-f]{1,2}$"
      - pattern: "^fram@[0-9a-f]{1,2}$"

  # There are multiple known vendors who manufacture EEPROM chips compatible
  # with Atmel's AT25. The compatible string requires two items where the
  # 'vendor' and 'model' parts of the first are the actual chip and the second
  # item is fixed to "atmel,at25". Some existing bindings only have the
  # "atmel,at25" part and should be fixed by somebody who knows vendor and
  # product.
  compatible:
    oneOf:
      - items:
          - enum:
              - anvo,anv32e61w
              - atmel,at25256B
              - fujitsu,mb85rs1mt
              - fujitsu,mb85rs64
              - microchip,at25160bn
              - microchip,25lc040
              - st,m95m02
              - st,m95256
              - st,m95640
              - cypress,fm25

          - const: atmel,at25

      # Please don't use this alternative for new bindings.
      - items:
          - const: atmel,at25

  reg:
    maxItems: 1

  pagesize:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 65536, 131072]
    description:
      Size of the eeprom page. FRAMs don't have pages.

  size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Total eeprom size in bytes.

  address-width:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [ 8, 9, 16, 24 ]
    description:
      Number of address bits.
      For 9 bits, the MSB of the address is sent as bit 3 of the instruction
      byte, before the address byte.

  spi-cpha: true

  spi-cpol: true

  read-only:
    description:
      Disable writes to the eeprom.
    type: boolean

  wp-gpios:
    maxItems: 1
    description:
      GPIO to which the write-protect pin of the chip is connected.

  # Deprecated: at25,byte-len, at25,addr-mode, at25,page-size
  at25,byte-len:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Total eeprom size in bytes. Deprecated, use "size" property instead.
    deprecated: true

  at25,addr-mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Addr-mode flags, as defined in include/linux/spi/eeprom.h.
      Deprecated, use "address-width" property instead.
    deprecated: true

  at25,page-size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Size of the eeprom page. Deprecated, use "pagesize" property instead.
    deprecated: true

required:
  - compatible
  - reg
  - spi-max-frequency

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#
  - $ref: /schemas/nvmem/nvmem.yaml
  - if:
      properties:
        compatible:
          not:
            contains:
              const: cypress,fm25
    then:
      required:
        - pagesize
        - size
        - address-width

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        eeprom@0 {
            compatible = "st,m95256", "atmel,at25";
            reg = <0>;
            spi-max-frequency = <5000000>;
            spi-cpha;
            spi-cpol;
            wp-gpios = <&gpio1 3 0>;

            pagesize = <64>;
            size = <32768>;
            address-width = <16>;
        };

        fram@1 {
            compatible = "cypress,fm25", "atmel,at25";
            reg = <1>;
            spi-max-frequency = <40000000>;
        };
    };
