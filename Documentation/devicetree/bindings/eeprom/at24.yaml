# SPDX-License-Identifier: GPL-2.0-only
# Copyright 2019 BayLibre SAS
%YAML 1.2
---
$id: http://devicetree.org/schemas/eeprom/at24.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: I2C EEPROMs compatible with Atmel's AT24

maintainers:
  - <PERSON><PERSON><PERSON> <b<PERSON><PERSON><PERSON><PERSON>@baylibre.com>

allOf:
  - $ref: /schemas/nvmem/nvmem.yaml

select:
  properties:
    compatible:
      contains:
        pattern: "^atmel,(24(c|cs|mac)[0-9]+|spd)$"
  required:
    - compatible

properties:
  $nodename:
    pattern: "^eeprom@[0-9a-f]{1,2}$"

  # There are multiple known vendors who manufacture EEPROM chips compatible
  # with Atmel's AT24. The compatible string requires either a single item
  # if the memory comes from Atmel (in which case the vendor part must be
  # 'atmel') or two items with the same 'model' part where the vendor part of
  # the first one is the actual manufacturer and the second item is the
  # corresponding 'atmel,<model>' from Atmel.
  compatible:
    oneOf:
      - allOf:
          - minItems: 1
            items:
              - pattern: "^(atmel|catalyst|microchip|nxp|ramtron|renesas|rohm|st),(24(c|cs|lc|mac)[0-9]+|spd)$"
              - pattern: "^atmel,(24(c|cs|mac)[0-9]+|spd)$"
          - oneOf:
              - items:
                  pattern: c00$
              - items:
                  pattern: c01$
              - items:
                  pattern: cs01$
              - items:
                  pattern: c02$
              - items:
                  pattern: cs02$
              - items:
                  pattern: mac402$
              - items:
                  pattern: mac602$
              - items:
                  pattern: c04$
              - items:
                  pattern: cs04$
              - items:
                  pattern: c08$
              - items:
                  pattern: cs08$
              - items:
                  pattern: c16$
              - items:
                  pattern: cs16$
              - items:
                  pattern: c32$
              - items:
                  pattern: cs32$
              - items:
                  pattern: c64$
              - items:
                  pattern: cs64$
              - items:
                  pattern: c128$
              - items:
                  pattern: cs128$
              - items:
                  pattern: c256$
              - items:
                  pattern: cs256$
              - items:
                  pattern: c512$
              - items:
                  pattern: cs512$
              - items:
                  pattern: c1024$
              - items:
                  pattern: cs1024$
              - items:
                  pattern: c1025$
              - items:
                  pattern: cs1025$
              - items:
                  pattern: c2048$
              - items:
                  pattern: cs2048$
              - items:
                  pattern: spd$
      # These are special cases that don't conform to the above pattern.
      # Each requires a standard at24 model as fallback.
      - items:
          - const: belling,bl24c16a
          - const: atmel,24c16
      - items:
          - enum:
              - rohm,br24g01
              - rohm,br24t01
          - const: atmel,24c01
      - items:
          - enum:
              - nxp,se97b
              - renesas,r1ex24002
          - const: atmel,24c02
      - items:
          - enum:
              - onnn,cat24c04
              - onnn,cat24c05
          - const: atmel,24c04
      - items:
          - const: renesas,r1ex24016
          - const: atmel,24c16
      - items:
          - const: giantec,gt24c32a
          - const: atmel,24c32
      - items:
          - enum:
              - renesas,r1ex24128
              - samsung,s524ad0xd1
          - const: atmel,24c128

  label:
    description: Descriptive name of the EEPROM.

  reg:
    maxItems: 1

  pagesize:
    description:
      The length of the pagesize for writing. Please consult the
      manual of your device, that value varies a lot. A wrong value
      may result in data loss! If not specified, a safety value of
      '1' is used which will be very slow.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 8, 16, 32, 64, 128, 256]
    default: 1

  read-only:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Disables writes to the eeprom.

  size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Total eeprom size in bytes.

  no-read-rollover:
    $ref: /schemas/types.yaml#/definitions/flag
    description:
      Indicates that the multi-address eeprom does not automatically roll
      over reads to the next slave address. Please consult the manual of
      your device.

  wp-gpios: true

  address-width:
    description:
      Number of address bits.
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 8
    enum: [ 8, 16 ]

  num-addresses:
    description:
      Total number of i2c slave addresses this device takes.
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 1
    minimum: 1
    maximum: 8

  vcc-supply:
    description:
      phandle of the regulator that provides the supply voltage.

required:
  - compatible
  - reg

unevaluatedProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      eeprom@52 {
          compatible = "microchip,24c32", "atmel,24c32";
          reg = <0x52>;
          pagesize = <32>;
          wp-gpios = <&gpio1 3 0>;
          num-addresses = <8>;
      };
    };
...
