FPGA Region Device Tree Binding

Alan <PERSON>ll 2016

 CONTENTS
 - Introduction
 - Terminology
 - Sequence
 - FPGA Region
 - Supported Use Models
 - Device Tree Examples
 - Constraints


Introduction
============

FPGA Regions represent FPGA's and partial reconfiguration regions of FPGA's in
the Device Tree.  FPGA Regions provide a way to program FPGAs under device tree
control.

This device tree binding document hits some of the high points of FPGA usage and
attempts to include terminology used by both major FPGA manufacturers.  This
document isn't a replacement for any manufacturers specifications for FPGA
usage.


Terminology
===========

Full Reconfiguration
 * The entire FPGA is programmed.

Partial Reconfiguration (PR)
 * A section of an FPGA is reprogrammed while the rest of the FPGA is not
   affected.
 * Not all FPGA's support PR.

Partial Reconfiguration Region (PRR)
 * Also called a "reconfigurable partition"
 * A PRR is a specific section of an FPGA reserved for reconfiguration.
 * A base (or static) FPGA image may create a set of PRR's that later may
   be independently reprogrammed many times.
 * The size and specific location of each PRR is fixed.
 * The connections at the edge of each PRR are fixed.  The image that is loaded
   into a PRR must fit and must use a subset of the region's connections.
 * The busses within the FPGA are split such that each region gets its own
   branch that may be gated independently.

Persona
 * Also called a "partial bit stream"
 * An FPGA image that is designed to be loaded into a PRR.  There may be
   any number of personas designed to fit into a PRR, but only one at at time
   may be loaded.
 * A persona may create more regions.

FPGA Bridge
 * FPGA Bridges gate bus signals between a host and FPGA.
 * FPGA Bridges should be disabled while the FPGA is being programmed to
   prevent spurious signals on the cpu bus and to the soft logic.
 * FPGA bridges may be actual hardware or soft logic on an FPGA.
 * During Full Reconfiguration, hardware bridges between the host and FPGA
   will be disabled.
 * During Partial Reconfiguration of a specific region, that region's bridge
   will be used to gate the busses.  Traffic to other regions is not affected.
 * In some implementations, the FPGA Manager transparently handles gating the
   buses, eliminating the need to show the hardware FPGA bridges in the
   device tree.
 * An FPGA image may create a set of reprogrammable regions, each having its
   own bridge and its own split of the busses in the FPGA.

FPGA Manager
 * An FPGA Manager is a hardware block that programs an FPGA under the control
   of a host processor.

Base Image
 * Also called the "static image"
 * An FPGA image that is designed to do full reconfiguration of the FPGA.
 * A base image may set up a set of partial reconfiguration regions that may
   later be reprogrammed.

    ----------------       ----------------------------------
    |  Host CPU    |       |             FPGA               |
    |              |       |                                |
    |          ----|       |       -----------    --------  |
    |          | H |       |   |==>| Bridge0 |<==>| PRR0 |  |
    |          | W |       |   |   -----------    --------  |
    |          |   |       |   |                            |
    |          | B |<=====>|<==|   -----------    --------  |
    |          | R |       |   |==>| Bridge1 |<==>| PRR1 |  |
    |          | I |       |   |   -----------    --------  |
    |          | D |       |   |                            |
    |          | G |       |   |   -----------    --------  |
    |          | E |       |   |==>| Bridge2 |<==>| PRR2 |  |
    |          ----|       |       -----------    --------  |
    |              |       |                                |
    ----------------       ----------------------------------

Figure 1: An FPGA set up with a base image that created three regions.  Each
region (PRR0-2) gets its own split of the busses that is independently gated by
a soft logic bridge (Bridge0-2) in the FPGA.  The contents of each PRR can be
reprogrammed independently while the rest of the system continues to function.


Sequence
========

When a DT overlay that targets an FPGA Region is applied, the FPGA Region will
do the following:

 1. Disable appropriate FPGA bridges.
 2. Program the FPGA using the FPGA manager.
 3. Enable the FPGA bridges.
 4. The Device Tree overlay is accepted into the live tree.
 5. Child devices are populated.

When the overlay is removed, the child nodes will be removed and the FPGA Region
will disable the bridges.


FPGA Region
===========

FPGA Regions represent FPGA's and FPGA PR regions in the device tree.  An FPGA
Region brings together the elements needed to program on a running system and
add the child devices:

 * FPGA Manager
 * FPGA Bridges
 * image-specific information needed to to the programming.
 * child nodes

The intended use is that a Device Tree overlay (DTO) can be used to reprogram an
FPGA while an operating system is running.

An FPGA Region that exists in the live Device Tree reflects the current state.
If the live tree shows a "firmware-name" property or child nodes under an FPGA
Region, the FPGA already has been programmed.  A DTO that targets an FPGA Region
and adds the "firmware-name" property is taken as a request to reprogram the
FPGA.  After reprogramming is successful, the overlay is accepted into the live
tree.

The base FPGA Region in the device tree represents the FPGA and supports full
reconfiguration.  It must include a phandle to an FPGA Manager.  The base
FPGA region will be the child of one of the hardware bridges (the bridge that
allows register access) between the cpu and the FPGA.  If there are more than
one bridge to control during FPGA programming, the region will also contain a
list of phandles to the additional hardware FPGA Bridges.

For partial reconfiguration (PR), each PR region will have an FPGA Region.
These FPGA regions are children of FPGA bridges which are then children of the
base FPGA region.  The "Full Reconfiguration to add PRR's" example below shows
this.

If an FPGA Region does not specify an FPGA Manager, it will inherit the FPGA
Manager specified by its ancestor FPGA Region.  This supports both the case
where the same FPGA Manager is used for all of an FPGA as well the case where
a different FPGA Manager is used for each region.

FPGA Regions do not inherit their ancestor FPGA regions' bridges.  This prevents
shutting down bridges that are upstream from the other active regions while one
region is getting reconfigured (see Figure 1 above).  During PR, the FPGA's
hardware bridges remain enabled.  The PR regions' bridges will be FPGA bridges
within the static image of the FPGA.

Required properties:
- compatible : should contain "fpga-region"
- fpga-mgr : should contain a phandle to an FPGA Manager.  Child FPGA Regions
	inherit this property from their ancestor regions.  An fpga-mgr property
	in a region will override any inherited FPGA manager.
- #address-cells, #size-cells, ranges : must be present to handle address space
	mapping for child nodes.

Optional properties:
- firmware-name : should contain the name of an FPGA image file located on the
	firmware search path.  If this property shows up in a live device tree
	it indicates that the FPGA has already been programmed with this image.
	If this property is in an overlay targeting an FPGA region, it is a
	request to program the FPGA with that image.
- fpga-bridges : should contain a list of phandles to FPGA Bridges that must be
	controlled during FPGA programming along with the parent FPGA bridge.
	This property is optional if the FPGA Manager handles the bridges.
        If the fpga-region is  the child of an fpga-bridge, the list should not
        contain the parent bridge.
- partial-fpga-config : boolean, set if partial reconfiguration is to be done,
	otherwise full reconfiguration is done.
- external-fpga-config : boolean, set if the FPGA has already been configured
	prior to OS boot up.
- encrypted-fpga-config : boolean, set if the bitstream is encrypted
- region-unfreeze-timeout-us : The maximum time in microseconds to wait for
	bridges to successfully become enabled after the region has been
	programmed.
- region-freeze-timeout-us : The maximum time in microseconds to wait for
	bridges to successfully become disabled before the region has been
	programmed.
- config-complete-timeout-us : The maximum time in microseconds time for the
	FPGA to go to operating mode after the region has been programmed.
- child nodes : devices in the FPGA after programming.

In the example below, when an overlay is applied targeting fpga-region0,
fpga_mgr is used to program the FPGA.  Two bridges are controlled during
programming: the parent fpga_bridge0 and fpga_bridge1.  Because the region is
the child of fpga_bridge0, only fpga_bridge1 needs to be specified in the
fpga-bridges property.  During programming, these bridges are disabled, the
firmware specified in the overlay is loaded to the FPGA using the FPGA manager
specified in the region.  If FPGA programming succeeds, the bridges are
reenabled and the overlay makes it into the live device tree.  The child devices
are then populated.  If FPGA programming fails, the bridges are left disabled
and the overlay is rejected.  The overlay's ranges property maps the lwhps
bridge's region (0xff200000) and the hps bridge's region (0xc0000000) for use by
the two child devices.

Example:
Base tree contains:

	fpga_mgr: fpga-mgr@ff706000 {
		compatible = "altr,socfpga-fpga-mgr";
		reg = <0xff706000 0x1000
		       0xffb90000 0x20>;
		interrupts = <0 175 4>;
	};

	fpga_bridge0: fpga-bridge@ff400000 {
		compatible = "altr,socfpga-lwhps2fpga-bridge";
		reg = <0xff400000 0x100000>;
		resets = <&rst LWHPS2FPGA_RESET>;
		clocks = <&l4_main_clk>;

		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		fpga_region0: fpga-region0 {
			compatible = "fpga-region";
			fpga-mgr = <&fpga_mgr>;
		};
	};

	fpga_bridge1: fpga-bridge@ff500000 {
		compatible = "altr,socfpga-hps2fpga-bridge";
		reg = <0xff500000 0x10000>;
		resets = <&rst HPS2FPGA_RESET>;
		clocks = <&l4_main_clk>;
	};

Overlay contains:

/dts-v1/;
/plugin/;

&fpga_region0 {
	#address-cells = <1>;
	#size-cells = <1>;

	firmware-name = "soc_system.rbf";
	fpga-bridges = <&fpga_bridge1>;
	ranges = <0x20000 0xff200000 0x100000>,
		 <0x0 0xc0000000 0x20000000>;

	gpio@10040 {
		compatible = "altr,pio-1.0";
		reg = <0x10040 0x20>;
		altr,ngpio = <4>;
		#gpio-cells = <2>;
		clocks = <2>;
		gpio-controller;
	};

	onchip-memory {
		device_type = "memory";
		compatible = "altr,onchipmem-15.1";
		reg = <0x0 0x10000>;
	};
};


Supported Use Models
====================

In all cases the live DT must have the FPGA Manager, FPGA Bridges (if any), and
a FPGA Region.  The target of the Device Tree Overlay is the FPGA Region.  Some
uses are specific to an FPGA device.

 * No FPGA Bridges
   In this case, the FPGA Manager which programs the FPGA also handles the
   bridges behind the scenes.  No FPGA Bridge devices are needed for full
   reconfiguration.

 * Full reconfiguration with hardware bridges
   In this case, there are hardware bridges between the processor and FPGA that
   need to be controlled during full reconfiguration.  Before the overlay is
   applied, the live DT must include the FPGA Manager, FPGA Bridges, and a
   FPGA Region.  The FPGA Region is the child of the bridge that allows
   register access to the FPGA.  Additional bridges may be listed in a
   fpga-bridges property in the FPGA region or in the device tree overlay.

 * Partial reconfiguration with bridges in the FPGA
   In this case, the FPGA will have one or more PRR's that may be programmed
   separately while the rest of the FPGA can remain active.  To manage this,
   bridges need to exist in the FPGA that can gate the buses going to each FPGA
   region while the buses are enabled for other sections.  Before any partial
   reconfiguration can be done, a base FPGA image must be loaded which includes
   PRR's with FPGA bridges.  The device tree should have an FPGA region for each
   PRR.

Device Tree Examples
====================

The intention of this section is to give some simple examples, focusing on
the placement of the elements detailed above, especially:
 * FPGA Manager
 * FPGA Bridges
 * FPGA Region
 * ranges
 * target-path or target

For the purposes of this section, I'm dividing the Device Tree into two parts,
each with its own requirements.  The two parts are:
 * The live DT prior to the overlay being added
 * The DT overlay

The live Device Tree must contain an FPGA Region, an FPGA Manager, and any FPGA
Bridges.  The FPGA Region's "fpga-mgr" property specifies the manager by phandle
to handle programming the FPGA.  If the FPGA Region is the child of another FPGA
Region, the parent's FPGA Manager is used.  If FPGA Bridges need to be involved,
they are specified in the FPGA Region by the "fpga-bridges" property.  During
FPGA programming, the FPGA Region will disable the bridges that are in its
"fpga-bridges" list and will re-enable them after FPGA programming has
succeeded.

The Device Tree Overlay will contain:
 * "target-path" or "target"
   The insertion point where the contents of the overlay will go into the
   live tree.  target-path is a full path, while target is a phandle.
 * "ranges"
    The address space mapping from processor to FPGA bus(ses).
 * "firmware-name"
   Specifies the name of the FPGA image file on the firmware search
   path.  The search path is described in the firmware class documentation.
 * "partial-fpga-config"
   This binding is a boolean and should be present if partial reconfiguration
   is to be done.
 * child nodes corresponding to hardware that will be loaded in this region of
   the FPGA.

Device Tree Example: Full Reconfiguration without Bridges
=========================================================

Live Device Tree contains:
	fpga_mgr0: fpga-mgr@f8007000 {
		compatible = "xlnx,zynq-devcfg-1.0";
		reg = <0xf8007000 0x100>;
		interrupt-parent = <&intc>;
		interrupts = <0 8 4>;
		clocks = <&clkc 12>;
		clock-names = "ref_clk";
		syscon = <&slcr>;
	};

	fpga_region0: fpga-region0 {
		compatible = "fpga-region";
		fpga-mgr = <&fpga_mgr0>;
		#address-cells = <0x1>;
		#size-cells = <0x1>;
		ranges;
	};

DT Overlay contains:

/dts-v1/;
/plugin/;

&fpga_region0 {
	#address-cells = <1>;
	#size-cells = <1>;

	firmware-name = "zynq-gpio.bin";

	gpio1: gpio@40000000 {
		compatible = "xlnx,xps-gpio-1.00.a";
		reg = <0x40000000 0x10000>;
		gpio-controller;
		#gpio-cells = <0x2>;
		xlnx,gpio-width= <0x6>;
	};
};

Device Tree Example: Full Reconfiguration to add PRR's
======================================================

The base FPGA Region is specified similar to the first example above.

This example programs the FPGA to have two regions that can later be partially
configured.  Each region has its own bridge in the FPGA fabric.

DT Overlay contains:

/dts-v1/;
/plugin/;

&fpga_region0 {
	#address-cells = <1>;
	#size-cells = <1>;

	firmware-name = "base.rbf";

	fpga-bridge@4400 {
		compatible = "altr,freeze-bridge-controller";
		reg = <0x4400 0x10>;

		fpga_region1: fpga-region1 {
			compatible = "fpga-region";
			#address-cells = <0x1>;
			#size-cells = <0x1>;
			ranges;
		};
	};

	fpga-bridge@4420 {
		compatible = "altr,freeze-bridge-controller";
		reg = <0x4420 0x10>;

		fpga_region2: fpga-region2 {
			compatible = "fpga-region";
			#address-cells = <0x1>;
			#size-cells = <0x1>;
			ranges;
		};
	};
};

Device Tree Example: Partial Reconfiguration
============================================

This example reprograms one of the PRR's set up in the previous example.

The sequence that occurs when this overlay is similar to the above, the only
differences are that the FPGA is partially reconfigured due to the
"partial-fpga-config" boolean and the only bridge that is controlled during
programming is the FPGA based bridge of fpga_region1.

/dts-v1/;
/plugin/;

&fpga_region1 {
	#address-cells = <1>;
	#size-cells = <1>;

	firmware-name = "soc_image2.rbf";
	partial-fpga-config;

	gpio@10040 {
		compatible = "altr,pio-1.0";
		reg = <0x10040 0x20>;
		clocks = <0x2>;
		altr,ngpio = <0x4>;
		#gpio-cells = <0x2>;
		gpio-controller;
	};
};

Constraints
===========

It is beyond the scope of this document to fully describe all the FPGA design
constraints required to make partial reconfiguration work[1] [2] [3], but a few
deserve quick mention.

A persona must have boundary connections that line up with those of the partition
or region it is designed to go into.

During programming, transactions through those connections must be stopped and
the connections must be held at a fixed logic level.  This can be achieved by
FPGA Bridges that exist on the FPGA fabric prior to the partial reconfiguration.

--
[1] www.altera.com/content/dam/altera-www/global/en_US/pdfs/literature/ug/ug_partrecon.pdf
[2] tspace.library.utoronto.ca/bitstream/1807/67932/1/Byma_Stuart_A_201411_MAS_thesis.pdf
[3] https://www.xilinx.com/support/documentation/sw_manuals/xilinx14_1/ug702.pdf
