# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/st,stm32-cryp.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 CRYP

description: The STM32 CRYP block is built on the CRYP block found in
  the STn8820 SoC introduced in 2007, and subsequently used in the U8500
  SoC in 2010.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - st,stn8820-cryp
      - ster<PERSON>son,ux500-cryp
      - st,stm32f756-cryp
      - st,stm32mp1-cryp

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  interrupts:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    items:
      - description: mem2cryp DMA channel
      - description: cryp2mem DMA channel

  dma-names:
    items:
      - const: mem2cryp
      - const: cryp2mem

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    #include <dt-bindings/reset/stm32mp1-resets.h>
    cryp@54001000 {
      compatible = "st,stm32mp1-cryp";
      reg = <0x54001000 0x400>;
      interrupts = <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&rcc CRYP1>;
      resets = <&rcc CRYP1_R>;
    };

...
