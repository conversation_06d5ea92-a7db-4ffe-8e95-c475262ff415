# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/st,stm32-hash.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 HASH

description: The STM32 HASH block is built on the HASH block found in
  the STn8820 SoC introduced in 2007, and subsequently used in the U8500
  SoC in 2010.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - st,stn8820-hash
      - stericsson,ux500-hash
      - st,stm32f456-hash
      - st,stm32f756-hash
      - st,stm32mp13-hash

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  interrupts:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    maxItems: 1

  dma-names:
    items:
      - const: in

  dma-maxburst:
    description: Set number of maximum dma burst supported
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 2
    default: 0

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks

allOf:
  - if:
      properties:
        compatible:
          items:
            const: stericsson,ux500-hash
    then:
      properties:
        interrupts: false
    else:
      required:
        - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    #include <dt-bindings/reset/stm32mp1-resets.h>
    hash@54002000 {
      compatible = "st,stm32f756-hash";
      reg = <0x54002000 0x400>;
      interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&rcc HASH1>;
      resets = <&rcc HASH1_R>;
      dmas = <&mdma1 31 0x10 0x1000A02 0x0 0x0>;
      dma-names = "in";
      dma-maxburst = <2>;
    };

...
