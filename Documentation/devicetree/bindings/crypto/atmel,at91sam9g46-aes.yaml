# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2022 Microchip Technology, Inc. and its subsidiaries
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/atmel,at91sam9g46-aes.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel Advanced Encryption Standard (AES) HW cryptographic accelerator

maintainers:
  - Tudor Ambarus <<EMAIL>>

properties:
  compatible:
    const: atmel,at91sam9g46-aes

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: aes_clk

  dmas:
    items:
      - description: TX DMA Channel
      - description: RX DMA Channel

  dma-names:
    items:
      - const: tx
      - const: rx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/at91.h>
    #include <dt-bindings/dma/at91.h>

    aes: crypto@e1810000 {
      compatible = "atmel,at91sam9g46-aes";
      reg = <0xe1810000 0x100>;
      interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&pmc PMC_TYPE_PERIPHERAL 27>;
      clock-names = "aes_clk";
      dmas = <&dma0 AT91_XDMAC_DT_PERID(1)>,
             <&dma0 AT91_XDMAC_DT_PERID(2)>;
      dma-names = "tx", "rx";
    };
