# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/allwinner,sun8i-ce.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner Crypto Engine driver

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - allwinner,sun8i-h3-crypto
      - allwinner,sun8i-r40-crypto
      - allwinner,sun20i-d1-crypto
      - allwinner,sun50i-a64-crypto
      - allwinner,sun50i-h5-crypto
      - allwinner,sun50i-h6-crypto

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Bus clock
      - description: Module clock
      - description: MBus clock
      - description: TRNG clock (RC oscillator)
    minItems: 2

  clock-names:
    items:
      - const: bus
      - const: mod
      - const: ram
      - const: trng
    minItems: 2

  resets:
    maxItems: 1

if:
  properties:
    compatible:
      enum:
        - allwinner,sun20i-d1-crypto
then:
  properties:
    clocks:
      minItems: 4
    clock-names:
      minItems: 4
else:
  if:
    properties:
      compatible:
        const: allwinner,sun50i-h6-crypto
  then:
    properties:
      clocks:
        minItems: 3
        maxItems: 3
      clock-names:
        minItems: 3
        maxItems: 3
  else:
    properties:
      clocks:
        maxItems: 2
      clock-names:
        maxItems: 2

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - resets

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/sun50i-a64-ccu.h>
    #include <dt-bindings/reset/sun50i-a64-ccu.h>

    crypto: crypto@1c15000 {
      compatible = "allwinner,sun8i-h3-crypto";
      reg = <0x01c15000 0x1000>;
      interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&ccu CLK_BUS_CE>, <&ccu CLK_CE>;
      clock-names = "bus", "mod";
      resets = <&ccu RST_BUS_CE>;
    };
