# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/qcom-qce.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm crypto engine driver

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  This document defines the binding for the QCE crypto
  controller found on Qualcomm parts.

properties:
  compatible:
    oneOf:
      - const: qcom,crypto-v5.1
        deprecated: true
        description: Kept only for ABI backward compatibility

      - const: qcom,crypto-v5.4
        deprecated: true
        description: Kept only for ABI backward compatibility

      - items:
          - enum:
              - qcom,ipq4019-qce
              - qcom,sm8150-qce
          - const: qcom,qce

      - items:
          - enum:
              - qcom,ipq6018-qce
              - qcom,ipq8074-qce
              - qcom,ipq9574-qce
              - qcom,msm8996-qce
              - qcom,qcm2290-qce
              - qcom,sdm845-qce
              - qcom,sm6115-qce
          - const: qcom,ipq4019-qce
          - const: qcom,qce

      - items:
          - enum:
              - qcom,sm8250-qce
              - qcom,sm8350-qce
              - qcom,sm8450-qce
              - qcom,sm8550-qce
          - const: qcom,sm8150-qce
          - const: qcom,qce

  reg:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 3

  clock-names:
    minItems: 1
    maxItems: 3

  iommus:
    minItems: 1
    maxItems: 8
    description:
      phandle to apps_smmu node with sid mask.

  interconnects:
    maxItems: 1
    description:
      Interconnect path between qce crypto and main memory.

  interconnect-names:
    const: memory

  dmas:
    items:
      - description: DMA specifiers for rx dma channel.
      - description: DMA specifiers for tx dma channel.

  dma-names:
    items:
      - const: rx
      - const: tx

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,crypto-v5.1
              - qcom,crypto-v5.4
              - qcom,ipq6018-qce
              - qcom,ipq8074-qce
              - qcom,msm8996-qce
              - qcom,sdm845-qce
    then:
      properties:
        clocks:
          maxItems: 3
        clock-names:
          items:
            - const: iface
            - const: bus
            - const: core
      required:
        - clocks
        - clock-names

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,qcm2290-qce
              - qcom,sm6115-qce
    then:
      properties:
        clocks:
          maxItems: 1
        clock-names:
          items:
            - const: core
      required:
        - clocks
        - clock-names

required:
  - compatible
  - reg
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-apq8084.h>
    crypto-engine@fd45a000 {
        compatible = "qcom,ipq6018-qce", "qcom,ipq4019-qce", "qcom,qce";
        reg = <0xfd45a000 0x6000>;
        clocks = <&gcc GCC_CE2_AHB_CLK>,
                 <&gcc GCC_CE2_AXI_CLK>,
                 <&gcc GCC_CE2_CLK>;
        clock-names = "iface", "bus", "core";
        dmas = <&cryptobam 2>, <&cryptobam 3>;
        dma-names = "rx", "tx";
        iommus = <&apps_smmu 0x584 0x0011>,
                 <&apps_smmu 0x586 0x0011>,
                 <&apps_smmu 0x594 0x0011>,
                 <&apps_smmu 0x596 0x0011>;
    };
