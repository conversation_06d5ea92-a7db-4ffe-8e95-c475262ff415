# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2022 Microchip Technology, Inc. and its subsidiaries
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/atmel,at91sam9g46-tdes.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel Triple Data Encryption Standard (TDES) HW cryptographic accelerator

maintainers:
  - Tudor Ambarus <<EMAIL>>

properties:
  compatible:
    const: atmel,at91sam9g46-tdes

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    const: tdes_clk

  dmas:
    items:
      - description: TX DMA Channel
      - description: RX DMA Channel

  dma-names:
    items:
      - const: tx
      - const: rx

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/at91.h>
    #include <dt-bindings/dma/at91.h>

    tdes: crypto@e2014000 {
      compatible = "atmel,at91sam9g46-tdes";
      reg = <0xe2014000 0x100>;
      interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&pmc PMC_TYPE_PERIPHERAL 96>;
      clock-names = "tdes_clk";
      dmas = <&dma0 AT91_XDMAC_DT_PERID(54)>,
             <&dma0 AT91_XDMAC_DT_PERID(53)>;
      dma-names = "tx", "rx";
    };
