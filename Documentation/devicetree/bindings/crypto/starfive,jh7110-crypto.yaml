# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/starfive,jh7110-crypto.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: StarFive Cryptographic Module

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: starfive,jh7110-crypto

  reg:
    maxItems: 1

  clocks:
    items:
      - description: Hardware reference clock
      - description: AHB reference clock

  clock-names:
    items:
      - const: hclk
      - const: ahb

  interrupts:
    maxItems: 1

  resets:
    maxItems: 1

  dmas:
    items:
      - description: TX DMA channel
      - description: RX DMA channel

  dma-names:
    items:
      - const: tx
      - const: rx

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - resets
  - dmas
  - dma-names

additionalProperties: false

examples:
  - |
    crypto: crypto@16000000 {
        compatible = "starfive,jh7110-crypto";
        reg = <0x16000000 0x4000>;
        clocks = <&clk 15>, <&clk 16>;
        clock-names = "hclk", "ahb";
        interrupts = <28>;
        resets = <&reset 3>;
        dmas = <&dma 1 2>,
               <&dma 0 2>;
        dma-names = "tx", "rx";
    };
...
