# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/crypto/qcom,inline-crypto-engine.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. (QTI) Inline Crypto Engine

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

properties:
  compatible:
    items:
      - enum:
          - qcom,sm8450-inline-crypto-engine
          - qcom,sm8550-inline-crypto-engine
      - const: qcom,inline-crypto-engine

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,sm8550-gcc.h>

    crypto@1d88000 {
      compatible = "qcom,sm8550-inline-crypto-engine",
                   "qcom,inline-crypto-engine";
      reg = <0x01d88000 0x8000>;
      clocks = <&gcc GCC_UFS_PHY_ICE_CORE_CLK>;
    };
...
