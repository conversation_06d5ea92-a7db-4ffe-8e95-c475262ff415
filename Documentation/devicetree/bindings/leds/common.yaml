# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/common.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Common leds properties

maintainers:
  - <PERSON><PERSON> <jacek.an<PERSON><PERSON><PERSON>@gmail.com>
  - <PERSON> <<EMAIL>>

description:
  LED and flash LED devices provide the same basic functionality as current
  regulators, but extended with LED and flash LED specific features like
  blinking patterns, flash timeout, flash faults and external flash strobe mode.

  Many LED devices expose more than one current output that can be connected
  to one or more discrete LED component. Since the arrangement of connections
  can influence the way of the LED device initialization, the LED components
  have to be tightly coupled with the LED device binding. They are represented
  by child nodes of the parent LED device binding.

properties:
  led-sources:
    description:
      List of device current outputs the LED is connected to. The outputs are
      identified by the numbers that must be defined in the LED device binding
      documentation.
    $ref: /schemas/types.yaml#/definitions/uint32-array

  function:
    description:
      LED function. Use one of the LED_FUNCTION_* prefixed definitions
      from the header include/dt-bindings/leds/common.h. If there is no
      matching LED_FUNCTION available, add a new one.
    $ref: /schemas/types.yaml#/definitions/string

  color:
    description:
      Color of the LED. Use one of the LED_COLOR_ID_* prefixed definitions from
      the header include/dt-bindings/leds/common.h. If there is no matching
      LED_COLOR_ID available, add a new one.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 9

  function-enumerator:
    description:
      Integer to be used when more than one instance of the same function is
      needed, differing only with an ordinal number.
    $ref: /schemas/types.yaml#/definitions/uint32

  label:
    description:
      The label for this LED. If omitted, the label is taken from the node name
      (excluding the unit address). It has to uniquely identify a device, i.e.
      no other LED class device can be assigned the same label. This property is
      deprecated - use 'function' and 'color' properties instead.
      function-enumerator has no effect when this property is present.

  default-state:
    description:
      The initial state of the LED. If the LED is already on or off and the
      default-state property is set the to same value, then no glitch should be
      produced where the LED momentarily turns off (or on). The "keep" setting
      will keep the LED at whatever its current state is, without producing a
      glitch.
    $ref: /schemas/types.yaml#/definitions/string
    enum:
      - on
      - off
      - keep
    default: off

  linux,default-trigger:
    description:
      This parameter, if present, is a string defining the trigger assigned to
      the LED.
    $ref: /schemas/types.yaml#/definitions/string

    oneOf:
      - enum:
            # LED will act as a back-light, controlled by the framebuffer system
          - backlight
            # LED will turn on (see also "default-state" property)
          - default-on
            # LED "double" flashes at a load average based rate
          - heartbeat
            # LED indicates disk activity
          - disk-activity
            # LED indicates disk read activity
          - disk-read
            # LED indicates disk write activity
          - disk-write
            # LED flashes at a fixed, configurable rate
          - timer
            # LED alters the brightness for the specified duration with one software
            # timer (requires "led-pattern" property)
          - pattern
            # LED indicates mic mute state
          - audio-micmute
            # LED indicates audio mute state
          - audio-mute
            # LED indicates bluetooth power state
          - bluetooth-power
            # LED indicates camera flash state
          - flash
            # LED indicated keyboard capslock
          - kbd-capslock
            # LED indicates MTD memory activity
          - mtd
            # LED indicates NAND memory activity (deprecated),
            # in new implementations use "mtd"
          - nand-disk
            # No trigger assigned to the LED. This is the default mode
            # if trigger is absent
          - none
            # LED indicates camera torch state
          - torch
            # LED indicates USB gadget activity
          - usb-gadget
            # LED indicates USB host activity
          - usb-host
            # LED indicates USB port state
          - usbport
        # LED is triggered by CPU activity
      - pattern: "^cpu[0-9]*$"
        # LED is triggered by Bluetooth activity
      - pattern: "^hci[0-9]+-power$"
        # LED is triggered by SD/MMC activity
      - pattern: "^mmc[0-9]+$"
        # LED is triggered by WLAN activity
      - pattern: "^phy[0-9]+tx$"

  led-pattern:
    description: |
      Array of integers with default pattern for certain triggers.

      Each trigger may parse this property differently:
        - one-shot : two numbers specifying delay on and delay off (in ms),
        - timer : two numbers specifying delay on and delay off (in ms),
        - pattern : the pattern is given by a series of tuples, of
          brightness and duration (in ms).  The exact format is
          described in:
          Documentation/devicetree/bindings/leds/leds-trigger-pattern.txt
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    items:
      minItems: 2
      maxItems: 2

  led-max-microamp:
    description:
      Maximum LED supply current in microamperes. This property can be made
      mandatory for the board configurations introducing a risk of hardware
      damage in case an excessive current is set.
      For flash LED controllers with configurable current this property is
      mandatory for the LEDs in the non-flash modes (e.g. torch or indicator).

  max-brightness:
    description:
      Normally, the maximum brightness is determined by the hardware, and this
      property is not required. This property is used to set a software limit.
      It could happen that an LED is made so bright that it gets damaged or
      causes damage due to restrictions in a specific system, such as mounting
      conditions.
      Note that this flag is mainly used for PWM-LEDs, where it is not possible
      to map brightness to current. Drivers for other controllers should use
      led-max-microamp.
    $ref: /schemas/types.yaml#definitions/uint32

  panic-indicator:
    description:
      This property specifies that the LED should be used, if at all possible,
      as a panic indicator.
    type: boolean

  retain-state-shutdown:
    description:
      This property specifies that the LED should not be turned off or changed
      when the system shuts down.
    type: boolean

  trigger-sources:
    description: |
      List of devices which should be used as a source triggering this LED
      activity. Some LEDs can be related to a specific device and should somehow
      indicate its state. E.g. USB 2.0 LED may react to device(s) in a USB 2.0
      port(s).
      Another common example is switch or router with multiple Ethernet ports
      each of them having its own LED assigned (assuming they are not
      hardwired). In such cases this property should contain phandle(s) of
      related source device(s).
      In many cases LED can be related to more than one device (e.g. one USB LED
      vs. multiple USB ports). Each source should be represented by a node in
      the device tree and be referenced by a phandle and a set of phandle
      arguments. A length of arguments should be specified by the
      #trigger-source-cells property in the source node.
    $ref: /schemas/types.yaml#/definitions/phandle-array

  # Required properties for flash LED child nodes:
  flash-max-microamp:
    description:
      Maximum flash LED supply current in microamperes. Required for flash LED
      nodes with configurable current.

  flash-max-timeout-us:
    description:
      Maximum timeout in microseconds after which the flash LED is turned off.
      Required for flash LED nodes with configurable timeout.

additionalProperties: true

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/leds/common.h>

    led-controller {
        compatible = "gpio-leds";

        led-0 {
            function = LED_FUNCTION_STATUS;
            linux,default-trigger = "heartbeat";
            gpios = <&gpio0 0 GPIO_ACTIVE_HIGH>;
        };

        led-1 {
            function = LED_FUNCTION_USB;
            gpios = <&gpio0 1 GPIO_ACTIVE_HIGH>;
            trigger-sources = <&ohci_port1>, <&ehci_port1>;
        };
    };

  - |
    #include <dt-bindings/leds/common.h>

    led-controller {
        compatible = "maxim,max77693-led";

        led {
            function = LED_FUNCTION_FLASH;
            color = <LED_COLOR_ID_WHITE>;
            led-sources = <0>, <1>;
            led-max-microamp = <50000>;
            flash-max-microamp = <320000>;
            flash-max-timeout-us = <500000>;
        };
    };

  - |
    #include <dt-bindings/leds/common.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        led-controller@30 {
            compatible = "panasonic,an30259a";
            reg = <0x30>;
            #address-cells = <1>;
            #size-cells = <0>;

            led@1 {
                reg = <1>;
                linux,default-trigger = "heartbeat";
                function = LED_FUNCTION_INDICATOR;
                function-enumerator = <1>;
            };

            led@2 {
                reg = <2>;
                function = LED_FUNCTION_INDICATOR;
                function-enumerator = <2>;
            };

            led@3 {
                reg = <3>;
                function = LED_FUNCTION_INDICATOR;
                function-enumerator = <3>;
            };
        };
    };

...
