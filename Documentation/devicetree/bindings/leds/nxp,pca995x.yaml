# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/nxp,pca995x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP PCA995x LED controllers

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description:
  The NXP PCA9952/PCA9955B are programmable LED controllers connected via I2C
  that can drive 16 separate lines. Each of them can be individually switched
  on and off, and brightness can be controlled via individual PWM.

  Datasheets are available at
  https://www.nxp.com/docs/en/data-sheet/PCA9952_PCA9955.pdf
  https://www.nxp.com/docs/en/data-sheet/PCA9955B.pdf

properties:
  compatible:
    enum:
      - nxp,pca9952
      - nxp,pca9955b

  reg:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

patternProperties:
  "^led@[0-9a-f]+$":
    type: object
    $ref: common.yaml#
    unevaluatedProperties: false

    properties:
      reg:
        minimum: 0
        maximum: 15

    required:
      - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/leds/common.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        led-controller@1 {
            compatible = "nxp,pca9955b";
            reg = <0x01>;
            #address-cells = <1>;
            #size-cells = <0>;

            led@0 {
                reg = <0x0>;
                color = <LED_COLOR_ID_RED>;
                function = LED_FUNCTION_POWER;
            };

            led@2 {
                reg = <0x2>;
                color = <LED_COLOR_ID_WHITE>;
                function = LED_FUNCTION_STATUS;
            };
        };
    };

...
