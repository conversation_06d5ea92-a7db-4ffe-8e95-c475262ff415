# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/irled/pwm-ir-tx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IR LED connected through PWM pin

maintainers:
  - <PERSON> <<EMAIL>>

description:
  IR LED connected through PWM pin which is used as remote controller
  transmitter.

properties:
  compatible:
    const: pwm-ir-tx

  pwms:
    maxItems: 1

required:
  - compatible
  - pwms

additionalProperties: false

examples:
  - |
    irled {
        compatible = "pwm-ir-tx";
        pwms = <&pwm0 0 10000000>;
    };
