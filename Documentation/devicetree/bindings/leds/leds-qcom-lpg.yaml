# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/leds-qcom-lpg.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Light Pulse Generator

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: >
  The Qualcomm Light Pulse Generator consists of three different hardware blocks;
  a ramp generator with lookup table, the light pulse generator and a three
  channel current sink. These blocks are found in a wide range of Qualcomm PMICs.

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,pm660l-lpg
          - qcom,pm8150b-lpg
          - qcom,pm8150l-lpg
          - qcom,pm8350c-pwm
          - qcom,pm8916-pwm
          - qcom,pm8941-lpg
          - qcom,pm8994-lpg
          - qcom,pmc8180c-lpg
          - qcom,pmi632-lpg
          - qcom,pmi8994-lpg
          - qcom,pmi8998-lpg
          - qcom,pmk8550-pwm
      - items:
          - enum:
              - qcom,pm8550-pwm
          - const: qcom,pm8350c-pwm

  "#pwm-cells":
    const: 2

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  qcom,power-source:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      power-source used to drive the output, as defined in the datasheet.
      Should be specified if the TRILED block is present
    enum: [0, 1, 3]

  qcom,dtest:
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    description: >
      A list of integer pairs, where each pair represent the dtest line the
      particular channel should be connected to and the flags denoting how the
      value should be outputted, as defined in the datasheet. The number of
      pairs should be the same as the number of channels.
    items:
      items:
        - description: dtest line to attach
        - description: flags for the attachment

  multi-led:
    type: object
    $ref: leds-class-multicolor.yaml#
    unevaluatedProperties: false

    properties:
      "#address-cells":
        const: 1

      "#size-cells":
        const: 0

    patternProperties:
      "^led@[0-9a-f]$":
        type: object
        $ref: common.yaml#
        unevaluatedProperties: false

        properties:
          reg:
            maxItems: 1

        required:
          - reg

patternProperties:
  "^led@[0-9a-f]$":
    type: object
    $ref: common.yaml#
    unevaluatedProperties: false

    properties:
      reg:
        maxItems: 1

    required:
      - reg

required:
  - compatible

additionalProperties: false

examples:
  - |
    #include <dt-bindings/leds/common.h>

    led-controller {
      compatible = "qcom,pmi8994-lpg";

      #address-cells = <1>;
      #size-cells = <0>;

      qcom,power-source = <1>;

      qcom,dtest = <0 0>,
                   <0 0>,
                   <0 0>,
                   <4 1>;

      led@1 {
        reg = <1>;
        color = <LED_COLOR_ID_GREEN>;
        function = LED_FUNCTION_INDICATOR;
        function-enumerator = <1>;
      };

      led@2 {
        reg = <2>;
        color = <LED_COLOR_ID_GREEN>;
        function = LED_FUNCTION_INDICATOR;
        function-enumerator = <0>;
        default-state = "on";
      };

      led@3 {
        reg = <3>;
        color = <LED_COLOR_ID_GREEN>;
        function = LED_FUNCTION_INDICATOR;
        function-enumerator = <2>;
      };

      led@4 {
        reg = <4>;
        color = <LED_COLOR_ID_GREEN>;
        function = LED_FUNCTION_INDICATOR;
        function-enumerator = <3>;
      };
    };
  - |
    #include <dt-bindings/leds/common.h>

    led-controller {
      compatible = "qcom,pmi8994-lpg";

      #address-cells = <1>;
      #size-cells = <0>;

      qcom,power-source = <1>;

      multi-led {
        color = <LED_COLOR_ID_RGB>;
        function = LED_FUNCTION_STATUS;

        #address-cells = <1>;
        #size-cells = <0>;

        led@1 {
          reg = <1>;
          color = <LED_COLOR_ID_RED>;
        };

        led@2 {
          reg = <2>;
          color = <LED_COLOR_ID_GREEN>;
        };

        led@3 {
          reg = <3>;
          color = <LED_COLOR_ID_BLUE>;
        };
      };
    };
  - |
    pwm-controller {
      compatible = "qcom,pm8916-pwm";
      #pwm-cells = <2>;
    };
...
