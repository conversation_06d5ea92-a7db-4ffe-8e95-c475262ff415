# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/register-bit-led.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Register Bit LEDs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |+
  Register bit leds are used with syscon multifunctional devices where single
  bits in a certain register can turn on/off a single LED. The register bit LEDs
  appear as children to the syscon device, with the proper compatible string.
  For the syscon bindings see:
  Documentation/devicetree/bindings/mfd/syscon.yaml

allOf:
  - $ref: /schemas/leds/common.yaml#

properties:
  $nodename:
    description:
      The unit-address is in the form of @<reg addr>,<bit offset>
    pattern: '^led@[0-9a-f]+,[0-9a-f]{1,2}$'

  compatible:
    const: register-bit-led

  reg:
    description:
      The register address and size
    maxItems: 1

  mask:
    description:
      bit mask for the bit controlling this LED in the register
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      [ 0x1, 0x2, 0x4, 0x8, 0x10, 0x20, 0x40, 0x80, 0x100, 0x200, 0x400, 0x800,
        0x1000, 0x2000, 0x4000, 0x8000, 0x10000, 0x20000, 0x40000, 0x80000,
        0x100000, 0x200000, 0x400000, 0x800000, 0x1000000, 0x2000000, 0x4000000,
        0x8000000, 0x10000000, 0x20000000, 0x40000000, 0x80000000 ]

  offset:
    description:
      register offset to the register controlling this LED
    $ref: /schemas/types.yaml#/definitions/uint32
    deprecated: true

required:
  - compatible
  - mask
  - reg

unevaluatedProperties: false

examples:
  - |

    syscon@10000000 {
        compatible = "arm,realview-pb1176-syscon", "syscon";
        reg = <0x10000000 0x1000>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges = <0x0 0x10000000 0x1000>;

        led@8,0 {
            compatible = "register-bit-led";
            reg = <0x08 0x04>;
            offset = <0x08>;
            mask = <0x01>;
            label = "versatile:0";
            linux,default-trigger = "heartbeat";
            default-state = "on";
        };
        led@8,1 {
            compatible = "register-bit-led";
            reg = <0x08 0x04>;
            offset = <0x08>;
            mask = <0x02>;
            label = "versatile:1";
            default-state = "off";
        };
        led@8,2 {
            compatible = "register-bit-led";
            reg = <0x08 0x04>;
            offset = <0x08>;
            mask = <0x04>;
            label = "versatile:2";
            default-state = "off";
        };
    };
...
