# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/leds-lp55xx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI/National Semiconductor LP55xx and LP8501 LED Drivers

maintainers:
  - <PERSON><PERSON> <jacek.an<PERSON><PERSON><PERSON>@gmail.com>
  - <PERSON> <<EMAIL>>

description: |
  Bindings for the TI/National Semiconductor LP55xx and LP8501 multi channel
  LED Drivers.

  For more product information please see the link below:
    https://www.ti.com/lit/gpn/lp5521
    https://www.ti.com/lit/gpn/lp5523
    https://www.ti.com/lit/gpn/lp55231
    https://www.ti.com/lit/gpn/lp5562
    https://www.ti.com/lit/gpn/lp8501

properties:
  compatible:
    enum:
      - national,lp5521
      - national,lp5523
      - ti,lp55231
      - ti,lp5562
      - ti,lp8501

  reg:
    maxItems: 1
    description: I2C slave address

  clock-mode:
    $ref: /schemas/types.yaml#/definitions/uint8
    description: |
      Input clock mode
    enum:
      - 0 # automode
      - 1 # internal
      - 2 # external

  enable-gpios:
    maxItems: 1
    description: |
      GPIO attached to the chip's enable pin

  label: true

  pwr-sel:
    $ref: /schemas/types.yaml#/definitions/uint8
    description: |
      LP8501 specific property. Power selection for output channels.
    enum:
      - 0 # D1~9 are connected to VDD
      - 1 # D1~6 with VDD, D7~9 with VOUT
      - 2 # D1~6 with VOUT, D7~9 with VDD
      - 3 # D1~9 are connected to VOUT

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  ti,charge-pump-mode:
    description:
      Set the operating mode of the internal charge pump as defined in
      <dt-bindings/leds/leds-lp55xx.h>.
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 3 # auto
    maximum: 3

patternProperties:
  '^multi-led@[0-8]$':
    type: object
    $ref: leds-class-multicolor.yaml#
    unevaluatedProperties: false

    properties:
      reg:
        maximum: 8

      '#address-cells':
        const: 1

      '#size-cells':
        const: 0

    patternProperties:
      "^led@[0-8]$":
        type: object
        $ref: common.yaml#
        unevaluatedProperties: false

        properties:
          led-cur:
            $ref: /schemas/types.yaml#/definitions/uint8
            description: |
              Current setting at each LED channel (mA x10, 0 if LED is not connected)
            minimum: 0
            maximum: 255

          max-cur:
            $ref: /schemas/types.yaml#/definitions/uint8
            description: Maximum current at each LED channel.

          reg:
            maximum: 8

        required:
          - reg

  "^led@[0-8]$":
    type: object
    $ref: common.yaml#
    unevaluatedProperties: false

    properties:
      led-cur:
        $ref: /schemas/types.yaml#/definitions/uint8
        description: |
          Current setting at each LED channel (mA x10, 0 if LED is not connected)
        minimum: 0
        maximum: 255

      max-cur:
        $ref: /schemas/types.yaml#/definitions/uint8
        description: Maximum current at each LED channel.

      reg:
        description: |
          Output channel for the LED.  This is zero based channel identifier and
          the data sheet is a one based channel identifier.
          reg value to output to LED output number
        enum:
          - 0 # LED output D1
          - 1 # LED output D2
          - 2 # LED output D3
          - 3 # LED output D4
          - 4 # LED output D5
          - 5 # LED output D6
          - 6 # LED output D7
          - 7 # LED output D8
          - 8 # LED output D9

      chan-name:
        $ref: /schemas/types.yaml#/definitions/string
        description: name of channel

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/leds/common.h>
    #include <dt-bindings/leds/leds-lp55xx.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        led-controller@32 {
            #address-cells = <1>;
            #size-cells = <0>;
            compatible = "ti,lp8501";
            reg = <0x32>;
            clock-mode = /bits/ 8 <2>;
            pwr-sel = /bits/ 8 <3>;	/* D1~9 connected to VOUT */
            ti,charge-pump-mode = <LP55XX_CP_BYPASS>;

            led@0 {
                reg = <0>;
                chan-name = "d1";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@1 {
                reg = <1>;
                chan-name = "d2";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@2 {
                reg = <2>;
                chan-name = "d3";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@3 {
                reg = <3>;
                chan-name = "d4";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@4 {
                reg = <4>;
                chan-name = "d5";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@5 {
                reg = <5>;
                chan-name = "d6";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@6 {
                reg = <6>;
                chan-name = "d7";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@7 {
                reg = <7>;
                chan-name = "d8";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };

            led@8 {
                reg = <8>;
                chan-name = "d9";
                led-cur = /bits/ 8 <0x14>;
                max-cur = /bits/ 8 <0x20>;
            };
        };

        led-controller@33 {
            #address-cells = <1>;
            #size-cells = <0>;
            compatible = "national,lp5523";
            reg = <0x33>;
            clock-mode = /bits/ 8 <0>;

            multi-led@2 {
                #address-cells = <1>;
                #size-cells = <0>;
                reg = <0x2>;
                color = <LED_COLOR_ID_RGB>;
                function = LED_FUNCTION_STANDBY;
                linux,default-trigger = "heartbeat";

                led@0 {
                    led-cur = /bits/ 8 <50>;
                    max-cur = /bits/ 8 <100>;
                    reg = <0x0>;
                    color = <LED_COLOR_ID_GREEN>;
                };

                led@1 {
                    led-cur = /bits/ 8 <50>;
                    max-cur = /bits/ 8 <100>;
                    reg = <0x1>;
                    color = <LED_COLOR_ID_BLUE>;
                };

                led@6 {
                    led-cur = /bits/ 8 <50>;
                    max-cur = /bits/ 8 <100>;
                    reg = <0x6>;
                    color = <LED_COLOR_ID_RED>;
                };
            };
        };
    };

...
