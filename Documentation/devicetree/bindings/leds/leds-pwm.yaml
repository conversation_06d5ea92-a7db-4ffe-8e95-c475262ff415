# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/leds-pwm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: LEDs connected to PWM

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Each LED is represented as a sub-node of the pwm-leds device.  Each
  node's name represents the name of the corresponding LED.

properties:
  compatible:
    const: pwm-leds

patternProperties:
  "^led(-[0-9a-f]+)?$":
    type: object
    $ref: common.yaml#
    unevaluatedProperties: false

    properties:
      pwms:
        maxItems: 1

      pwm-names: true

      max-brightness:
        description:
          Maximum brightness possible for the LED
        $ref: /schemas/types.yaml#/definitions/uint32

      active-low:
        description:
          For PWMs where the LED is wired to supply rather than ground.
        type: boolean

    required:
      - pwms
      - max-brightness

additionalProperties: false

examples:
  - |

    #include <dt-bindings/leds/common.h>

    led-controller {
        compatible = "pwm-leds";

        led-1 {
            label = "omap4::keypad";
            pwms = <&twl_pwm 0 7812500>;
            max-brightness = <127>;
        };

        led-2 {
            color = <LED_COLOR_ID_GREEN>;
            function = LED_FUNCTION_CHARGING;
            pwms = <&twl_pwmled 0 7812500>;
            max-brightness = <255>;
        };
    };

...
