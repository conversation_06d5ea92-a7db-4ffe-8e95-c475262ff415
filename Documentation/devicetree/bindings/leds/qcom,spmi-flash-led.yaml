# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/qcom,spmi-flash-led.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Flash LED device inside Qualcomm Technologies, Inc. PMICs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Flash LED controller is present inside some Qualcomm Technologies, Inc. PMICs.
  The flash LED module can have different number of LED channels supported
  e.g. 3 or 4. There are some different registers between them but they can
  both support maximum current up to 1.5 A per channel and they can also support
  ganging 2 channels together to supply maximum current up to 2 A. The current
  will be split symmetrically on each channel and they will be enabled and
  disabled at the same time.

properties:
  compatible:
    items:
      - enum:
          - qcom,pm6150l-flash-led
          - qcom,pm8150c-flash-led
          - qcom,pm8150l-flash-led
          - qcom,pm8350c-flash-led
          - qcom,pm8550-flash-led
          - qcom,pmi8998-flash-led
      - const: qcom,spmi-flash-led

  reg:
    maxItems: 1

patternProperties:
  "^led-[0-3]$":
    type: object
    $ref: common.yaml#
    unevaluatedProperties: false
    description:
      Represents the physical LED components which are connected to the
      flash LED channels' output.

    properties:
      led-sources:
        description:
          The HW indices of the flash LED channels that connect to the
          physical LED
        allOf:
          - minItems: 1
            maxItems: 2
            items:
              enum: [1, 2, 3, 4]

      led-max-microamp:
        anyOf:
          - minimum: 5000
            maximum: 500000
            multipleOf: 5000
          - minimum: 10000
            maximum: 1000000
            multipleOf: 10000

      flash-max-microamp:
        anyOf:
          - minimum: 12500
            maximum: 1500000
            multipleOf: 12500
          - minimum: 25000
            maximum: 2000000
            multipleOf: 25000

      flash-max-timeout-us:
        minimum: 10000
        maximum: 1280000
        multipleOf: 10000

    required:
      - led-sources
      - led-max-microamp

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/leds/common.h>
    spmi {
        #address-cells = <1>;
        #size-cells = <0>;
        led-controller@ee00 {
            compatible = "qcom,pm8350c-flash-led", "qcom,spmi-flash-led";
            reg = <0xee00>;

            led-0 {
                function = LED_FUNCTION_FLASH;
                color = <LED_COLOR_ID_WHITE>;
                led-sources = <1>, <4>;
                led-max-microamp = <300000>;
                flash-max-microamp = <2000000>;
                flash-max-timeout-us = <1280000>;
                function-enumerator = <0>;
            };

            led-1 {
                function = LED_FUNCTION_FLASH;
                color = <LED_COLOR_ID_YELLOW>;
                led-sources = <2>, <3>;
                led-max-microamp = <300000>;
                flash-max-microamp = <2000000>;
                flash-max-timeout-us = <1280000>;
                function-enumerator = <1>;
            };
        };
    };
