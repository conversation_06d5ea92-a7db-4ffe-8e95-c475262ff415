# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/backlight/pwm-backlight.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: pwm-backlight

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: pwm-backlight

  pwms:
    maxItems: 1

  pwm-names: true

  power-supply:
    description: regulator for supply voltage

  enable-gpios:
    description:
      Contains a single GPIO specifier for the GPIO which enables and disables
      the backlight.
    maxItems: 1

  post-pwm-on-delay-ms:
    description:
      Delay in ms between setting an initial (non-zero) PWM and enabling the
      backlight using GPIO.

  pwm-off-delay-ms:
    description:
      Delay in ms between disabling the backlight using GPIO and setting PWM
      value to 0.

  brightness-levels:
    description:
      Array of distinct brightness levels. Typically these are in the range
      from 0 to 255, but any range starting at 0 will do. The actual brightness
      level (PWM duty cycle) will be interpolated from these values. 0 means a
      0% duty cycle (darkest/off), while the last value in the array represents
      a 100% duty cycle (brightest).
    $ref: /schemas/types.yaml#/definitions/uint32-array

  default-brightness-level:
    description:
      The default brightness level (index into the array defined by the
      "brightness-levels" property).
    $ref: /schemas/types.yaml#/definitions/uint32

  num-interpolated-steps:
    description:
      Number of interpolated steps between each value of brightness-levels
      table. This way a high resolution pwm duty cycle can be used without
      having to list out every possible value in the brightness-level array.
    $ref: /schemas/types.yaml#/definitions/uint32

dependencies:
  default-brightness-level: [brightness-levels]
  num-interpolated-steps: [brightness-levels]

required:
  - compatible
  - pwms

additionalProperties: false

examples:
  - |
    backlight {
        compatible = "pwm-backlight";
        pwms = <&pwm 0 5000000>;

        brightness-levels = <0 4 8 16 32 64 128 255>;
        default-brightness-level = <6>;

        power-supply = <&vdd_bl_reg>;
        enable-gpios = <&gpio 58 0>;
        post-pwm-on-delay-ms = <10>;
        pwm-off-delay-ms = <10>;
    };

  - |
    // Example using num-interpolation-steps:
    backlight {
        compatible = "pwm-backlight";
        pwms = <&pwm 0 5000000>;

        brightness-levels = <0 2048 4096 8192 16384 65535>;
        num-interpolated-steps = <2048>;
        default-brightness-level = <4096>;

        power-supply = <&vdd_bl_reg>;
        enable-gpios = <&gpio 58 0>;
    };

...
