# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/leds/backlight/led-backlight.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: led-backlight

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description:
  This binding is used to describe a basic backlight device made of LEDs. It
  can also be used to describe a backlight device controlled by the output of
  a LED driver.

properties:
  compatible:
    const: led-backlight

  leds:
    description: A list of LED nodes
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      maxItems: 1

  brightness-levels:
    description:
      Array of distinct brightness levels. The levels must be in the range
      accepted by the underlying LED devices. This is used to translate a
      backlight brightness level into a LED brightness level. If it is not
      provided, the identity mapping is used.
    $ref: /schemas/types.yaml#/definitions/uint32-array

  default-brightness-level:
    description:
      The default brightness level (index into the array defined by the
      "brightness-levels" property).
    $ref: /schemas/types.yaml#/definitions/uint32

required:
  - compatible
  - leds

additionalProperties: false

examples:
  - |
    backlight {
        compatible = "led-backlight";

        leds = <&led1>, <&led2>;
        brightness-levels = <0 4 8 16 32 64 128 255>;
        default-brightness-level = <6>;
    };

...
