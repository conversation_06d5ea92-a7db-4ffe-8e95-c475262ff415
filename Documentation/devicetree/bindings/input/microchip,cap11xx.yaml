# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/microchip,cap11xx.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Microchip CAP11xx based capacitive touch sensors

description: |
  The Microchip CAP1xxx Family of RightTouchTM multiple-channel capacitive
  touch controllers and LED drivers. The device communication via I2C only.

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - microchip,cap1106
      - microchip,cap1126
      - microchip,cap1188
      - microchip,cap1203
      - microchip,cap1206
      - microchip,cap1293
      - microchip,cap1298

  reg:
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  interrupts:
    maxItems: 1
    description: |
      Property describing the interrupt line the
      device's ALERT#/CM_IRQ# pin is connected to.
      The device only has one interrupt source.

  autorepeat:
    description: |
      Enables the Linux input system's autorepeat feature on the input device.

  linux,keycodes:
    minItems: 6
    maxItems: 6
    description: |
      Specifies an array of numeric keycode values to
      be used for the channels. If this property is
      omitted, KEY_A, KEY_B, etc are used as defaults.
      The array must have exactly six entries.

  microchip,sensor-gain:
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 1
    enum: [1, 2, 4, 8]
    description: |
      Defines the gain of the sensor circuitry. This
      effectively controls the sensitivity, as a
      smaller delta capacitance is required to
      generate the same delta count values.

  microchip,irq-active-high:
    type: boolean
    description: |
      By default the interrupt pin is active low
      open drain. This property allows using the active
      high push-pull output.

patternProperties:
  "^led@[0-7]$":
    type: object
    description: CAP11xx LEDs
    $ref: /schemas/leds/common.yaml#

    properties:
      reg:
        enum: [0, 1, 2, 3, 4, 5, 6, 7]

      label: true

      linux,default-trigger: true

      default-state: true

    required:
      - reg

    additionalProperties: false

allOf:
  - $ref: input.yaml
  - if:
      properties:
        compatible:
          contains:
            enum:
              - microchip,cap1106
    then:
      patternProperties:
        "^led@[0-7]$": false

required:
  - compatible
  - interrupts

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      touch@28 {
        compatible = "microchip,cap1188";
        interrupt-parent = <&gpio1>;
        interrupts = <0 0>;
        reg = <0x28>;
        autorepeat;
        microchip,sensor-gain = <2>;

        linux,keycodes = <103>,	/* KEY_UP */
                         <106>,	/* KEY_RIGHT */
                         <108>,	/* KEY_DOWN */
                         <105>,	/* KEY_LEFT */
                         <109>,	/* KEY_PAGEDOWN */
                         <104>;	/* KEY_PAGEUP */

        #address-cells = <1>;
        #size-cells = <0>;

        led@0 {
                label = "cap11xx:green:usr0";
                reg = <0>;
        };

        led@1 {
                label = "cap11xx:green:usr1";
                reg = <1>;
        };

        led@2 {
                label = "cap11xx:green:alive";
                reg = <2>;
                linux,default-trigger = "heartbeat";
        };
      };
    };
