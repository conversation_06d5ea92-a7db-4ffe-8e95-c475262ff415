# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/elan,ekth6915.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON><PERSON> eKTH6915 touchscreen controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  Supports the Elan eKTH6915 touchscreen controller.
  This touchscreen controller uses the i2c-hid protocol with a reset GPIO.

allOf:
  - $ref: /schemas/input/touchscreen/touchscreen.yaml#

properties:
  compatible:
    items:
      - const: elan,ekth6915

  reg:
    const: 0x10

  interrupts:
    maxItems: 1

  panel: true

  reset-gpios:
    description: Reset GPIO; not all touchscreens using eKTH6915 hook this up.

  vcc33-supply:
    description: The 3.3V supply to the touchscreen.

  vccio-supply:
    description:
      The IO supply to the touchscreen. Need not be specified if this is the
      same as the 3.3V supply.

required:
  - compatible
  - reg
  - interrupts
  - vcc33-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      ap_ts: touchscreen@10 {
        compatible = "elan,ekth6915";
        reg = <0x10>;

        interrupt-parent = <&tlmm>;
        interrupts = <9 IRQ_TYPE_LEVEL_LOW>;

        reset-gpios = <&tlmm 8 GPIO_ACTIVE_LOW>;
        vcc33-supply = <&pp3300_ts>;
      };
    };
