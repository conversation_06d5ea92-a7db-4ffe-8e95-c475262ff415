# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/azoteq,iqs7222.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Azoteq IQS7222A/B/C/D Capacitive Touch Controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Azoteq IQS7222A, IQS7222B, IQS7222C and IQS7222D are multichannel
  capacitive touch controllers that feature additional sensing capabilities.

  Link to datasheets: https://www.azoteq.com/

properties:
  compatible:
    enum:
      - azoteq,iqs7222a
      - azoteq,iqs7222b
      - azoteq,iqs7222c
      - azoteq,iqs7222d

  reg:
    maxItems: 1

  irq-gpios:
    maxItems: 1
    description:
      Specifies the GPIO connected to the device's active-low RDY output.

  reset-gpios:
    maxItems: 1
    description:
      Specifies the GPIO connected to the device's active-low MCLR input. The
      device is temporarily held in hardware reset prior to initialization if
      this property is present.

  azoteq,max-counts:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]
    description: |
      Specifies the maximum number of conversion periods (counts) that can be
      reported as follows:
      0: 1023
      1: 2047
      2: 4095
      3: 16384

  azoteq,auto-mode:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3]
    description: |
      Specifies the number of conversions to occur before an interrupt is
      generated as follows:
      0: 4
      1: 8
      2: 16
      3: 32

  azoteq,ati-frac-div-fine:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 31
    description: Specifies the preloaded ATI fine fractional divider.

  azoteq,ati-frac-div-coarse:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 31
    description: Specifies the preloaded ATI coarse fractional divider.

  azoteq,ati-comp-select:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 1023
    description: Specifies the preloaded ATI compensation selection.

  azoteq,lta-beta-lp:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Specifies the long-term average filter damping factor to be applied during
      low-power mode.

  azoteq,lta-beta-np:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Specifies the long-term average filter damping factor to be applied during
      normal-power mode.

  azoteq,counts-beta-lp:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Specifies the counts filter damping factor to be applied during low-power
      mode.

  azoteq,counts-beta-np:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Specifies the counts filter damping factor to be applied during normal-
      power mode.

  azoteq,lta-fast-beta-lp:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Specifies the long-term average filter fast damping factor to be applied
      during low-power mode.

  azoteq,lta-fast-beta-np:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Specifies the long-term average filter fast damping factor to be applied
      during normal-power mode.

  azoteq,timeout-ati-ms:
    multipleOf: 500
    minimum: 0
    maximum: 32767500
    description:
      Specifies the delay (in ms) before ATI is retried following an ATI error.

  azoteq,rate-ati-ms:
    minimum: 0
    maximum: 65535
    description: Specifies the rate (in ms) at which ATI status is evaluated.

  azoteq,timeout-np-ms:
    minimum: 0
    maximum: 65535
    description:
      Specifies the length of time (in ms) to wait for an event before moving
      from normal-power mode to low-power mode.

  azoteq,rate-np-ms:
    minimum: 0
    maximum: 3000
    description: Specifies the report rate (in ms) during normal-power mode.

  azoteq,timeout-lp-ms:
    minimum: 0
    maximum: 65535
    description:
      Specifies the length of time (in ms) to wait for an event before moving
      from low-power mode to ultra-low-power mode.

  azoteq,rate-lp-ms:
    minimum: 0
    maximum: 3000
    description: Specifies the report rate (in ms) during low-power mode.

  azoteq,timeout-ulp-ms:
    minimum: 0
    maximum: 65535
    description:
      Specifies the rate (in ms) at which channels not regularly sampled during
      ultra-low-power mode are updated.

  azoteq,rate-ulp-ms:
    minimum: 0
    maximum: 3000
    description: Specifies the report rate (in ms) during ultra-low-power mode.

  touchscreen-size-x: true
  touchscreen-size-y: true
  touchscreen-inverted-x: true
  touchscreen-inverted-y: true
  touchscreen-swapped-x-y: true

  trackpad:
    type: object
    description: Represents all channels associated with the trackpad.

    properties:
      azoteq,channel-select:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 12
        items:
          minimum: 0
          maximum: 13
        description:
          Specifies the order of the channels that participate in the trackpad.
          Specify 255 to omit a given channel for the purpose of mapping a non-
          rectangular trackpad.

      azoteq,num-rows:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 1
        maximum: 12
        description: Specifies the number of rows that comprise the trackpad.

      azoteq,num-cols:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 1
        maximum: 12
        description: Specifies the number of columns that comprise the trackpad.

      azoteq,top-speed:
        $ref: /schemas/types.yaml#/definitions/uint32
        multipleOf: 4
        minimum: 0
        maximum: 1020
        description:
          Specifies the speed (in coordinates traveled per conversion) after
          which coordinate filtering is no longer applied.

      azoteq,bottom-speed:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description:
          Specifies the speed (in coordinates traveled per conversion) after
          which coordinate filtering is linearly reduced.

      azoteq,use-prox:
        type: boolean
        description:
          Directs the trackpad to respond to the proximity states of the
          selected channels instead of their corresponding touch states.
          Note the trackpad cannot report granular coordinates during a
          state of proximity.

    patternProperties:
      "^azoteq,lower-cal-(x|y)$":
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description: Specifies the trackpad's lower starting points.

      "^azoteq,upper-cal-(x|y)$":
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description: Specifies the trackpad's upper starting points.

      "^event-(press|tap|(swipe|flick)-(x|y)-(pos|neg))$":
        type: object
        $ref: input.yaml#
        description:
          Represents a press or gesture event reported by the trackpad. Specify
          'linux,code' under the press event to report absolute coordinates.

        properties:
          linux,code: true

          azoteq,gesture-angle-tighten:
            type: boolean
            description:
              Limits the tangent of the gesture angle to 0.5 (axial gestures
              only). If specified in one direction, the effect is applied in
              either direction.

          azoteq,gesture-max-ms:
            multipleOf: 16
            minimum: 0
            maximum: 4080
            description:
              Specifies the length of time (in ms) within which a tap, swipe
              or flick gesture must be completed in order to be acknowledged
              by the device. The number specified for any one swipe or flick
              gesture applies to all other swipe or flick gestures.

          azoteq,gesture-min-ms:
            multipleOf: 16
            minimum: 0
            maximum: 4080
            description:
              Specifies the length of time (in ms) for which a tap gesture must
              be held in order to be acknowledged by the device.

          azoteq,gesture-dist:
            $ref: /schemas/types.yaml#/definitions/uint32
            minimum: 0
            maximum: 65535
            description:
              Specifies the distance (in coordinates) across which a swipe or
              flick gesture must travel in order to be acknowledged by the
              device. The number specified for any one swipe or flick gesture
              applies to all remaining swipe or flick gestures.

              For tap gestures, this property specifies the distance from the
              original point of contact across which the contact is permitted
              to travel before the gesture is rejected by the device.

          azoteq,gpio-select:
            $ref: /schemas/types.yaml#/definitions/uint32-array
            minItems: 1
            maxItems: 3
            items:
              minimum: 0
              maximum: 2
            description: |
              Specifies one or more GPIO mapped to the event as follows:
              0: GPIO0
              1: GPIO3
              2: GPIO4

              Note that although multiple events can be mapped to a single
              GPIO, they must all be of the same type (proximity, touch or
              trackpad gesture).

        additionalProperties: false

    required:
      - azoteq,channel-select

    additionalProperties: false

patternProperties:
  "^cycle-[0-9]$":
    type: object
    description: Represents a conversion cycle serving two sensing channels.

    properties:
      azoteq,conv-period:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description: Specifies the cycle's conversion period.

      azoteq,conv-frac:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description: Specifies the cycle's conversion frequency fraction.

      azoteq,tx-enable:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 9
        items:
          minimum: 0
          maximum: 8
        description: Specifies the CTx pin(s) associated with the cycle.

      azoteq,rx-float-inactive:
        type: boolean
        description: Floats any inactive CRx pins instead of grounding them.

      azoteq,dead-time-enable:
        type: boolean
        description:
          Increases the denominator of the conversion frequency formula by one.

      azoteq,tx-freq-fosc:
        type: boolean
        description:
          Fixes the conversion frequency to that of the device's core clock.

      azoteq,vbias-enable:
        type: boolean
        description: Enables the bias voltage for use during inductive sensing.

      azoteq,sense-mode:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2, 3]
        description: |
          Specifies the cycle's sensing mode as follows:
          0: None
          1: Self capacitive
          2: Mutual capacitive
          3: Inductive

          Note that in the case of IQS7222A, cycles 5 and 6 are restricted to
          Hall-effect sensing.

      azoteq,iref-enable:
        type: boolean
        description:
          Enables the current reference for use during various sensing modes.

      azoteq,iref-level:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 15
        description: Specifies the cycle's current reference level.

      azoteq,iref-trim:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 15
        description: Specifies the cycle's current reference trim.

    dependencies:
      azoteq,iref-level: ["azoteq,iref-enable"]
      azoteq,iref-trim: ["azoteq,iref-enable"]

    additionalProperties: false

  "^channel-([0-9]|1[0-9])$":
    type: object
    description:
      Represents a single sensing channel. A channel is active if defined and
      inactive otherwise.

      Note that in the case of IQS7222A, channels 10 and 11 are restricted to
      Hall-effect sensing with events reported on channel 10 only.

    properties:
      azoteq,ulp-allow:
        type: boolean
        description:
          Permits the device to enter ultra-low-power mode while the channel
          lies in a state of touch or proximity.

      azoteq,ref-select:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 9
        description: Specifies a separate reference channel to be followed.

      azoteq,ref-weight:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 65535
        description: Specifies the relative weight of the reference channel.

      azoteq,use-prox:
        type: boolean
        description:
          Activates the reference channel in response to proximity events
          instead of touch events.

      azoteq,counts-filt-enable:
        type: boolean
        description: Applies counts filtering to the channel.

      azoteq,ati-band:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2, 3]
        description: |
          Specifies the channel's ATI band as a fraction of its ATI target as
          follows:
          0: 1/16
          1: 1/8
          2: 1/4
          3: 1/2

      azoteq,global-halt:
        type: boolean
        description:
          Specifies that the channel's long-term average is to freeze if any
          other participating channel lies in a proximity or touch state.

      azoteq,invert-enable:
        type: boolean
        description:
          Inverts the polarity of the states reported for proximity and touch
          events relative to their respective thresholds.

      azoteq,dual-direction:
        type: boolean
        description:
          Specifies that the channel's long-term average is to freeze in the
          presence of either increasing or decreasing counts, thereby permit-
          ting events to be reported in either direction.

      azoteq,rx-enable:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 4
        items:
          minimum: 0
          maximum: 7
        description: Specifies the CRx pin(s) associated with the channel.

      azoteq,samp-cap-double:
        type: boolean
        description: Doubles the sampling capacitance from 40 pF to 80 pF.

      azoteq,vref-half:
        type: boolean
        description: Halves the discharge threshold from 1.0 V to 0.5 V.

      azoteq,proj-bias:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2, 3]
        description: |
          Specifies the bias current applied during mutual (projected)
          capacitive sensing as follows:
          0: 2 uA
          1: 5 uA
          2: 7 uA
          3: 10 uA

      azoteq,ati-target:
        $ref: /schemas/types.yaml#/definitions/uint32
        multipleOf: 8
        minimum: 0
        maximum: 2040
        description: Specifies the channel's ATI target.

      azoteq,ati-base:
        $ref: /schemas/types.yaml#/definitions/uint32
        multipleOf: 16
        minimum: 0
        maximum: 496
        description: Specifies the channel's ATI base.

      azoteq,ati-mode:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2, 3, 4, 5]
        description: |
          Specifies the channel's ATI mode as follows:
          0: Disabled
          1: Compensation
          2: Compensation divider
          3: Fine fractional divider
          4: Coarse fractional divider
          5: Full

      azoteq,ati-frac-div-fine:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 31
        description: Specifies the channel's ATI fine fractional divider.

      azoteq,ati-frac-mult-coarse:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 15
        description: Specifies the channel's ATI coarse fractional multiplier.

      azoteq,ati-frac-div-coarse:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 31
        description: Specifies the channel's ATI coarse fractional divider.

      azoteq,ati-comp-div:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 31
        description: Specifies the channel's ATI compensation divider.

      azoteq,ati-comp-select:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 1023
        description: Specifies the channel's ATI compensation selection.

      azoteq,debounce-enter:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 15
        description: Specifies the channel's debounce entrance factor.

      azoteq,debounce-exit:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 15
        description: Specifies the channel's debounce exit factor.

    patternProperties:
      "^event-(prox|touch)$":
        type: object
        $ref: input.yaml#
        description:
          Represents a proximity or touch event reported by the channel.

        properties:
          azoteq,gpio-select:
            $ref: /schemas/types.yaml#/definitions/uint32-array
            minItems: 1
            maxItems: 3
            items:
              minimum: 0
              maximum: 2
            description: |
              Specifies one or more GPIO mapped to the event as follows:
              0: GPIO0
              1: GPIO3
              2: GPIO4

              Note that although multiple events can be mapped to a single
              GPIO, they must all be of the same type (proximity, touch or
              slider/trackpad gesture).

          azoteq,thresh:
            $ref: /schemas/types.yaml#/definitions/uint32
            description:
              Specifies the threshold for the event. Valid entries range from
              0-127 and 0-255 for proximity and touch events, respectively.

          azoteq,hyst:
            $ref: /schemas/types.yaml#/definitions/uint32
            minimum: 0
            maximum: 255
            description:
              Specifies the hysteresis for the event (touch events only).

          azoteq,timeout-press-ms:
            multipleOf: 500
            minimum: 0
            maximum: 127500
            description:
              Specifies the length of time (in ms) to wait before automatically
              releasing a press event. Specify zero to allow the press state to
              persist indefinitely.

              The IQS7222B does not feature channel-specific timeouts; the time-
              out specified for any one channel applies to all channels.

          linux,code: true

          linux,input-type:
            enum: [1, 5]
            default: 1
            description:
              Specifies whether the event is to be interpreted as a key (1)
              or a switch (5).

        additionalProperties: false

    dependencies:
      azoteq,ref-weight: ["azoteq,ref-select"]
      azoteq,use-prox: ["azoteq,ref-select"]

    additionalProperties: false

  "^slider-[0-1]$":
    type: object
    description: Represents a slider comprising three or four channels.

    properties:
      azoteq,channel-select:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 3
        maxItems: 4
        items:
          minimum: 0
          maximum: 9
        description:
          Specifies the order of the channels that participate in the slider.

      azoteq,slider-size:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 1
        maximum: 65535
        description:
          Specifies the slider's one-dimensional resolution, equal to the
          maximum coordinate plus one.

      azoteq,lower-cal:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description: Specifies the slider's lower starting point.

      azoteq,upper-cal:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description: Specifies the slider's upper starting point.

      azoteq,top-speed:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 65535
        description:
          Specifies the speed (in coordinates traveled per conversion) after
          which coordinate filtering is no longer applied.

      azoteq,bottom-speed:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 255
        description:
          Specifies the speed (in coordinates traveled per conversion) after
          which coordinate filtering is linearly reduced.

      azoteq,bottom-beta:
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 7
        description:
          Specifies the coordinate filter damping factor to be applied
          while the speed of movement is below that which is specified
          by azoteq,bottom-speed.

      azoteq,static-beta:
        type: boolean
        description:
          Applies the coordinate filter damping factor specified by
          azoteq,bottom-beta regardless of the speed of movement.

      azoteq,use-prox:
        type: boolean
        description:
          Directs the slider to respond to the proximity states of the selected
          channels instead of their corresponding touch states. Note the slider
          cannot report granular coordinates during a state of proximity.

      linux,axis:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Specifies the absolute axis to which coordinates are mapped. Specify
          ABS_WHEEL to operate the slider as a wheel (IQS7222C only).

    patternProperties:
      "^event-(press|tap|(swipe|flick)-(pos|neg))$":
        type: object
        $ref: input.yaml#
        description:
          Represents a press or gesture (IQS7222A only) event reported by
          the slider.

        properties:
          linux,code: true

          azoteq,gesture-max-ms:
            multipleOf: 16
            minimum: 0
            maximum: 4080
            description:
              Specifies the length of time (in ms) within which a tap, swipe
              or flick gesture must be completed in order to be acknowledged
              by the device. The number specified for any one swipe or flick
              gesture applies to all remaining swipe or flick gestures.

          azoteq,gesture-min-ms:
            multipleOf: 16
            minimum: 0
            maximum: 496
            description:
              Specifies the length of time (in ms) for which a tap gesture must
              be held in order to be acknowledged by the device.

          azoteq,gesture-dist:
            $ref: /schemas/types.yaml#/definitions/uint32
            multipleOf: 16
            minimum: 0
            maximum: 4080
            description:
              Specifies the distance (in coordinates) across which a swipe or
              flick gesture must travel in order to be acknowledged by the
              device. The number specified for any one swipe or flick gesture
              applies to all remaining swipe or flick gestures.

          azoteq,gpio-select:
            $ref: /schemas/types.yaml#/definitions/uint32-array
            minItems: 1
            maxItems: 3
            items:
              minimum: 0
              maximum: 2
            description: |
              Specifies one or more GPIO mapped to the event as follows:
              0: GPIO0
              1: GPIO3
              2: GPIO4

              Note that although multiple events can be mapped to a single
              GPIO, they must all be of the same type (proximity, touch or
              slider gesture).

        additionalProperties: false

    required:
      - azoteq,channel-select

    additionalProperties: false

  "^gpio-[0-2]$":
    type: object
    description: |
      Represents a GPIO mapped to one or more events as follows:
      gpio-0: GPIO0
      gpio-1: GPIO3
      gpio-2: GPIO4

    allOf:
      - $ref: ../pinctrl/pincfg-node.yaml#

    properties:
      drive-open-drain: true

    additionalProperties: false

allOf:
  - $ref: touchscreen/touchscreen.yaml#

  - if:
      properties:
        compatible:
          contains:
            enum:
              - azoteq,iqs7222a
              - azoteq,iqs7222b
              - azoteq,iqs7222c

    then:
      properties:
        touchscreen-size-x: false
        touchscreen-size-y: false
        touchscreen-inverted-x: false
        touchscreen-inverted-y: false
        touchscreen-swapped-x-y: false

        trackpad: false

      patternProperties:
        "^channel-([0-9]|1[0-9])$":
          properties:
            azoteq,counts-filt-enable: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - azoteq,iqs7222b
              - azoteq,iqs7222c

    then:
      patternProperties:
        "^channel-([0-9]|1[0-9])$":
          properties:
            azoteq,ulp-allow: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - azoteq,iqs7222b
              - azoteq,iqs7222d

    then:
      patternProperties:
        "^cycle-[0-9]$":
          properties:
            azoteq,iref-enable: false

        "^channel-([0-9]|1[0-9])$":
          properties:
            azoteq,ref-select: false

        "^slider-[0-1]$": false

  - if:
      properties:
        compatible:
          contains:
            const: azoteq,iqs7222b

    then:
      patternProperties:
        "^channel-([0-9]|1[0-9])$":
          patternProperties:
            "^event-(prox|touch)$":
              properties:
                azoteq,gpio-select: false

        "^gpio-[0-2]$": false

  - if:
      properties:
        compatible:
          contains:
            const: azoteq,iqs7222a

    then:
      patternProperties:
        "^channel-([0-9]|1[0-9])$":
          patternProperties:
            "^event-(prox|touch)$":
              properties:
                azoteq,gpio-select:
                  maxItems: 1
                  items:
                    maximum: 0

        "^slider-[0-1]$":
          properties:
            azoteq,slider-size:
              multipleOf: 16
              minimum: 16
              maximum: 4080

            azoteq,top-speed:
              multipleOf: 4
              maximum: 1020

          patternProperties:
            "^event-(press|tap|(swipe|flick)-(pos|neg))$":
              properties:
                azoteq,gpio-select:
                  maxItems: 1
                  items:
                    maximum: 0

    else:
      patternProperties:
        "^slider-[0-1]$":
          patternProperties:
            "^event-(press|tap|(swipe|flick)-(pos|neg))$":
              properties:
                azoteq,gesture-max-ms: false

                azoteq,gesture-min-ms: false

                azoteq,gesture-dist: false

required:
  - compatible
  - reg
  - irq-gpios

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/input/input.h>

    i2c {
            #address-cells = <1>;
            #size-cells = <0>;

            iqs7222a@44 {
                    compatible = "azoteq,iqs7222a";
                    reg = <0x44>;
                    irq-gpios = <&gpio 4 GPIO_ACTIVE_LOW>;
                    azoteq,lta-beta-lp = <7>;
                    azoteq,lta-beta-np = <8>;
                    azoteq,counts-beta-lp = <2>;
                    azoteq,counts-beta-np = <3>;
                    azoteq,lta-fast-beta-lp = <3>;
                    azoteq,lta-fast-beta-np = <4>;

                    cycle-0 {
                            azoteq,conv-period = <5>;
                            azoteq,conv-frac = <127>;
                            azoteq,tx-enable = <1>, <2>, <4>, <5>;
                            azoteq,dead-time-enable;
                            azoteq,sense-mode = <2>;
                    };

                    cycle-1 {
                            azoteq,conv-period = <5>;
                            azoteq,conv-frac = <127>;
                            azoteq,tx-enable = <5>;
                            azoteq,dead-time-enable;
                            azoteq,sense-mode = <2>;
                    };

                    cycle-2 {
                            azoteq,conv-period = <5>;
                            azoteq,conv-frac = <127>;
                            azoteq,tx-enable = <4>;
                            azoteq,dead-time-enable;
                            azoteq,sense-mode = <2>;
                    };

                    cycle-3 {
                            azoteq,conv-period = <5>;
                            azoteq,conv-frac = <127>;
                            azoteq,tx-enable = <2>;
                            azoteq,dead-time-enable;
                            azoteq,sense-mode = <2>;
                    };

                    cycle-4 {
                            azoteq,conv-period = <5>;
                            azoteq,conv-frac = <127>;
                            azoteq,tx-enable = <1>;
                            azoteq,dead-time-enable;
                            azoteq,sense-mode = <2>;
                    };

                    cycle-5 {
                            azoteq,conv-period = <2>;
                            azoteq,conv-frac = <0>;
                    };

                    cycle-6 {
                            azoteq,conv-period = <2>;
                            azoteq,conv-frac = <0>;
                    };

                    channel-0 {
                            azoteq,ulp-allow;
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <3>;
                            azoteq,ati-target = <800>;
                            azoteq,ati-base = <208>;
                            azoteq,ati-mode = <5>;
                    };

                    channel-1 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <3>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <208>;
                            azoteq,ati-mode = <5>;
                    };

                    channel-2 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <3>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <208>;
                            azoteq,ati-mode = <5>;
                    };

                    channel-3 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <3>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <208>;
                            azoteq,ati-mode = <5>;
                    };

                    channel-4 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <3>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <208>;
                            azoteq,ati-mode = <5>;
                    };

                    channel-5 {
                            azoteq,ulp-allow;
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <6>;
                            azoteq,ati-target = <800>;
                            azoteq,ati-base = <144>;
                            azoteq,ati-mode = <5>;
                    };

                    channel-6 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <6>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <160>;
                            azoteq,ati-mode = <5>;

                            event-touch {
                                    linux,code = <KEY_MUTE>;
                            };
                    };

                    channel-7 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <6>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <160>;
                            azoteq,ati-mode = <5>;

                            event-touch {
                                    linux,code = <KEY_VOLUMEDOWN>;
                            };
                    };

                    channel-8 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <6>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <160>;
                            azoteq,ati-mode = <5>;

                            event-touch {
                                    linux,code = <KEY_VOLUMEUP>;
                            };
                    };

                    channel-9 {
                            azoteq,global-halt;
                            azoteq,invert-enable;
                            azoteq,rx-enable = <6>;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <160>;
                            azoteq,ati-mode = <5>;

                            event-touch {
                                    linux,code = <KEY_POWER>;
                            };
                    };

                    channel-10 {
                            azoteq,ulp-allow;
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <112>;

                            event-touch {
                                    linux,code = <SW_LID>;
                                    linux,input-type = <EV_SW>;
                            };
                    };

                    channel-11 {
                            azoteq,ati-target = <496>;
                            azoteq,ati-base = <112>;
                    };

                    slider-0 {
                            azoteq,channel-select = <1>, <2>, <3>, <4>;
                            azoteq,slider-size = <4080>;
                            azoteq,upper-cal = <50>;
                            azoteq,lower-cal = <30>;
                            azoteq,top-speed = <200>;
                            azoteq,bottom-speed = <1>;
                            azoteq,bottom-beta = <3>;

                            event-tap {
                                    linux,code = <KEY_PLAYPAUSE>;
                                    azoteq,gesture-max-ms = <400>;
                                    azoteq,gesture-min-ms = <32>;
                            };

                            event-flick-pos {
                                    linux,code = <KEY_NEXTSONG>;
                                    azoteq,gesture-max-ms = <800>;
                                    azoteq,gesture-dist = <800>;
                            };

                            event-flick-neg {
                                    linux,code = <KEY_PREVIOUSSONG>;
                            };
                    };
            };
    };

...
