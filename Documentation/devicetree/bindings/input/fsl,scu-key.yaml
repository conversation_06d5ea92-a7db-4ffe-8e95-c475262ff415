# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/fsl,scu-key.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: i.MX SCU Client Device Node - SCU Key Based on SCU Message Protocol

maintainers:
  - <PERSON> Aisheng <<EMAIL>>

description: i.MX SCU Client Device Node
  Client nodes are maintained as children of the relevant IMX-SCU device node.

allOf:
  - $ref: input.yaml#

properties:
  compatible:
    items:
      - const: fsl,imx8qxp-sc-key
      - const: fsl,imx-sc-key

  linux,keycodes:
    maxItems: 1

required:
  - compatible
  - linux,keycodes

additionalProperties: false

examples:
  - |
    #include <dt-bindings/input/input.h>

    keys {
        compatible = "fsl,imx8qxp-sc-key", "fsl,imx-sc-key";
        linux,keycodes = <KEY_POWER>;
    };
