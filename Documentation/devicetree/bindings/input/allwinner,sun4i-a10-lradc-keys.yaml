# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/allwinner,sun4i-a10-lradc-keys.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A10 LRADC

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: allwinner,sun4i-a10-lradc-keys
      - const: allwinner,sun8i-a83t-r-lradc
      - items:
          - enum:
              - allwinner,suniv-f1c100s-lradc
              - allwinner,sun50i-a64-lradc
          - const: allwinner,sun8i-a83t-r-lradc
      - const: allwinner,sun50i-r329-lradc
      - items:
          - const: allwinner,sun20i-d1-lradc
          - const: allwinner,sun50i-r329-lradc

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  resets:
    maxItems: 1

  interrupts:
    maxItems: 1

  vref-supply:
    description:
      Regulator for the LRADC reference voltage

  wakeup-source: true

patternProperties:
  "^button-[0-9]+$":
    type: object
    $ref: input.yaml#
    properties:
      label:
        $ref: /schemas/types.yaml#/definitions/string
        description: Descriptive name of the key

      linux,code: true

      channel:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1]
        description: ADC Channel this key is attached to

      voltage:
        $ref: /schemas/types.yaml#/definitions/uint32
        description:
          Voltage in microvolts at LRADC input when this key is
          pressed

    required:
      - label
      - linux,code
      - channel
      - voltage

    additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - vref-supply

if:
  properties:
    compatible:
      contains:
        enum:
          - allwinner,sun50i-r329-lradc

then:
  required:
    - clocks
    - resets

additionalProperties: false

examples:
  - |
    lradc: lradc@1c22800 {
        compatible = "allwinner,sun4i-a10-lradc-keys";
        reg = <0x01c22800 0x100>;
        interrupts = <31>;
        vref-supply = <&reg_vcc3v0>;

        button-191 {
            label = "Volume Up";
            linux,code = <115>;
            channel = <0>;
            voltage = <191274>;
        };

        button-392 {
            label = "Volume Down";
            linux,code = <114>;
            channel = <0>;
            voltage = <392644>;
        };
    };

...
