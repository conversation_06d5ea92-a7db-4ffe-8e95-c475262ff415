# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/touchscreen/cypress,tt21000.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Cypress TT21000 touchscreen controller

description: The Cypress TT21000 series (also known as "CYTTSP5" after
  the marketing name Cypress TrueTouch Standard Product series 5).

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: touchscreen.yaml#

properties:
  compatible:
    const: cypress,tt21000

  reg:
    maxItems: 1

  '#address-cells':
    const: 1

  '#size-cells':
    const: 0

  interrupts:
    maxItems: 1

  vdd-supply:
    description: Regulator for voltage.

  reset-gpios:
    maxItems: 1

  linux,keycodes:
    description: EV_ABS specific event code generated by the axis.

  wakeup-source: true

patternProperties:
  "^button@[0-9]+$":
    type: object
    $ref: ../input.yaml#
    properties:
      reg:
        maxItems: 1
      linux,keycodes:
        description: Keycode to emit

    required:
      - reg
      - linux,keycodes

    additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - vdd-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/input/linux-event-codes.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        touchscreen@24 {
            #address-cells = <1>;
            #size-cells = <0>;

            compatible = "cypress,tt21000";
            reg = <0x24>;
            pinctrl-names = "default";
            pinctrl-0 = <&tp_reset_ds203>;
            interrupt-parent = <&pio>;
            interrupts = <1 5 IRQ_TYPE_LEVEL_LOW>;
            reset-gpios = <&pio 7 1 GPIO_ACTIVE_LOW>;
            vdd-supply = <&reg_touch>;

            button@0 {
                reg = <0>;
                linux,keycodes = <KEY_HOMEPAGE>;
            };

            button@1 {
                reg = <1>;
                linux,keycodes = <KEY_MENU>;
            };

            button@2 {
                reg = <2>;
                linux,keycodes = <KEY_BACK>;
            };
        };
    };
...
