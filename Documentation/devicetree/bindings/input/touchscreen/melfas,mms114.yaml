# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/touchscreen/melfas,mms114.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON><PERSON><PERSON> MMS114 family touchscreen controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: touchscreen.yaml#

properties:
  $nodename:
    pattern: "^touchscreen(@.*)?$"

  compatible:
    items:
      - enum:
          - melfas,mms114
          - melfas,mms134s
          - melfas,mms136
          - melfas,mms152
          - melfas,mms345l

  reg:
    description: I2C address

  clock-frequency:
    description: I2C client clock frequency, defined for host
    minimum: 100000
    maximum: 400000

  interrupts:
    maxItems: 1

  avdd-supply:
    description: Analog power supply regulator on AVDD pin

  vdd-supply:
    description: Digital power supply regulator on VDD pin

  touchscreen-size-x: true
  touchscreen-size-y: true
  touchscreen-fuzz-x: true
  touchscreen-fuzz-y: true
  touchscreen-fuzz-pressure: true
  touchscreen-inverted-x: true
  touchscreen-inverted-y: true
  touchscreen-swapped-x-y: true
  touchscreen-max-pressure: true

  linux,keycodes:
    description: Keycodes for the touch keys
    minItems: 1
    maxItems: 15

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - touchscreen-size-x
  - touchscreen-size-y

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      touchscreen@48 {
        compatible = "melfas,mms114";
        reg = <0x48>;
        interrupt-parent = <&gpio>;
        interrupts = <39 IRQ_TYPE_EDGE_FALLING>;
        avdd-supply = <&ldo1_reg>;
        vdd-supply = <&ldo2_reg>;
        touchscreen-size-x = <720>;
        touchscreen-size-y = <1280>;
        touchscreen-fuzz-x = <10>;
        touchscreen-fuzz-y = <10>;
        touchscreen-fuzz-pressure = <10>;
        touchscreen-inverted-x;
        touchscreen-inverted-y;
      };
    };

...
