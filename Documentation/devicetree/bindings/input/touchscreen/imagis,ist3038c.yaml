# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/touchscreen/imagis,ist3038c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Imagis IST30XXC family touchscreen controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: touchscreen.yaml#

properties:
  $nodename:
    pattern: "^touchscreen@[0-9a-f]+$"

  compatible:
    enum:
      - imagis,ist3038c

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  vdd-supply:
    description: Power supply regulator for the chip

  vddio-supply:
    description: Power supply regulator for the I2C bus

  touchscreen-size-x: true
  touchscreen-size-y: true
  touchscreen-fuzz-x: true
  touchscreen-fuzz-y: true
  touchscreen-inverted-x: true
  touchscreen-inverted-y: true
  touchscreen-swapped-x-y: true

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - touchscreen-size-x
  - touchscreen-size-y

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      touchscreen@50 {
        compatible = "imagis,ist3038c";
        reg = <0x50>;
        interrupt-parent = <&gpio>;
        interrupts = <13 IRQ_TYPE_EDGE_FALLING>;
        vdd-supply = <&ldo1_reg>;
        vddio-supply = <&ldo2_reg>;
        touchscreen-size-x = <720>;
        touchscreen-size-y = <1280>;
        touchscreen-fuzz-x = <10>;
        touchscreen-fuzz-y = <10>;
        touchscreen-inverted-x;
        touchscreen-inverted-y;
      };
    };

...
