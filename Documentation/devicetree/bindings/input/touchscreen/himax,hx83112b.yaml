# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/touchscreen/himax,hx83112b.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Himax hx83112b touchscreen controller

maintainers:
  - <PERSON>an <<EMAIL>>

allOf:
  - $ref: touchscreen.yaml#

properties:
  compatible:
    enum:
      - himax,hx83112b

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  reset-gpios:
    maxItems: 1

  touchscreen-inverted-x: true
  touchscreen-inverted-y: true
  touchscreen-size-x: true
  touchscreen-size-y: true
  touchscreen-swapped-x-y: true

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts
  - reset-gpios
  - touchscreen-size-x
  - touchscreen-size-y

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/gpio/gpio.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      touchscreen@48 {
        compatible = "himax,hx83112b";
        reg = <0x48>;
        interrupt-parent = <&tlmm>;
        interrupts = <65 IRQ_TYPE_LEVEL_LOW>;
        touchscreen-size-x = <1080>;
        touchscreen-size-y = <2160>;
        reset-gpios = <&tlmm 64 GPIO_ACTIVE_LOW>;
      };
    };

...
