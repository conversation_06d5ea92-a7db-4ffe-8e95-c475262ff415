# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/touchscreen/goodix.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Goodix GT9xx series touchscreen controller

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: touchscreen.yaml#

properties:
  compatible:
    enum:
      - goodix,gt1151
      - goodix,gt1158
      - goodix,gt5663
      - goodix,gt5688
      - goodix,gt911
      - goodix,gt9110
      - goodix,gt912
      - goodix,gt9147
      - goodix,gt917s
      - goodix,gt927
      - goodix,gt9271
      - goodix,gt928
      - goodix,gt9286
      - goodix,gt967

  reg:
    enum: [ 0x5d, 0x14 ]

  interrupts:
    maxItems: 1

  irq-gpios:
    description: GPIO pin used for IRQ. The driver uses the interrupt gpio pin
      as output to reset the device.
    maxItems: 1

  reset-gpios:
    maxItems: 1

  AVDD28-supply:
    description: Analog power supply regulator on AVDD28 pin

  VDDIO-supply:
    description: GPIO power supply regulator on VDDIO pin

  touchscreen-inverted-x: true
  touchscreen-inverted-y: true
  touchscreen-size-x: true
  touchscreen-size-y: true
  touchscreen-swapped-x-y: true

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      gt928@5d {
        compatible = "goodix,gt928";
        reg = <0x5d>;
        interrupt-parent = <&gpio>;
        interrupts = <0 0>;
        irq-gpios = <&gpio1 0 0>;
        reset-gpios = <&gpio1 1 0>;
      };
    };

...
