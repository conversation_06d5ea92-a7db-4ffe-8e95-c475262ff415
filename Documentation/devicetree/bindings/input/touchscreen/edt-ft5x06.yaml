# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/touchscreen/edt-ft5x06.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: FocalTech EDT-FT5x06 Polytouch

description: |
             There are 5 variants of the chip for various touch panel sizes
              FT5206GE1  2.8" .. 3.8"
              FT5306DE4  4.3" .. 7"
              FT5406EE8  7"   .. 8.9"
              FT5506EEG  7"   .. 8.9"
              FT5726NEI  5.7” .. 11.6"

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: touchscreen.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - evervision,ev-ft5726

    then:
      properties:
        offset-x: true
        offset-y: true

properties:
  compatible:
    enum:
      - edt,edt-ft5206
      - edt,edt-ft5306
      - edt,edt-ft5406
      - edt,edt-ft5506
      - evervision,ev-ft5726
      - focaltech,ft6236

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  reset-gpios:
    maxItems: 1

  wake-gpios:
    maxItems: 1

  wakeup-source: true

  vcc-supply: true
  iovcc-supply: true

  gain:
    description: Allows setting the sensitivity in the range from 0 to 31.
                 Note that lower values indicate higher sensitivity.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 31

  offset:
    description: Allows setting the edge compensation in the range from 0 to 31.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 31

  offset-x:
    description: Same as offset, but applies only to the horizontal position.
                 Range from 0 to 80, only supported by evervision,ev-ft5726 devices.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 80

  offset-y:
    description: Same as offset, but applies only to the vertical position.
                 Range from 0 to 80, only supported by evervision,ev-ft5726 devices.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 80

  report-rate-hz:
    description: |
                 Allows setting the scan rate in Hertz.
                  M06 supports range from 30 to 140 Hz.
                  M12 supports range from 1 to 255 Hz.
    minimum: 1
    maximum: 255

  threshold:
    description: Allows setting the  "click"-threshold in the range from 0 to 255.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 255

  touchscreen-size-x: true
  touchscreen-size-y: true
  touchscreen-fuzz-x: true
  touchscreen-fuzz-y: true
  touchscreen-inverted-x: true
  touchscreen-inverted-y: true
  touchscreen-swapped-x-y: true
  interrupt-controller: true

additionalProperties: false

required:
  - compatible
  - reg
  - interrupts

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;
      edt-ft5x06@38 {
        compatible = "edt,edt-ft5406";
        reg = <0x38>;
        interrupt-parent = <&gpio2>;
        interrupts = <5 IRQ_TYPE_EDGE_FALLING>;
        reset-gpios = <&gpio2 6 GPIO_ACTIVE_LOW>;
        wake-gpios = <&gpio4 9 GPIO_ACTIVE_HIGH>;
      };
    };

...
