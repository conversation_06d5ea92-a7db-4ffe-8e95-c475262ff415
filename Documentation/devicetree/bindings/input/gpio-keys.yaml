# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/input/gpio-keys.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: GPIO attached keys

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - gpio-keys
      - gpio-keys-polled

  autorepeat: true

  label:
    description: Name of entire device

  poll-interval: true

patternProperties:
  "^(button|event|key|switch|(button|event|key|switch)-[a-z0-9-]+|[a-z0-9-]+-(button|event|key|switch))$":
    $ref: input.yaml#

    properties:
      gpios:
        maxItems: 1

      interrupts:
        maxItems: 1

      label:
        description: Descriptive name of the key.

      linux,code:
        description: Key / Axis code to emit.

      linux,input-type:
        default: 1  # EV_KEY

      linux,input-value:
        description: |
          If linux,input-type is EV_ABS or EV_REL then this
          value is sent for events this button generates when pressed.
          EV_ABS/EV_REL axis will generate an event with a value of 0
          when all buttons with linux,input-type == type and
          linux,code == axis are released. This value is interpreted
          as a signed 32 bit value, e.g. to make a button generate a
          value of -1 use:

          linux,input-value = <0xffffffff>; /* -1 */

        $ref: /schemas/types.yaml#/definitions/uint32

      debounce-interval:
        description:
          Debouncing interval time in milliseconds. If not specified defaults to 5.
        $ref: /schemas/types.yaml#/definitions/uint32

        default: 5

      wakeup-source:
        description: Button can wake-up the system.

      wakeup-event-action:
        description: |
          Specifies whether the key should wake the system when asserted, when
          deasserted, or both. This property is only valid for keys that wake up the
          system (e.g., when the "wakeup-source" property is also provided).

          Supported values are defined in linux-event-codes.h:

            EV_ACT_ANY        - both asserted and deasserted
            EV_ACT_ASSERTED   - asserted
            EV_ACT_DEASSERTED - deasserted
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [0, 1, 2]

      linux,can-disable:
        description:
          Indicates that button is connected to dedicated (not shared) interrupt
          which can be disabled to suppress events from the button.
        type: boolean

    required:
      - linux,code

    anyOf:
      - required:
          - interrupts
      - required:
          - interrupts-extended
      - required:
          - gpios

    dependencies:
      wakeup-event-action: [ wakeup-source ]
      linux,input-value: [ gpios ]

    unevaluatedProperties: false

allOf:
  - $ref: input.yaml#
  - if:
      properties:
        compatible:
          const: gpio-keys-polled
    then:
      required:
        - poll-interval
    else:
      properties:
        poll-interval: false

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    gpio-keys {
        compatible = "gpio-keys";
        autorepeat;

        key-up {
            label = "GPIO Key UP";
            linux,code = <103>;
            gpios = <&gpio1 0 1>;
        };

        key-down {
            label = "GPIO Key DOWN";
            linux,code = <108>;
            interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
        };
    };

...
