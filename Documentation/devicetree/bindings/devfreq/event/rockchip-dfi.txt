
* Rockchip rk3399 DFI device

Required properties:
- compatible: Must be "rockchip,rk3399-dfi".
- reg: physical base address of each DFI and length of memory mapped region
- rockchip,pmu: phandle to the syscon managing the "pmu general register files"
- clocks: phandles for clock specified in "clock-names" property
- clock-names : the name of clock used by the DFI, must be "pclk_ddr_mon";

Example:
	dfi: dfi@ff630000 {
		compatible = "rockchip,rk3399-dfi";
		reg = <0x00 0xff630000 0x00 0x4000>;
		rockchip,pmu = <&pmugrf>;
		clocks = <&cru PCLK_DDR_MON>;
		clock-names = "pclk_ddr_mon";
	};
