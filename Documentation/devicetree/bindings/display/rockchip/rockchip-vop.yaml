# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/rockchip/rockchip-vop.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip SoC display controller (VOP)

description:
  VOP (Video Output Processor) is the display controller for the Rockchip
  series of SoCs which transfers the image data from a video memory
  buffer to an external LCD interface.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - rockchip,px30-vop-big
      - rockchip,px30-vop-lit
      - rockchip,rk3036-vop
      - rockchip,rk3066-vop
      - rockchip,rk3126-vop
      - rockchip,rk3188-vop
      - rockchip,rk3228-vop
      - rockchip,rk3288-vop
      - rockchip,rk3328-vop
      - rockchip,rk3366-vop
      - rockchip,rk3368-vop
      - rockchip,rk3399-vop-big
      - rockchip,rk3399-vop-lit

  reg:
    minItems: 1
    items:
      - description:
          Must contain one entry corresponding to the base address and length
          of the register space.
      - description:
          Can optionally contain a second entry corresponding to
          the CRTC gamma LUT address.

  interrupts:
    maxItems: 1
    description:
      The VOP interrupt is shared by several interrupt sources, such as
      frame start (VSYNC), line flag and other status interrupts.

  clocks:
    items:
      - description: Clock for ddr buffer transfer.
      - description: Pixel clock.
      - description: Clock for the ahb bus to R/W the phy regs.

  clock-names:
    items:
      - const: aclk_vop
      - const: dclk_vop
      - const: hclk_vop

  resets:
    maxItems: 3

  reset-names:
    items:
      - const: axi
      - const: ahb
      - const: dclk

  port:
    $ref: /schemas/graph.yaml#/properties/port

  assigned-clocks:
    maxItems: 2

  assigned-clock-rates:
    maxItems: 2

  iommus:
    maxItems: 1

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - resets
  - reset-names
  - port

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/rk3288-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/rk3288-power.h>
    vopb: vopb@ff930000 {
      compatible = "rockchip,rk3288-vop";
      reg = <0xff930000 0x19c>,
            <0xff931000 0x1000>;
      interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&cru ACLK_VOP0>,
               <&cru DCLK_VOP0>,
               <&cru HCLK_VOP0>;
      clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
      power-domains = <&power RK3288_PD_VIO>;
      resets = <&cru SRST_LCDC1_AXI>,
               <&cru SRST_LCDC1_AHB>,
               <&cru SRST_LCDC1_DCLK>;
      reset-names = "axi", "ahb", "dclk";
      iommus = <&vopb_mmu>;
      vopb_out: port {
        #address-cells = <1>;
        #size-cells = <0>;
        vopb_out_edp: endpoint@0 {
          reg = <0>;
          remote-endpoint = <&edp_in_vopb>;
        };
        vopb_out_hdmi: endpoint@1 {
          reg = <1>;
          remote-endpoint = <&hdmi_in_vopb>;
        };
      };
    };
