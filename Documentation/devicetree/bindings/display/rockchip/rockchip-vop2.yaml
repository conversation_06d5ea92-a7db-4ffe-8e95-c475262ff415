# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/rockchip/rockchip-vop2.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip SoC display controller (VOP2)

description:
  VOP2 (Video Output Processor v2) is the display controller for the Rockchip
  series of SoCs which transfers the image data from a video memory
  buffer to an external LCD interface.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - rockchip,rk3566-vop
      - rockchip,rk3568-vop

  reg:
    items:
      - description:
          Must contain one entry corresponding to the base address and length
          of the register space.
      - description:
          Can optionally contain a second entry corresponding to
          the CRTC gamma LUT address.

  reg-names:
    items:
      - const: vop
      - const: gamma-lut

  interrupts:
    maxItems: 1
    description:
      The VOP interrupt is shared by several interrupt sources, such as
      frame start (VSYNC), line flag and other status interrupts.

  clocks:
    items:
      - description: Clock for ddr buffer transfer.
      - description: Clock for the ahb bus to R/W the phy regs.
      - description: Pixel clock for video port 0.
      - description: Pixel clock for video port 1.
      - description: Pixel clock for video port 2.

  clock-names:
    items:
      - const: aclk
      - const: hclk
      - const: dclk_vp0
      - const: dclk_vp1
      - const: dclk_vp2

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Phandle to GRF regs used for misc control

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description:
          Output endpoint of VP0

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description:
          Output endpoint of VP1

      port@2:
        $ref: /schemas/graph.yaml#/properties/port
        description:
          Output endpoint of VP2

  iommus:
    maxItems: 1

  power-domains:
    maxItems: 1

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - clocks
  - clock-names
  - ports

additionalProperties: false

examples:
  - |
        #include <dt-bindings/clock/rk3568-cru.h>
        #include <dt-bindings/interrupt-controller/arm-gic.h>
        #include <dt-bindings/power/rk3568-power.h>
        bus {
            #address-cells = <2>;
            #size-cells = <2>;
            vop: vop@fe040000 {
                compatible = "rockchip,rk3568-vop";
                reg = <0x0 0xfe040000 0x0 0x3000>, <0x0 0xfe044000 0x0 0x1000>;
                reg-names = "vop", "gamma-lut";
                interrupts = <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>;
                clocks = <&cru ACLK_VOP>,
                         <&cru HCLK_VOP>,
                         <&cru DCLK_VOP0>,
                         <&cru DCLK_VOP1>,
                         <&cru DCLK_VOP2>;
                clock-names = "aclk",
                              "hclk",
                              "dclk_vp0",
                              "dclk_vp1",
                              "dclk_vp2";
                power-domains = <&power RK3568_PD_VO>;
                iommus = <&vop_mmu>;
                vop_out: ports {
                    #address-cells = <1>;
                    #size-cells = <0>;
                    vp0: port@0 {
                        reg = <0>;
                        #address-cells = <1>;
                        #size-cells = <0>;
                    };
                    vp1: port@1 {
                        reg = <1>;
                        #address-cells = <1>;
                        #size-cells = <0>;
                    };
                    vp2: port@2 {
                        reg = <2>;
                        #address-cells = <1>;
                        #size-cells = <0>;
                    };
                };
            };
        };
