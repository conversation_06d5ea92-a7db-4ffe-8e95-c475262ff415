# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/rockchip/rockchip,dw-mipi-dsi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip specific extensions to the Synopsys Designware MIPI DSI

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - rockchip,px30-mipi-dsi
          - rockchip,rk3288-mipi-dsi
          - rockchip,rk3399-mipi-dsi
          - rockchip,rk3568-mipi-dsi
      - const: snps,dw-mipi-dsi

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 4

  clock-names:
    oneOf:
      - minItems: 2
        items:
          - const: ref
          - const: pclk
          - const: phy_cfg
          - const: grf
      - const: pclk

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      This SoC uses GRF regs to switch between vopl/vopb.

  phys:
    maxItems: 1

  phy-names:
    const: dphy

  "#phy-cells":
    const: 0
    description:
      Defined when in use as ISP phy.

  power-domains:
    maxItems: 1

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

required:
  - compatible
  - clocks
  - clock-names
  - rockchip,grf

allOf:
  - $ref: /schemas/display/bridge/snps,dw-mipi-dsi.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - rockchip,px30-mipi-dsi
              - rockchip,rk3568-mipi-dsi

    then:
      properties:
        clocks:
          maxItems: 1

        clock-names:
          maxItems: 1

      required:
        - phys
        - phy-names

  - if:
      properties:
        compatible:
          contains:
            const: rockchip,rk3288-mipi-dsi

    then:
      properties:
        clocks:
          maxItems: 2

        clock-names:
          maxItems: 2

  - if:
      properties:
        compatible:
          contains:
            const: rockchip,rk3399-mipi-dsi

    then:
      properties:
        clocks:
          minItems: 4

        clock-names:
          minItems: 4

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/rk3288-cru.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    mipi_dsi: dsi@ff960000 {
      compatible = "rockchip,rk3288-mipi-dsi", "snps,dw-mipi-dsi";
      reg = <0xff960000 0x4000>;
      interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
      clocks = <&cru SCLK_MIPIDSI_24M>, <&cru PCLK_MIPI_DSI0>;
      clock-names = "ref", "pclk";
      resets = <&cru SRST_MIPIDSI0>;
      reset-names = "apb";
      rockchip,grf = <&grf>;

      ports {
        #address-cells = <1>;
        #size-cells = <0>;

        mipi_in: port@0 {
          reg = <0>;
          #address-cells = <1>;
          #size-cells = <0>;

          mipi_in_vopb: endpoint@0 {
            reg = <0>;
            remote-endpoint = <&vopb_out_mipi>;
          };
          mipi_in_vopl: endpoint@1 {
            reg = <1>;
            remote-endpoint = <&vopl_out_mipi>;
          };
        };

        mipi_out: port@1 {
          reg = <1>;

          mipi_out_panel: endpoint {
            remote-endpoint = <&panel_in_mipi>;
          };
        };
      };
    };
