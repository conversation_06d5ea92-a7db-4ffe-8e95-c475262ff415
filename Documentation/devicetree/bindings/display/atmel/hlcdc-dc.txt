Device-Tree bindings for Atmel's HLCDC (High LCD Controller) DRM driver

The Atmel HLCDC Display Controller is subdevice of the HLCDC MFD device.
See ../../mfd/atmel-hlcdc.txt for more details.

Required properties:
 - compatible: value should be "atmel,hlcdc-display-controller"
 - pinctrl-names: the pin control state names. Should contain "default".
 - pinctrl-0: should contain the default pinctrl states.
 - #address-cells: should be set to 1.
 - #size-cells: should be set to 0.

Required children nodes:
 Children nodes are encoding available output ports and their connections
 to external devices using the OF graph representation (see ../graph.txt).
 At least one port node is required.

Optional properties in grandchild nodes:
 Any endpoint grandchild node may specify a desired video interface
 according to ../../media/video-interfaces.txt, specifically
 - bus-width: recognized values are <12>, <16>, <18> and <24>, and
   override any output mode selection heuristic, forcing "rgb444",
   "rgb565", "rgb666" and "rgb888" respectively.

Example:

	hlcdc: hlcdc@f0030000 {
		compatible = "atmel,sama5d3-hlcdc";
		reg = <0xf0030000 0x2000>;
		interrupts = <36 IRQ_TYPE_LEVEL_HIGH 0>;
		clocks = <&lcdc_clk>, <&lcdck>, <&clk32k>;
		clock-names = "periph_clk","sys_clk", "slow_clk";

		hlcdc-display-controller {
			compatible = "atmel,hlcdc-display-controller";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_lcd_base &pinctrl_lcd_rgb888>;
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;

				hlcdc_panel_output: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&panel_input>;
				};
			};
		};

		hlcdc_pwm: hlcdc-pwm {
			compatible = "atmel,hlcdc-pwm";
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_lcd_pwm>;
			#pwm-cells = <3>;
		};
	};

Example 2: With a video interface override to force rgb565; as above
but with these changes/additions:

	&hlcdc {
		hlcdc-display-controller {
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_lcd_base &pinctrl_lcd_rgb565>;

			port@0 {
				hlcdc_panel_output: endpoint@0 {
					bus-width = <16>;
				};
			};
		};
	};
