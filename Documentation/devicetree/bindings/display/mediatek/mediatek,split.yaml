# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,split.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek display split

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek display split, namely SPLIT, is used to split stream to two
  encoders.
  SPLIT device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt8173-disp-split
      - items:
          - const: mediatek,mt6795-disp-split
          - const: mediatek,mt8173-disp-split

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    items:
      - description: SPLIT Clock

required:
  - compatible
  - reg
  - power-domains
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/mt8173-clk.h>
    #include <dt-bindings/power/mt8173-power.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        split0: split@14018000 {
            compatible = "mediatek,mt8173-disp-split";
            reg = <0 0x14018000 0 0x1000>;
            power-domains = <&spm MT8173_POWER_DOMAIN_MM>;
            clocks = <&mmsys CLK_MM_DISP_SPLIT0>;
        };
    };
