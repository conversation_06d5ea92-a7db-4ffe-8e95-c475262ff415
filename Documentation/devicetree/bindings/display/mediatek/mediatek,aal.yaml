# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,aal.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek display adaptive ambient light processor

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek display adaptive ambient light processor, namely AAL,
  is responsible for backlight power saving and sunlight visibility improving.
  AAL device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt8173-disp-aal
          - mediatek,mt8183-disp-aal
      - items:
          - enum:
              - mediatek,mt2712-disp-aal
              - mediatek,mt6795-disp-aal
          - const: mediatek,mt8173-disp-aal
      - items:
          - enum:
              - mediatek,mt8186-disp-aal
              - mediatek,mt8188-disp-aal
              - mediatek,mt8192-disp-aal
              - mediatek,mt8195-disp-aal
          - const: mediatek,mt8183-disp-aal

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    items:
      - description: AAL Clock

  mediatek,gce-client-reg:
    description: The register of client driver can be configured by gce with
      4 arguments defined in this property, such as phandle of gce, subsys id,
      register offset and size. Each GCE subsys id is mapping to a client
      defined in the header include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - power-domains
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8173-clk.h>
    #include <dt-bindings/power/mt8173-power.h>
    #include <dt-bindings/gce/mt8173-gce.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        aal@14015000 {
           compatible = "mediatek,mt8173-disp-aal";
           reg = <0 0x14015000 0 0x1000>;
           interrupts = <GIC_SPI 189 IRQ_TYPE_LEVEL_LOW>;
           power-domains = <&scpsys MT8173_POWER_DOMAIN_MM>;
           clocks = <&mmsys CLK_MM_DISP_AAL>;
           mediatek,gce-client-reg = <&gce SUBSYS_1401XXXX 0x5000 0x1000>;
       };
    };
