# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,ethdr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek Ethdr Device

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  ETHDR (ET High Dynamic Range) is a MediaTek internal HDR engine and is
  designed for HDR video and graphics conversion in the external display path.
  It handles multiple HDR input types and performs tone mapping, color
  space/color format conversion, and then combine different layers,
  output the required HDR or SDR signal to the subsequent display path.
  This engine is composed of two video frontends, two graphic frontends,
  one video backend and a mixer. ETHDR has two DMA function blocks, DS and ADL.
  These two function blocks read the pre-programmed registers from DRAM and
  set them to HW in the v-blanking period.

properties:
  compatible:
    const: mediatek,mt8195-disp-ethdr

  reg:
    maxItems: 7

  reg-names:
    items:
      - const: mixer
      - const: vdo_fe0
      - const: vdo_fe1
      - const: gfx_fe0
      - const: gfx_fe1
      - const: vdo_be
      - const: adl_ds

  interrupts:
    maxItems: 1

  iommus:
    minItems: 1
    maxItems: 2

  clocks:
    items:
      - description: mixer clock
      - description: video frontend 0 clock
      - description: video frontend 1 clock
      - description: graphic frontend 0 clock
      - description: graphic frontend 1 clock
      - description: video backend clock
      - description: autodownload and menuload clock
      - description: video frontend 0 async clock
      - description: video frontend 1 async clock
      - description: graphic frontend 0 async clock
      - description: graphic frontend 1 async clock
      - description: video backend async clock
      - description: ethdr top clock

  clock-names:
    items:
      - const: mixer
      - const: vdo_fe0
      - const: vdo_fe1
      - const: gfx_fe0
      - const: gfx_fe1
      - const: vdo_be
      - const: adl_ds
      - const: vdo_fe0_async
      - const: vdo_fe1_async
      - const: gfx_fe0_async
      - const: gfx_fe1_async
      - const: vdo_be_async
      - const: ethdr_top

  power-domains:
    maxItems: 1

  resets:
    items:
      - description: video frontend 0 async reset
      - description: video frontend 1 async reset
      - description: graphic frontend 0 async reset
      - description: graphic frontend 1 async reset
      - description: video backend async reset

  reset-names:
    items:
      - const: vdo_fe0_async
      - const: vdo_fe1_async
      - const: gfx_fe0_async
      - const: gfx_fe1_async
      - const: vdo_be_async

  mediatek,gce-client-reg:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    minItems: 1
    maxItems: 7
    description: The register of display function block to be set by gce.
      There are 4 arguments in this property, gce node, subsys id, offset and
      register size. The subsys id is defined in the gce header of each chips
      include/dt-bindings/gce/<chip>-gce.h, mapping to the register of display
      function block.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - interrupts
  - power-domains
  - resets
  - mediatek,gce-client-reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8195-clk.h>
    #include <dt-bindings/gce/mt8195-gce.h>
    #include <dt-bindings/memory/mt8195-memory-port.h>
    #include <dt-bindings/power/mt8195-power.h>
    #include <dt-bindings/reset/mt8195-resets.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        hdr-engine@1c114000 {
                compatible = "mediatek,mt8195-disp-ethdr";
                reg = <0 0x1c114000 0 0x1000>,
                      <0 0x1c115000 0 0x1000>,
                      <0 0x1c117000 0 0x1000>,
                      <0 0x1c119000 0 0x1000>,
                      <0 0x1c11a000 0 0x1000>,
                      <0 0x1c11b000 0 0x1000>,
                      <0 0x1c11c000 0 0x1000>;
                reg-names = "mixer", "vdo_fe0", "vdo_fe1", "gfx_fe0", "gfx_fe1",
                            "vdo_be", "adl_ds";
                mediatek,gce-client-reg = <&gce0 SUBSYS_1c11XXXX 0x4000 0x1000>,
                                          <&gce0 SUBSYS_1c11XXXX 0x5000 0x1000>,
                                          <&gce0 SUBSYS_1c11XXXX 0x7000 0x1000>,
                                          <&gce0 SUBSYS_1c11XXXX 0x9000 0x1000>,
                                          <&gce0 SUBSYS_1c11XXXX 0xa000 0x1000>,
                                          <&gce0 SUBSYS_1c11XXXX 0xb000 0x1000>,
                                          <&gce0 SUBSYS_1c11XXXX 0xc000 0x1000>;
                clocks = <&vdosys1 CLK_VDO1_DISP_MIXER>,
                         <&vdosys1 CLK_VDO1_HDR_VDO_FE0>,
                         <&vdosys1 CLK_VDO1_HDR_VDO_FE1>,
                         <&vdosys1 CLK_VDO1_HDR_GFX_FE0>,
                         <&vdosys1 CLK_VDO1_HDR_GFX_FE1>,
                         <&vdosys1 CLK_VDO1_HDR_VDO_BE>,
                         <&vdosys1 CLK_VDO1_26M_SLOW>,
                         <&vdosys1 CLK_VDO1_HDR_VDO_FE0_DL_ASYNC>,
                         <&vdosys1 CLK_VDO1_HDR_VDO_FE1_DL_ASYNC>,
                         <&vdosys1 CLK_VDO1_HDR_GFX_FE0_DL_ASYNC>,
                         <&vdosys1 CLK_VDO1_HDR_GFX_FE1_DL_ASYNC>,
                         <&vdosys1 CLK_VDO1_HDR_VDO_BE_DL_ASYNC>,
                         <&topckgen CLK_TOP_ETHDR>;
                clock-names = "mixer", "vdo_fe0", "vdo_fe1", "gfx_fe0", "gfx_fe1",
                              "vdo_be", "adl_ds", "vdo_fe0_async", "vdo_fe1_async",
                              "gfx_fe0_async", "gfx_fe1_async","vdo_be_async",
                              "ethdr_top";
                power-domains = <&spm MT8195_POWER_DOMAIN_VDOSYS1>;
                iommus = <&iommu_vpp M4U_PORT_L3_HDR_DS>,
                         <&iommu_vpp M4U_PORT_L3_HDR_ADL>;
                interrupts = <GIC_SPI 517 IRQ_TYPE_LEVEL_HIGH 0>; /* disp mixer */
                resets = <&vdosys1 MT8195_VDOSYS1_SW1_RST_B_HDR_VDO_FE0_DL_ASYNC>,
                         <&vdosys1 MT8195_VDOSYS1_SW1_RST_B_HDR_VDO_FE1_DL_ASYNC>,
                         <&vdosys1 MT8195_VDOSYS1_SW1_RST_B_HDR_GFX_FE0_DL_ASYNC>,
                         <&vdosys1 MT8195_VDOSYS1_SW1_RST_B_HDR_GFX_FE1_DL_ASYNC>,
                         <&vdosys1 MT8195_VDOSYS1_SW1_RST_B_HDR_VDO_BE_DL_ASYNC>;
                reset-names = "vdo_fe0_async", "vdo_fe1_async", "gfx_fe0_async",
                              "gfx_fe1_async", "vdo_be_async";
        };
    };
...
