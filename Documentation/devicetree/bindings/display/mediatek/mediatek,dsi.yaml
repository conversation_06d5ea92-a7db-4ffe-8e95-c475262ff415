# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,dsi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek DSI Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The MediaTek DSI function block is a sink of the display subsystem and can
  drive up to 4-lane MIPI DSI output. Two DSIs can be synchronized for dual-
  channel output.

allOf:
  - $ref: /schemas/display/dsi-controller.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt2701-dsi
          - mediatek,mt7623-dsi
          - mediatek,mt8167-dsi
          - mediatek,mt8173-dsi
          - mediatek,mt8183-dsi
          - mediatek,mt8186-dsi
      - items:
          - enum:
              - mediatek,mt6795-dsi
          - const: mediatek,mt8173-dsi

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    maxItems: 1

  clocks:
    items:
      - description: Engine Clock
      - description: Digital Clock
      - description: HS Clock

  clock-names:
    items:
      - const: engine
      - const: digital
      - const: hs

  resets:
    maxItems: 1

  phys:
    maxItems: 1

  phy-names:
    items:
      - const: dphy

  port:
    $ref: /schemas/graph.yaml#/properties/port
    description:
      Output port node. This port should be connected to the input
      port of an attached DSI panel or DSI-to-eDP encoder chip.

required:
  - compatible
  - reg
  - interrupts
  - power-domains
  - clocks
  - clock-names
  - phys
  - phy-names
  - port

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/mt8183-clk.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/power/mt8183-power.h>
    #include <dt-bindings/phy/phy.h>
    #include <dt-bindings/reset/mt8183-resets.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        dsi0: dsi@14014000 {
            compatible = "mediatek,mt8183-dsi";
            reg = <0 0x14014000 0 0x1000>;
            interrupts = <GIC_SPI 236 IRQ_TYPE_LEVEL_LOW>;
            power-domains = <&spm MT8183_POWER_DOMAIN_DISP>;
            clocks = <&mmsys CLK_MM_DSI0_MM>,
                <&mmsys CLK_MM_DSI0_IF>,
                <&mipi_tx0>;
            clock-names = "engine", "digital", "hs";
            resets = <&mmsys MT8183_MMSYS_SW0_RST_B_DISP_DSI0>;
            phys = <&mipi_tx0>;
            phy-names = "dphy";
            port {
                dsi0_out: endpoint {
                    remote-endpoint = <&panel_in>;
                };
            };
        };
    };

...
