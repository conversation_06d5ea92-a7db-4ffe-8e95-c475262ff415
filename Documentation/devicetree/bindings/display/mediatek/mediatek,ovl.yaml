# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,ovl.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek display overlay

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek display overlay, namely OVL, can do alpha blending from
  the memory.
  OVL device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt2701-disp-ovl
          - mediatek,mt8173-disp-ovl
          - mediatek,mt8183-disp-ovl
          - mediatek,mt8192-disp-ovl
      - items:
          - enum:
              - mediatek,mt7623-disp-ovl
              - mediatek,mt2712-disp-ovl
          - const: mediatek,mt2701-disp-ovl
      - items:
          - enum:
              - mediatek,mt6795-disp-ovl
          - const: mediatek,mt8173-disp-ovl
      - items:
          - enum:
              - mediatek,mt8188-disp-ovl
              - mediatek,mt8195-disp-ovl
          - const: mediatek,mt8183-disp-ovl
      - items:
          - enum:
              - mediatek,mt8186-disp-ovl
          - const: mediatek,mt8192-disp-ovl

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    items:
      - description: OVL Clock

  iommus:
    description:
      This property should point to the respective IOMMU block with master port as argument,
      see Documentation/devicetree/bindings/iommu/mediatek,iommu.yaml for details.

  mediatek,gce-client-reg:
    description: The register of client driver can be configured by gce with
      4 arguments defined in this property, such as phandle of gce, subsys id,
      register offset and size. Each GCE subsys id is mapping to a client
      defined in the header include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - power-domains
  - clocks
  - iommus

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8173-clk.h>
    #include <dt-bindings/power/mt8173-power.h>
    #include <dt-bindings/gce/mt8173-gce.h>
    #include <dt-bindings/memory/mt8173-larb-port.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        ovl0: ovl@1400c000 {
            compatible = "mediatek,mt8173-disp-ovl";
            reg = <0 0x1400c000 0 0x1000>;
            interrupts = <GIC_SPI 180 IRQ_TYPE_LEVEL_LOW>;
            power-domains = <&scpsys MT8173_POWER_DOMAIN_MM>;
            clocks = <&mmsys CLK_MM_DISP_OVL0>;
            iommus = <&iommu M4U_PORT_DISP_OVL0>;
            mediatek,gce-client-reg = <&gce SUBSYS_1400XXXX 0xc000 0x1000>;
        };
    };
