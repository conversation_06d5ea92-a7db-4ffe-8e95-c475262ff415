# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,ccorr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek display color correction

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek display color correction, namely CCORR, reproduces correct color
  on panels with different color gamut.
  CCORR device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt8183-disp-ccorr
          - mediatek,mt8192-disp-ccorr
      - items:
          - enum:
              - mediatek,mt8186-disp-ccorr
              - mediatek,mt8188-disp-ccorr
              - mediatek,mt8195-disp-ccorr
          - const: mediatek,mt8192-disp-ccorr

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    items:
      - description: CCORR Clock

  mediatek,gce-client-reg:
    description: The register of client driver can be configured by gce with
      4 arguments defined in this property, such as phandle of gce, subsys id,
      register offset and size. Each GCE subsys id is mapping to a client
      defined in the header include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - power-domains
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8183-clk.h>
    #include <dt-bindings/power/mt8183-power.h>
    #include <dt-bindings/gce/mt8183-gce.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        ccorr0: ccorr@1400f000 {
            compatible = "mediatek,mt8183-disp-ccorr";
            reg = <0 0x1400f000 0 0x1000>;
            interrupts = <GIC_SPI 232 IRQ_TYPE_LEVEL_LOW>;
            power-domains = <&spm MT8183_POWER_DOMAIN_DISP>;
            clocks = <&mmsys CLK_MM_DISP_CCORR0>;
            mediatek,gce-client-reg = <&gce SUBSYS_1400XXXX 0xf000 0x1000>;
        };
    };
