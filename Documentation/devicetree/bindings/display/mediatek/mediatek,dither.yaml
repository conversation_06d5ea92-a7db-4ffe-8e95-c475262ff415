# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,dither.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek display dither processor

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek display dither processor, namely DITHER, works by approximating
  unavailable colors with available colors and by mixing and matching available
  colors to mimic unavailable ones.
  DITHER device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt8183-disp-dither
      - items:
          - enum:
              - mediatek,mt8186-disp-dither
              - mediatek,mt8188-disp-dither
              - mediatek,mt8192-disp-dither
              - mediatek,mt8195-disp-dither
          - const: mediatek,mt8183-disp-dither

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    items:
      - description: DITHER Clock

  mediatek,gce-client-reg:
    description: The register of client driver can be configured by gce with
      4 arguments defined in this property, such as phandle of gce, subsys id,
      register offset and size. Each GCE subsys id is mapping to a client
      defined in the header include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - power-domains
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8183-clk.h>
    #include <dt-bindings/power/mt8183-power.h>
    #include <dt-bindings/gce/mt8183-gce.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        dither0: dither@14012000 {
            compatible = "mediatek,mt8183-disp-dither";
            reg = <0 0x14012000 0 0x1000>;
            interrupts = <GIC_SPI 235 IRQ_TYPE_LEVEL_LOW>;
            power-domains = <&spm MT8183_POWER_DOMAIN_DISP>;
            clocks = <&mmsys CLK_MM_DISP_DITHER0>;
            mediatek,gce-client-reg = <&gce SUBSYS_1401XXXX 0x2000 0x1000>;
        };
    };
