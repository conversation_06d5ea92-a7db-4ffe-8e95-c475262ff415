# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,rdma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek Read Direct Memory Access

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek Read Direct Memory Access(RDMA) component used to read the
  data into DMA. It provides real time data to the back-end panel
  driver, such as DSI, DPI and DP_INTF.
  It contains one line buffer to store the sufficient pixel data.
  RDMA device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt2701-disp-rdma
          - mediatek,mt8173-disp-rdma
          - mediatek,mt8183-disp-rdma
          - mediatek,mt8195-disp-rdma
      - items:
          - enum:
              - mediatek,mt8188-disp-rdma
          - const: mediatek,mt8195-disp-rdma
      - items:
          - enum:
              - mediatek,mt7623-disp-rdma
              - mediatek,mt2712-disp-rdma
          - const: mediatek,mt2701-disp-rdma
      - items:
          - enum:
              - mediatek,mt6795-disp-rdma
          - const: mediatek,mt8173-disp-rdma
      - items:
          - enum:
              - mediatek,mt8186-disp-rdma
              - mediatek,mt8192-disp-rdma
          - const: mediatek,mt8183-disp-rdma

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    items:
      - description: RDMA Clock

  iommus:
    description:
      This property should point to the respective IOMMU block with master port as argument,
      see Documentation/devicetree/bindings/iommu/mediatek,iommu.yaml for details.

  mediatek,rdma-fifo-size:
    description:
      rdma fifo size may be different even in same SOC, add this property to the
      corresponding rdma.
      The value below is the Max value which defined in hardware data sheet
      mediatek,rdma-fifo-size of mt8173-rdma0 is 8K
      mediatek,rdma-fifo-size of mt8183-rdma0 is 5K
      mediatek,rdma-fifo-size of mt8183-rdma1 is 2K
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [8192, 5120, 2048]

  mediatek,gce-client-reg:
    description: The register of client driver can be configured by gce with
      4 arguments defined in this property, such as phandle of gce, subsys id,
      register offset and size. Each GCE subsys id is mapping to a client
      defined in the header include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - power-domains
  - clocks
  - iommus

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8173-clk.h>
    #include <dt-bindings/power/mt8173-power.h>
    #include <dt-bindings/gce/mt8173-gce.h>
    #include <dt-bindings/memory/mt8173-larb-port.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        rdma0: rdma@1400e000 {
            compatible = "mediatek,mt8173-disp-rdma";
            reg = <0 0x1400e000 0 0x1000>;
            interrupts = <GIC_SPI 182 IRQ_TYPE_LEVEL_LOW>;
            power-domains = <&scpsys MT8173_POWER_DOMAIN_MM>;
            clocks = <&mmsys CLK_MM_DISP_RDMA0>;
            iommus = <&iommu M4U_PORT_DISP_RDMA0>;
            mediatek,rdma-fifo-size = <8192>;
            mediatek,gce-client-reg = <&gce SUBSYS_1400XXXX 0xe000 0x1000>;
        };
    };
