# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,dpi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek DPI and DP_INTF Controller

maintainers:
  - CK Hu <<EMAIL>>
  - Jita<PERSON> shi <<EMAIL>>

description: |
  The MediaTek DPI and DP_INTF function blocks are a sink of the display
  subsystem and provides 8-bit RGB/YUV444 or 8/10/10-bit YUV422 pixel data on a
  parallel output bus.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt2701-dpi
          - mediatek,mt7623-dpi
          - mediatek,mt8173-dpi
          - mediatek,mt8183-dpi
          - mediatek,mt8186-dpi
          - mediatek,mt8188-dp-intf
          - mediatek,mt8192-dpi
          - mediatek,mt8195-dp-intf
      - items:
          - enum:
              - mediatek,mt6795-dpi
          - const: mediatek,mt8183-dpi

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Pixel Clock
      - description: Engine Clock
      - description: DPI PLL

  clock-names:
    items:
      - const: pixel
      - const: engine
      - const: pll

  pinctrl-0: true
  pinctrl-1: true

  pinctrl-names:
    items:
      - const: default
      - const: sleep

  port:
    $ref: /schemas/graph.yaml#/properties/port
    description:
      Output port node. This port should be connected to the input port of an
      attached HDMI, LVDS or DisplayPort encoder chip.

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - port

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8173-clk.h>

    dpi0: dpi@1401d000 {
        compatible = "mediatek,mt8173-dpi";
        reg = <0x1401d000 0x1000>;
        interrupts = <GIC_SPI 194 IRQ_TYPE_LEVEL_LOW>;
        clocks = <&mmsys CLK_MM_DPI_PIXEL>,
             <&mmsys CLK_MM_DPI_ENGINE>,
             <&apmixedsys CLK_APMIXED_TVDPLL>;
        clock-names = "pixel", "engine", "pll";
        pinctrl-names = "default", "sleep";
        pinctrl-0 = <&dpi_pin_func>;
        pinctrl-1 = <&dpi_pin_idle>;

        port {
            dpi0_out: endpoint {
                remote-endpoint = <&hdmi0_in>;
            };
        };
    };

...
