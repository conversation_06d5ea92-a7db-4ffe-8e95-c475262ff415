# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/mediatek/mediatek,merge.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Mediatek display merge

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Mediatek display merge, namely MERGE, is used to merge two slice-per-line
  inputs into one side-by-side output.
  MERGE device node must be siblings to the central MMSYS_CONFIG node.
  For a description of the MMSYS_CONFIG binding, see
  Documentation/devicetree/bindings/arm/mediatek/mediatek,mmsys.yaml
  for details.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt8173-disp-merge
          - mediatek,mt8195-disp-merge
      - items:
          - const: mediatek,mt6795-disp-merge
          - const: mediatek,mt8173-disp-merge

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    description: A phandle and PM domain specifier as defined by bindings of
      the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    oneOf:
      - items:
          - const: merge
      - items:
          - const: merge
          - const: merge_async

  mediatek,merge-fifo-en:
    description:
      The setting of merge fifo is mainly provided for the display latency
      buffer to ensure that the back-end panel display data will not be
      underrun, a little more data is needed in the fifo.
      According to the merge fifo settings, when the water level is detected
      to be insufficient, it will trigger RDMA sending ultra and preulra
      command to SMI to speed up the data rate.
    type: boolean

  mediatek,merge-mute:
    description: Support mute function. Mute the content of merge output.
    type: boolean

  mediatek,gce-client-reg:
    description: The register of client driver can be configured by gce with
      4 arguments defined in this property, such as phandle of gce, subsys id,
      register offset and size. Each GCE subsys id is mapping to a client
      defined in the header include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

  resets:
    description: reset controller
      See Documentation/devicetree/bindings/reset/reset.txt for details.
    maxItems: 1

required:
  - compatible
  - reg
  - power-domains
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/mt8173-clk.h>
    #include <dt-bindings/power/mt8173-power.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        merge@14017000 {
            compatible = "mediatek,mt8173-disp-merge";
            reg = <0 0x14017000 0 0x1000>;
            power-domains = <&spm MT8173_POWER_DOMAIN_MM>;
            clocks = <&mmsys CLK_MM_DISP_MERGE>;
            clock-names = "merge";
        };
    };
