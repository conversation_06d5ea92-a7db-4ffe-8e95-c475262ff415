# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/fsl,lcdif.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale/NXP i.MX LCD Interface (LCDIF)

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  (e)LCDIF display controller found in the Freescale/NXP i.MX SoCs.

properties:
  compatible:
    oneOf:
      - enum:
          - fsl,imx23-lcdif
          - fsl,imx28-lcdif
          - fsl,imx6sx-lcdif
          - fsl,imx8mp-lcdif
          - fsl,imx93-lcdif
      - items:
          - enum:
              - fsl,imx6sl-lcdif
              - fsl,imx6sll-lcdif
              - fsl,imx6ul-lcdif
              - fsl,imx7d-lcdif
              - fsl,imx8mm-lcdif
              - fsl,imx8mn-lcdif
              - fsl,imx8mq-lcdif
          - const: fsl,imx6sx-lcdif

  reg:
    maxItems: 1

  clocks:
    items:
      - description: Pixel clock
      - description: Bus clock
      - description: Display AXI clock
    minItems: 1

  clock-names:
    items:
      - const: pix
      - const: axi
      - const: disp_axi
    minItems: 1

  interrupts:
    maxItems: 1

  power-domains:
    maxItems: 1

  port:
    $ref: /schemas/graph.yaml#/properties/port
    description: The LCDIF output port

required:
  - compatible
  - reg
  - clocks
  - interrupts
  - port

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: fsl,imx6sx-lcdif
    then:
      properties:
        clocks:
          minItems: 2
          maxItems: 3
        clock-names:
          minItems: 2
          maxItems: 3
      required:
        - clock-names
  - if:
      properties:
        compatible:
          contains:
            enum:
              - fsl,imx8mp-lcdif
              - fsl,imx93-lcdif
    then:
      properties:
        clocks:
          minItems: 3
          maxItems: 3
        clock-names:
          minItems: 3
          maxItems: 3
      required:
        - clock-names
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - fsl,imx6sx-lcdif
                - fsl,imx8mp-lcdif
                - fsl,imx93-lcdif
    then:
      properties:
        clocks:
          maxItems: 1
        clock-names:
          maxItems: 1
  - if:
      properties:
        compatible:
          contains:
            enum:
              - fsl,imx6sl-lcdif
              - fsl,imx6sx-lcdif
              - fsl,imx8mm-lcdif
              - fsl,imx8mn-lcdif
              - fsl,imx8mp-lcdif
              - fsl,imx93-lcdif
    then:
      required:
        - power-domains

examples:
  - |
    #include <dt-bindings/clock/imx6sx-clock.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    display-controller@2220000 {
        compatible = "fsl,imx6sx-lcdif";
        reg = <0x02220000 0x4000>;
        interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clks IMX6SX_CLK_LCDIF1_PIX>,
                 <&clks IMX6SX_CLK_LCDIF_APB>,
                 <&clks IMX6SX_CLK_DISPLAY_AXI>;
        clock-names = "pix", "axi", "disp_axi";
        power-domains = <&pd_disp>;

        port {
            endpoint {
                remote-endpoint = <&panel_in>;
            };
        };
    };

...
