# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/solomon,ssd1307fb.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: <PERSON> SSD1307 OLED Controller Framebuffer

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      # Deprecated compatible strings
      - enum:
          - solomon,ssd1305fb-i2c
          - solomon,ssd1306fb-i2c
          - solomon,ssd1307fb-i2c
          - solomon,ssd1309fb-i2c
        deprecated: true
      - enum:
          - sinowealth,sh1106
          - solomon,ssd1305
          - solomon,ssd1306
          - solomon,ssd1307
          - solomon,ssd1309

  reg:
    maxItems: 1

  pwms:
    maxItems: 1

  reset-gpios:
    maxItems: 1

  # Only required for SPI
  dc-gpios:
    description:
      GPIO connected to the controller's D/C# (Data/Command) pin,
      that is needed for 4-wire SPI to tell the controller if the
      data sent is for a command register or the display data RAM
    maxItems: 1

  vbat-supply:
    description: The supply for VBAT

  solomon,height:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Height in pixel of the screen driven by the controller.
      The default value is controller-dependent.

  solomon,width:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Width in pixel of the screen driven by the controller.
      The default value is controller-dependent.

  solomon,page-offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 1
    description:
      Offset of pages (band of 8 pixels) that the screen is mapped to

  solomon,segment-no-remap:
    type: boolean
    description:
      Display needs normal (non-inverted) data column to segment mapping

  solomon,col-offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 0
    description:
      Offset of columns (COL/SEG) that the screen is mapped to

  solomon,com-seq:
    type: boolean
    description:
      Display uses sequential COM pin configuration

  solomon,com-lrremap:
    type: boolean
    description:
      Display uses left-right COM pin remap

  solomon,com-invdir:
    type: boolean
    description:
      Display uses inverted COM pin scan direction

  solomon,com-offset:
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 0
    description:
      Number of the COM pin wired to the first display line

  solomon,prechargep1:
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 2
    description:
      Length of deselect period (phase 1) in clock cycles

  solomon,prechargep2:
    $ref: /schemas/types.yaml#/definitions/uint32
    default: 2
    description:
      Length of precharge period (phase 2) in clock cycles.  This needs to be
      the higher, the higher the capacitance of the OLED's pixels is.

  solomon,dclk-div:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 1
    maximum: 16
    description:
      Clock divisor. The default value is controller-dependent.

  solomon,dclk-frq:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 0
    maximum: 15
    description:
      Clock frequency, higher value means higher frequency.
      The default value is controller-dependent.

  solomon,lookup-table:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    maxItems: 4
    description:
      8 bit value array of current drive pulse widths for BANK0, and colors A,
      B, and C. Each value in range of 31 to 63 for pulse widths of 32 to 64.
      Color D is always width 64.

  solomon,area-color-enable:
    type: boolean
    description:
      Display uses color mode

  solomon,low-power:
    type: boolean
    description:
      Display runs in low power mode

required:
  - compatible
  - reg

allOf:
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

  - if:
      properties:
        compatible:
          contains:
            const: sinowealth,sh1106
    then:
      properties:
        width:
          default: 132
        height:
          default: 64
        solomon,dclk-div:
          default: 1
        solomon,dclk-frq:
          default: 5

  - if:
      properties:
        compatible:
          contains:
            enum:
              - solomon,ssd1305-i2c
              - solomon,ssd1305
    then:
      properties:
        width:
          default: 132
        height:
          default: 64
        solomon,dclk-div:
          default: 1
        solomon,dclk-frq:
          default: 7

  - if:
      properties:
        compatible:
          contains:
            enum:
              - solomon,ssd1306-i2c
              - solomon,ssd1306
    then:
      properties:
        width:
          default: 128
        height:
          default: 64
        solomon,dclk-div:
          default: 1
        solomon,dclk-frq:
          default: 8

  - if:
      properties:
        compatible:
          contains:
            enum:
              - solomon,ssd1307-i2c
              - solomon,ssd1307
    then:
      properties:
        width:
          default: 128
        height:
          default: 39
        solomon,dclk-div:
          default: 2
        solomon,dclk-frq:
          default: 12
      required:
        - pwms

  - if:
      properties:
        compatible:
          contains:
            enum:
              - solomon,ssd1309-i2c
              - solomon,ssd1309
    then:
      properties:
        width:
          default: 128
        height:
          default: 64
        solomon,dclk-div:
          default: 1
        solomon,dclk-frq:
          default: 10

unevaluatedProperties: false

examples:
  - |
    i2c {
            #address-cells = <1>;
            #size-cells = <0>;

            ssd1307_i2c: oled@3c {
                    compatible = "solomon,ssd1307";
                    reg = <0x3c>;
                    pwms = <&pwm 4 3000>;
                    reset-gpios = <&gpio2 7>;
            };

            ssd1306_i2c: oled@3d {
                    compatible = "solomon,ssd1306";
                    reg = <0x3d>;
                    pwms = <&pwm 4 3000>;
                    reset-gpios = <&gpio2 7>;
                    solomon,com-lrremap;
                    solomon,com-invdir;
                    solomon,com-offset = <32>;
                    solomon,lookup-table = /bits/ 8 <0x3f 0x3f 0x3f 0x3f>;
            };
    };
  - |
    spi {
            #address-cells = <1>;
            #size-cells = <0>;

            ssd1307_spi: oled@0 {
                    compatible = "solomon,ssd1307";
                    reg = <0x0>;
                    pwms = <&pwm 4 3000>;
                    reset-gpios = <&gpio2 7>;
                    dc-gpios = <&gpio2 8>;
                    spi-max-frequency = <10000000>;
            };

            ssd1306_spi: oled@1 {
                    compatible = "solomon,ssd1306";
                    reg = <0x1>;
                    pwms = <&pwm 4 3000>;
                    reset-gpios = <&gpio2 7>;
                    dc-gpios = <&gpio2 8>;
                    spi-max-frequency = <10000000>;
                    solomon,com-lrremap;
                    solomon,com-invdir;
                    solomon,com-offset = <32>;
                    solomon,lookup-table = /bits/ 8 <0x3f 0x3f 0x3f 0x3f>;
            };
    };
