# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/tegra/nvidia,tegra20-host1x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra host1x controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: The host1x top-level node defines a number of children, each
  representing one of the host1x client modules defined in this binding.

properties:
  compatible:
    oneOf:
      - enum:
          - nvidia,tegra20-host1x
          - nvidia,tegra30-host1x
          - nvidia,tegra114-host1x
          - nvidia,tegra124-host1x
          - nvidia,tegra210-host1x
          - nvidia,tegra186-host1x
          - nvidia,tegra194-host1x
          - nvidia,tegra234-host1x

      - items:
          - const: nvidia,tegra132-host1x
          - const: nvidia,tegra124-host1x

  reg:
    minItems: 1
    maxItems: 3

  reg-names:
    minItems: 1
    maxItems: 3

  interrupts:
    minItems: 1
    maxItems: 9

  interrupt-names:
    minItems: 1
    maxItems: 9

  '#address-cells':
    description: The number of cells used to represent physical base addresses
      in the host1x address space.
    enum: [1, 2]

  '#size-cells':
    description: The number of cells used to represent the size of an address
      range in the host1x address space.
    enum: [1, 2]

  ranges:
    maxItems: 1

  clocks:
    description: Must contain one entry, for the module clock. See
      ../clocks/clock-bindings.txt for details.

  clock-names:
    items:
      - const: host1x

  resets:
    minItems: 1 # MC reset is optional on Tegra186 and later
    items:
      - description: module reset
      - description: memory client hotflush reset

  reset-names:
    minItems: 1 # MC reset is optional on Tegra186 and later
    items:
      - const: host1x
      - const: mc

  iommus:
    maxItems: 1

  interconnects:
    items:
      - description: memory read client for host1x

  interconnect-names:
    items:
      - const: dma-mem # read

  operating-points-v2: true

  power-domains:
    items:
      - description: phandle to the HEG or core power domain

required:
  - compatible
  - interrupts
  - interrupt-names
  - '#address-cells'
  - '#size-cells'
  - ranges
  - reg
  - clocks
  - clock-names

unevaluatedProperties:
  type: object

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - nvidia,tegra20-host1x
              - nvidia,tegra30-host1x
              - nvidia,tegra114-host1x
              - nvidia,tegra124-host1x
              - nvidia,tegra210-host1x
    then:
      properties:
        interrupts:
          items:
            - description: host1x syncpoint interrupt
            - description: host1x general interrupt

        interrupt-names:
          items:
            - const: syncpt
            - const: host1x
      required:
        - resets
        - reset-names
  - if:
      properties:
        compatible:
          contains:
            enum:
              - nvidia,tegra186-host1x
              - nvidia,tegra194-host1x
    then:
      properties:
        reg-names:
          items:
            - const: hypervisor
            - const: vm

        reg:
          items:
            - description: region used by the hypervisor
            - description: region assigned to the virtual machine

        resets:
          maxItems: 1

        reset-names:
          maxItems: 1

        interrupts:
          items:
            - description: host1x syncpoint interrupt
            - description: host1x general interrupt

        interrupt-names:
          items:
            - const: syncpt
            - const: host1x

        iommu-map:
          description: Specification of stream IDs available for memory context device
            use. Should be a mapping of IDs 0..n to IOMMU entries corresponding to
            usable stream IDs.

      required:
        - reg-names
  - if:
      properties:
        compatible:
          contains:
            enum:
              - nvidia,tegra234-host1x
    then:
      properties:
        reg-names:
          items:
            - const: common
            - const: hypervisor
            - const: vm

        reg:
          items:
            - description: region used by host1x server
            - description: region used by the hypervisor
            - description: region assigned to the virtual machine

        interrupts:
          items:
            - description: host1x syncpoint interrupt 0
            - description: host1x syncpoint interrupt 1
            - description: host1x syncpoint interrupt 2
            - description: host1x syncpoint interrupt 3
            - description: host1x syncpoint interrupt 4
            - description: host1x syncpoint interrupt 5
            - description: host1x syncpoint interrupt 6
            - description: host1x syncpoint interrupt 7
            - description: host1x general interrupt

        interrupt-names:
          items:
            - const: syncpt0
            - const: syncpt1
            - const: syncpt2
            - const: syncpt3
            - const: syncpt4
            - const: syncpt5
            - const: syncpt6
            - const: syncpt7
            - const: host1x

        iommu-map:
          description: Specification of stream IDs available for memory context device
            use. Should be a mapping of IDs 0..n to IOMMU entries corresponding to
            usable stream IDs.

      required:
        - reg-names

examples:
  - |
    #include <dt-bindings/clock/tegra20-car.h>
    #include <dt-bindings/gpio/tegra-gpio.h>
    #include <dt-bindings/memory/tegra20-mc.h>

    host1x@50000000 {
        compatible = "nvidia,tegra20-host1x";
        reg = <0x50000000 0x00024000>;
        interrupts = <0 65 0x04>, /* mpcore syncpt */
                     <0 67 0x04>; /* mpcore general */
        interrupt-names = "syncpt", "host1x";
        clocks = <&tegra_car TEGRA20_CLK_HOST1X>;
        clock-names = "host1x";
        resets = <&tegra_car 28>, <&mc TEGRA20_MC_RESET_HC>;
        reset-names = "host1x", "mc";

        #address-cells = <1>;
        #size-cells = <1>;

        ranges = <0x54000000 0x54000000 0x04000000>;

        mpe@54040000 {
            compatible = "nvidia,tegra20-mpe";
            reg = <0x54040000 0x00040000>;
            interrupts = <0 68 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_MPE>;
            resets = <&tegra_car 60>;
            reset-names = "mpe";
        };

        vi@54080000 {
            compatible = "nvidia,tegra20-vi";
            reg = <0x54080000 0x00040000>;
            interrupts = <0 69 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_VI>;
            resets = <&tegra_car 100>;
            reset-names = "vi";
        };

        epp@540c0000 {
            compatible = "nvidia,tegra20-epp";
            reg = <0x540c0000 0x00040000>;
            interrupts = <0 70 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_EPP>;
            resets = <&tegra_car 19>;
            reset-names = "epp";
        };

        isp@54100000 {
            compatible = "nvidia,tegra20-isp";
            reg = <0x54100000 0x00040000>;
            interrupts = <0 71 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_ISP>;
            resets = <&tegra_car 23>;
            reset-names = "isp";
        };

        gr2d@54140000 {
            compatible = "nvidia,tegra20-gr2d";
            reg = <0x54140000 0x00040000>;
            interrupts = <0 72 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_GR2D>;
            resets = <&tegra_car 21>, <&mc TEGRA20_MC_RESET_2D>;
            reset-names = "2d", "mc";
        };

        gr3d@54180000 {
            compatible = "nvidia,tegra20-gr3d";
            reg = <0x54180000 0x00040000>;
            clocks = <&tegra_car TEGRA20_CLK_GR3D>;
            resets = <&tegra_car 24>, <&mc TEGRA20_MC_RESET_3D>;
            reset-names = "3d", "mc";
        };

        dc@54200000 {
            compatible = "nvidia,tegra20-dc";
            reg = <0x54200000 0x00040000>;
            interrupts = <0 73 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_DISP1>;
            clock-names = "dc";
            resets = <&tegra_car 27>;
            reset-names = "dc";

            rgb {
            };
        };

        dc@54240000 {
            compatible = "nvidia,tegra20-dc";
            reg = <0x54240000 0x00040000>;
            interrupts = <0 74 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_DISP2>;
            clock-names = "dc";
            resets = <&tegra_car 26>;
            reset-names = "dc";

            rgb {
            };
        };

        hdmi@54280000 {
            compatible = "nvidia,tegra20-hdmi";
            reg = <0x54280000 0x00040000>;
            interrupts = <0 75 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_HDMI>,
                     <&tegra_car TEGRA20_CLK_PLL_D_OUT0>;
            clock-names = "hdmi", "parent";
            resets = <&tegra_car 51>;
            reset-names = "hdmi";

            hdmi-supply = <&vdd_5v0_hdmi>;
            pll-supply = <&vdd_hdmi_pll>;
            vdd-supply = <&vdd_3v3_hdmi>;

            nvidia,ddc-i2c-bus = <&hdmi_ddc>;
            nvidia,hpd-gpio = <&gpio TEGRA_GPIO(N, 7) GPIO_ACTIVE_HIGH>;
        };

        tvo@542c0000 {
            compatible = "nvidia,tegra20-tvo";
            reg = <0x542c0000 0x00040000>;
            interrupts = <0 76 0x04>;
            clocks = <&tegra_car TEGRA20_CLK_TVO>;
        };

        dsi@54300000 {
            compatible = "nvidia,tegra20-dsi";
            reg = <0x54300000 0x00040000>;
            clocks = <&tegra_car TEGRA20_CLK_DSI>,
                     <&tegra_car TEGRA20_CLK_PLL_D_OUT0>;
            clock-names = "dsi", "parent";
            resets = <&tegra_car 48>;
            reset-names = "dsi";
        };
    };

  - |
    #include <dt-bindings/clock/tegra210-car.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/memory/tegra210-mc.h>

    host1x@50000000 {
        compatible = "nvidia,tegra210-host1x";
        reg = <0x50000000 0x00024000>;
        interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>, /* mpcore syncpt */
                     <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>; /* mpcore general */
        interrupt-names = "syncpt", "host1x";
        clocks = <&tegra_car TEGRA210_CLK_HOST1X>;
        clock-names = "host1x";
        resets = <&tegra_car 28>;
        reset-names = "host1x";

        #address-cells = <1>;
        #size-cells = <1>;

        ranges = <0x54000000 0x54000000 0x01000000>;
        iommus = <&mc TEGRA_SWGROUP_HC>;

        vi@54080000 {
            compatible = "nvidia,tegra210-vi";
            reg = <0x54080000 0x00000700>;
            interrupts = <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
            assigned-clocks = <&tegra_car TEGRA210_CLK_VI>;
            assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_C4_OUT0>;

            clocks = <&tegra_car TEGRA210_CLK_VI>;
            power-domains = <&pd_venc>;

            #address-cells = <1>;
            #size-cells = <1>;

            ranges = <0x0 0x54080000 0x2000>;

            csi@838 {
                compatible = "nvidia,tegra210-csi";
                reg = <0x838 0x1300>;
                assigned-clocks = <&tegra_car TEGRA210_CLK_CILAB>,
                                  <&tegra_car TEGRA210_CLK_CILCD>,
                                  <&tegra_car TEGRA210_CLK_CILE>,
                                  <&tegra_car TEGRA210_CLK_CSI_TPG>;
                assigned-clock-parents = <&tegra_car TEGRA210_CLK_PLL_P>,
                                         <&tegra_car TEGRA210_CLK_PLL_P>,
                                         <&tegra_car TEGRA210_CLK_PLL_P>;
                assigned-clock-rates = <102000000>,
                                       <102000000>,
                                       <102000000>,
                                       <972000000>;

                clocks = <&tegra_car TEGRA210_CLK_CSI>,
                         <&tegra_car TEGRA210_CLK_CILAB>,
                         <&tegra_car TEGRA210_CLK_CILCD>,
                         <&tegra_car TEGRA210_CLK_CILE>,
                         <&tegra_car TEGRA210_CLK_CSI_TPG>;
                clock-names = "csi", "cilab", "cilcd", "cile", "csi_tpg";
                power-domains = <&pd_sor>;
            };
        };
    };
