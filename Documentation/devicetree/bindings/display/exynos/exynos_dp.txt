The Exynos display port interface should be configured based on
the type of panel connected to it.

We use two nodes:
	-dp-controller node
	-dptx-phy node(defined inside dp-controller node)

For the DP-PHY initialization, we use the dptx-phy node.
Required properties for dptx-phy: deprecated, use phys and phy-names
	-reg: deprecated
		Base address of DP PHY register.
	-samsung,enable-mask: deprecated
		The bit-mask used to enable/disable DP PHY.

For the Panel initialization, we read data from dp-controller node.
Required properties for dp-controller:
	-compatible:
		should be "samsung,exynos5-dp".
	-reg:
		physical base address of the controller and length
		of memory mapped region.
	-interrupts:
		interrupt combiner values.
	-clocks:
		from common clock binding: handle to dp clock.
	-clock-names:
		from common clock binding: Shall be "dp".
	-phys:
		from general PHY binding: the phandle for the PHY device.
	-phy-names:
		from general PHY binding: Should be "dp".

Optional properties for dp-controller:
	-interlaced:
		interlace scan mode.
			Progressive if defined, Interlaced if not defined
	-vsync-active-high:
		VSYNC polarity configuration.
			High if defined, Low if not defined
	-hsync-active-high:
		HSYNC polarity configuration.
			High if defined, Low if not defined
	-samsung,hpd-gpio:
		Hotplug detect GPIO.
			Indicates which GPIO should be used for hotplug
			detection
	-video interfaces: Device node can contain video interface port
			nodes according to [1].
	- display-timings: timings for the connected panel as described by
		Documentation/devicetree/bindings/display/panel/display-timing.txt

For the below properties, please refer to Analogix DP binding document:
 * Documentation/devicetree/bindings/display/bridge/analogix,dp.yaml
	-phys (required)
	-phy-names (required)
	-hpd-gpios (optional)
	 force-hpd (optional)

Deprecated properties for DisplayPort:
-interlaced:            deprecated prop that can parsed from drm_display_mode.
-vsync-active-high:     deprecated prop that can parsed from drm_display_mode.
-hsync-active-high:     deprecated prop that can parsed from drm_display_mode.
-samsung,ycbcr-coeff:   deprecated prop that can parsed from drm_display_mode.
-samsung,dynamic-range: deprecated prop that can parsed from drm_display_mode.
-samsung,color-space:   deprecated prop that can parsed from drm_display_info.
-samsung,color-depth:   deprecated prop that can parsed from drm_display_info.
-samsung,link-rate:     deprecated prop that can reading from monitor by dpcd method.
-samsung,lane-count:    deprecated prop that can reading from monitor by dpcd method.
-samsung,hpd-gpio:      deprecated name for hpd-gpios.

-------------------------------------------------------------------------------

Example:

SOC specific portion:
	dp-controller {
		compatible = "samsung,exynos5-dp";
		reg = <0x145b0000 0x10000>;
		interrupts = <10 3>;
		interrupt-parent = <&combiner>;
		clocks = <&clock 342>;
		clock-names = "dp";

		phys = <&dp_phy>;
		phy-names = "dp";
	};

Board Specific portion:
	dp-controller {
		display-timings {
			native-mode = <&lcd_timing>;
			lcd_timing: 1366x768 {
				clock-frequency = <70589280>;
				hactive = <1366>;
				vactive = <768>;
				hfront-porch = <40>;
				hback-porch = <40>;
				hsync-len = <32>;
				vback-porch = <10>;
				vfront-porch = <12>;
				vsync-len = <6>;
			};
		};

		ports {
			port@0 {
				dp_out: endpoint {
					remote-endpoint = <&bridge_in>;
				};
			};
		};
	};
