# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/imx/fsl,imx6-hdmi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX6 DWC HDMI TX Encoder

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The HDMI transmitter is a Synopsys DesignWare HDMI 1.4 TX controller IP
  with a companion PHY IP.

allOf:
  - $ref: ../bridge/synopsys,dw-hdmi.yaml#

properties:
  compatible:
    enum:
      - fsl,imx6dl-hdmi
      - fsl,imx6q-hdmi

  reg-io-width:
    const: 1

  clocks:
    maxItems: 2

  clock-names:
    maxItems: 2

  ddc-i2c-bus:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      The HDMI DDC bus can be connected to either a system I2C master or the
      functionally-reduced I2C master contained in the DWC HDMI. When connected
      to a system I2C master this property contains a phandle to that I2C
      master controller.

  gpr:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      phandle to the iomuxc-gpr region containing the HDMI multiplexer control
      register.

  ports:
    $ref: /schemas/graph.yaml#/properties/ports
    description: |
      This device has four video ports, corresponding to the four inputs of the
      HDMI multiplexer. Each port shall have a single endpoint.

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description: First input of the HDMI multiplexer

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description: Second input of the HDMI multiplexer

      port@2:
        $ref: /schemas/graph.yaml#/properties/port
        description: Third input of the HDMI multiplexer

      port@3:
        $ref: /schemas/graph.yaml#/properties/port
        description: Fourth input of the HDMI multiplexer

    anyOf:
      - required:
          - port@0
      - required:
          - port@1
      - required:
          - port@2
      - required:
          - port@3

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - gpr
  - interrupts
  - ports

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/imx6qdl-clock.h>

    hdmi: hdmi@120000 {
        reg = <0x00120000 0x9000>;
        interrupts = <0 115 0x04>;
        gpr = <&gpr>;
        clocks = <&clks IMX6QDL_CLK_HDMI_IAHB>,
                 <&clks IMX6QDL_CLK_HDMI_ISFR>;
        clock-names = "iahb", "isfr";

        ports {
            #address-cells = <1>;
            #size-cells = <0>;

            port@0 {
                reg = <0>;

                hdmi_mux_0: endpoint {
                    remote-endpoint = <&ipu1_di0_hdmi>;
                };
            };

            port@1 {
                reg = <1>;

                hdmi_mux_1: endpoint {
                    remote-endpoint = <&ipu1_di1_hdmi>;
                };
            };
        };
    };

...
