# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/olimex,lcd-olinuxino.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Olimex Ltd. LCD-OLinuXino bridge panel.

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  This device can be used as bridge between a host controller and LCD panels.
  Currently supported LCDs are:
    - LCD-OLinuXino-4.3TS
    - LCD-OLinuXino-5
    - LCD-OLinuXino-7
    - LCD-OLinuXino-10

  The panel itself contains:
    - AT24C16C EEPROM holding panel identification and timing requirements
    - AR1021 resistive touch screen controller (optional)
    - FT5x6 capacitive touch screen controller (optional)
    - GT911/GT928 capacitive touch screen controller (optional)

  The above chips share same I2C bus. The EEPROM is factory preprogrammed with
  device information (id, serial, etc.) and timing requirements.

  Touchscreen bingings can be found in these files:
    - input/touchscreen/goodix.yaml
    - input/touchscreen/edt-ft5x06.txt
    - input/touchscreen/ar1021.txt

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: olimex,lcd-olinuxino

  backlight: true
  enable-gpios: true
  power-supply: true
  reg: true

required:
  - compatible
  - reg
  - power-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@50 {
            compatible = "olimex,lcd-olinuxino";
            reg = <0x50>;
            power-supply = <&reg_vcc5v0>;
            enable-gpios = <&pio 7 8 GPIO_ACTIVE_HIGH>;
            backlight = <&backlight>;
        };
    };

...
