# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/rocktech,jh057n00900.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rocktech JH057N00900 5.5" 720x1440 TFT LCD panel

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  Rocktech JH057N00900 is a 720x1440 TFT LCD panel
  connected using a MIPI-DSI video interface.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    enum:
      # Anberic RG353V-V2 5.0" 640x480 TFT LCD panel
      - anbernic,rg353v-panel-v2
      # Rocktech JH057N00900 5.5" 720x1440 TFT LCD panel
      - rocktech,jh057n00900
      # Xingbangda XBD599 5.99" 720x1440 TFT LCD panel
      - xingbangda,xbd599

  port: true
  reg:
    maxItems: 1
    description: DSI virtual channel

  vcc-supply:
    description: Panel power supply

  iovcc-supply:
    description: I/O voltage supply

  reset-gpios: true

  backlight: true

required:
  - compatible
  - reg
  - vcc-supply
  - iovcc-supply
  - reset-gpios

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "rocktech,jh057n00900";
            reg = <0>;
            vcc-supply = <&reg_2v8_p>;
            iovcc-supply = <&reg_1v8_p>;
            reset-gpios = <&gpio3 13 GPIO_ACTIVE_LOW>;
            backlight = <&backlight>;
        };
    };

...
