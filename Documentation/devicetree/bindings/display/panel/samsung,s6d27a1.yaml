# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/samsung,s6d27a1.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung S6D27A1 display panel

description: The S6D27A1 is a 480x800 DPI display panel from Samsung Mobile
  Displays (SMD).

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    const: samsung,s6d27a1

  reg: true

  interrupts:
    description: provides an optional ESD (electrostatic discharge)
      interrupt that signals abnormalities in the display hardware.
      This can also be raised for other reasons like erroneous
      configuration.
    maxItems: 1

  reset-gpios: true

  vci-supply:
    description: regulator that supplies the VCI analog voltage
      usually around 3.0 V

  vccio-supply:
    description: regulator that supplies the VCCIO voltage usually
      around 1.8 V

  backlight: true

  spi-cpha: true

  spi-cpol: true

  spi-max-frequency:
    maximum: 1200000

  port: true

required:
  - compatible
  - reg
  - vci-supply
  - vccio-supply
  - spi-cpha
  - spi-cpol
  - port

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    spi {
        compatible = "spi-gpio";
        sck-gpios = <&gpio 0 GPIO_ACTIVE_HIGH>;
        miso-gpios = <&gpio 1 GPIO_ACTIVE_HIGH>;
        mosi-gpios = <&gpio 2 GPIO_ACTIVE_HIGH>;
        cs-gpios = <&gpio 3 GPIO_ACTIVE_HIGH>;
        num-chipselects = <1>;
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "samsung,s6d27a1";
            spi-max-frequency = <1200000>;
            spi-cpha;
            spi-cpol;
            reg = <0>;
            vci-supply = <&lcd_3v0_reg>;
            vccio-supply = <&lcd_1v8_reg>;
            reset-gpios = <&gpio 4 GPIO_ACTIVE_LOW>;
            interrupt-parent = <&gpio>;
            interrupts = <5 IRQ_TYPE_EDGE_RISING>;

            port {
                panel_in: endpoint {
                    remote-endpoint = <&display_out>;
                };
            };
        };
    };

...
