# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/panel-simple-dsi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Simple DSI panels with a single power-supply

maintainers:
  - Thierry Reding <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  This binding file is a collection of the DSI panels that
  requires only a single power-supply.
  There are optionally a backlight and an enable GPIO.
  The panel may use an OF graph binding for the association to the display,
  or it may be a direct child node of the display.

  If the panel is more advanced a dedicated binding file is required.

properties:

  compatible:
    enum:
      # compatible must be listed in alphabetical order, ordered by compatible.
      # The description in the comment is mandatory for each compatible.

        # AU Optronics Corporation 8.0" WUXGA TFT LCD panel
      - auo,b080uan01
        # Boe Corporation 8.0" WUXGA TFT LCD panel
      - boe,tv080wum-nl0
        # Innolux P079ZCA 7.85" 768x1024 TFT LCD panel
      - innolux,p079zca
        # JDI FHD_R63452 1080x1920 5.2" IPS LCD Panel
      - jdi,fhd-r63452
        # Khadas TS050 5" 1080x1920 LCD panel
      - khadas,ts050
        # Kingdisplay KD097D04 9.7" 1536x2048 TFT LCD panel
      - kingdisplay,kd097d04
        # LG ACX467AKM-7 4.95" 1080×1920 LCD Panel
      - lg,acx467akm-7
        # LG Corporation 7" WXGA TFT LCD panel
      - lg,ld070wx3-sl01
        # One Stop Displays OSD101T2587-53TS 10.1" 1920x1200 panel
      - osddisplays,osd101t2587-53ts
        # Panasonic 10" WUXGA TFT LCD panel
      - panasonic,vvx10f004b00
        # Panasonic 10" WUXGA TFT LCD panel
      - panasonic,vvx10f034n00
        # Samsung s6e3fc2x01 1080x2340 AMOLED panel
      - samsung,s6e3fc2x01
        # Samsung sofef00 1080x2280 AMOLED panel
      - samsung,sofef00
        # Shangai Top Display Optoelectronics 7" TL070WSH30 1024x600 TFT LCD panel
      - tdo,tl070wsh30

  reg:
    maxItems: 1
    description: DSI virtual channel

  backlight: true
  enable-gpios: true
  reset-gpios: true
  port: true
  power-supply: true
  vddio-supply: true

allOf:
  - $ref: panel-common.yaml#
  - if:
      properties:
        compatible:
          enum:
            - samsung,s6e3fc2x01
            - samsung,sofef00
    then:
      properties:
        power-supply: false
      required:
        - vddio-supply
    else:
      properties:
        vddio-supply: false
      required:
        - power-supply

additionalProperties: false

required:
  - compatible
  - reg

examples:
  - |
    dsi {
      #address-cells = <1>;
      #size-cells = <0>;
      panel@0 {
        compatible = "panasonic,vvx10f034n00";
        reg = <0>;
        power-supply = <&vcc_lcd_reg>;

        port {
          panel: endpoint {
            remote-endpoint = <&ltdc_out>;
          };
        };
      };
    };
