# SPDX-License-Identifier: GPL-2.0-only or BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/visionox,r66451.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Visionox R66451 AMOLED DSI Panel

maintainers:
  - <PERSON> <quic_j<PERSON><PERSON><PERSON>@quicinc.com>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: visionox,r66451

  reg:
    maxItems: 1
    description: DSI virtual channel

  vddio-supply: true
  vdd-supply: true
  port: true
  reset-gpios: true

additionalProperties: false

required:
  - compatible
  - reg
  - vddio-supply
  - vdd-supply
  - reset-gpios
  - port

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "visionox,r66451";
            reg = <0>;
            vddio-supply = <&vreg_l12c_1p8>;
            vdd-supply = <&vreg_l13c_3p0>;

            reset-gpios = <&tlmm 24 GPIO_ACTIVE_LOW>;

            port {
                panel0_in: endpoint {
                    remote-endpoint = <&dsi0_out>;
                };
            };
        };
    };
...
