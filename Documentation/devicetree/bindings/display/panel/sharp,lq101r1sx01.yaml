# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/sharp,lq101r1sx01.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sharp Microelectronics 10.1" WQXGA TFT LCD panel

maintainers:
  - Thi<PERSON>ing <<EMAIL>>

description: |
  This panel requires a dual-channel DSI host to operate. It supports two modes:
  - left-right: each channel drives the left or right half of the screen
  - even-odd: each channel drives the even or odd lines of the screen

  Each of the DSI channels controls a separate DSI peripheral. The peripheral
  driven by the first link (DSI-LINK1), left or even, is considered the primary
  peripheral and controls the device. The 'link2' property contains a phandle
  to the peripheral driven by the second link (DSI-LINK2, right or odd).

  Note that in video mode the DSI-LINK1 interface always provides the left/even
  pixels and DSI-LINK2 always provides the right/odd pixels. In command mode it
  is possible to program either link to drive the left/even or right/odd pixels
  but for the sake of consistency this binding assumes that the same assignment
  is chosen as for video mode.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - const: sharp,lq101r1sx03
          - const: sharp,lq101r1sx01
      - enum:
          - sharp,lq101r1sx01

  reg: true
  power-supply: true
  backlight: true

  link2:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: |
      phandle to the DSI peripheral on the secondary link. Note that the
      presence of this property marks the containing node as DSI-LINK1

required:
  - compatible
  - reg

if:
  required:
    - link2
then:
  required:
    - power-supply

additionalProperties: false

examples:
  - |
    dsi0: dsi@fd922800 {
        #address-cells = <1>;
        #size-cells = <0>;
        reg = <0xfd922800 0x200>;

        panel: panel@0 {
            compatible = "sharp,lq101r1sx01";
            reg = <0>;

            link2 = <&secondary>;

            power-supply = <&power>;
            backlight = <&backlight>;
        };
    };

    dsi1: dsi@fd922a00 {
        #address-cells = <1>;
        #size-cells = <0>;
        reg = <0xfd922a00 0x200>;

        secondary: panel@0 {
            compatible = "sharp,lq101r1sx01";
            reg = <0>;
        };
    };

...
