# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/boe,tv101wum-nl6.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: BOE TV101WUM-NL6 DSI Display Panel

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    enum:
        # BOE TV101WUM-NL6 10.1" WUXGA TFT LCD panel
      - boe,tv101wum-nl6
        # AUO KD101N80-45NA 10.1" WUXGA TFT LCD panel
      - auo,kd101n80-45na
        # BOE TV101WUM-N53 10.1" WUXGA TFT LCD panel
      - boe,tv101wum-n53
        # AUO B101UAN08.3 10.1" WUXGA TFT LCD panel
      - auo,b101uan08.3
        # BOE TV105WUM-NW0 10.5" WUXGA TFT LCD panel
      - boe,tv105wum-nw0
        # BOE TV110C9M-LL3 10.95" WUXGA TFT LCD panel
      - boe,tv110c9m-ll3
        # INX HJ110IZ-01A 10.95" WUXGA TFT LCD panel
      - innolux,hj110iz-01a
        # STARRY 2081101QFH032011-53G 10.1" WUXGA TFT LCD panel
      - starry,2081101qfh032011-53g
        # STARRY himax83102-j02 10.51" WUXGA TFT LCD panel
      - starry,himax83102-j02
        # STARRY ili9882t 10.51" WUXGA TFT LCD panel
      - starry,ili9882t

  reg:
    description: the virtual channel number of a DSI peripheral

  enable-gpios:
    description: a GPIO spec for the enable pin

  pp1800-supply:
    description: core voltage supply

  pp3300-supply:
    description: core voltage supply

  avdd-supply:
    description: phandle of the regulator that provides positive voltage

  avee-supply:
    description: phandle of the regulator that provides negative voltage

  backlight:
    description: phandle of the backlight device attached to the panel

  port: true
  rotation: true

required:
  - compatible
  - reg
  - enable-gpios
  - pp1800-supply
  - avdd-supply
  - avee-supply

additionalProperties: false

examples:
  - |
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "boe,tv101wum-nl6";
            reg = <0>;
            enable-gpios = <&pio 45 0>;
            avdd-supply = <&ppvarn_lcd>;
            avee-supply = <&ppvarp_lcd>;
            pp1800-supply = <&pp1800_lcd>;
            backlight = <&backlight_lcd0>;
            port {
                panel_in: endpoint {
                    remote-endpoint = <&dsi_out>;
                };
            };
        };
    };

...
