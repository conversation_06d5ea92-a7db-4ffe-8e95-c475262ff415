# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/ilitek,ili9163.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ilitek ILI9163 display panels

maintainers:
  - <PERSON> <<EMAIL>>

description:
  This binding is for display panels using an Ilitek ILI9163 controller in SPI
  mode.

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    items:
      - enum:
          - newhaven,1.8-128160EF
      - const: ilitek,ili9163

  spi-max-frequency:
    maximum: 32000000

  dc-gpios:
    maxItems: 1
    description: Display data/command selection (D/CX)

  backlight: true
  reg: true
  reset-gpios: true
  rotation: true

required:
  - compatible
  - reg
  - dc-gpios
  - reset-gpios

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    backlight: backlight {
            compatible = "gpio-backlight";
            gpios = <&gpio 22 GPIO_ACTIVE_HIGH>;
    };
    spi {
            #address-cells = <1>;
            #size-cells = <0>;

            display@0 {
                    compatible = "newhaven,1.8-128160EF", "ilitek,ili9163";
                    reg = <0>;
                    spi-max-frequency = <32000000>;
                    dc-gpios = <&gpio0 24 GPIO_ACTIVE_HIGH>;
                    reset-gpios = <&gpio0 25 GPIO_ACTIVE_HIGH>;
                    rotation = <180>;
                    backlight = <&backlight>;
            };
    };

...
