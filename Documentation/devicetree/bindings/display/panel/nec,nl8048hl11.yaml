# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/nec,nl8048hl11.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NEC NL8048HL11 4.1" WVGA TFT LCD panel

description:
  The NEC NL8048HL11 is a 4.1" WVGA TFT LCD panel with a 24-bit RGB parallel
  data interface and an SPI control interface.

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    const: nec,nl8048hl11

  label: true
  port: true
  reg: true
  reset-gpios: true

  spi-max-frequency:
    maximum: 10000000

required:
  - compatible
  - reg
  - reset-gpios
  - port

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      lcd_panel: panel@0 {
        compatible = "nec,nl8048hl11";
        reg = <0>;
        spi-max-frequency = <10000000>;

        reset-gpios = <&gpio7 7 GPIO_ACTIVE_LOW>;

        port {
          lcd_in: endpoint {
            remote-endpoint = <&dpi_out>;
          };
        };
      };
    };

...
