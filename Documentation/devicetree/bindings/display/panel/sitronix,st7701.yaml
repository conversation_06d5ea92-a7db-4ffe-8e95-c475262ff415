# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/sitronix,st7701.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sitronix ST7701 based LCD panels

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  ST7701 designed for small and medium sizes of TFT LCD display, is
  capable of supporting up to 480RGBX864 in resolution. It provides
  several system interfaces like MIPI/RGB/SPI.

  Techstar TS8550B is 480x854, 2-lane MIPI DSI LCD panel which has
  inbuilt ST7701 chip.

  Densitron DMT028VGHMCMI-1A is 480x640, 2-lane MIPI DSI LCD panel
  which has built-in ST7701 chip.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - densitron,dmt028vghmcmi-1a
          - elida,kd50t048a
          - techstar,ts8550b
      - const: sitronix,st7701

  reg:
    description: DSI virtual channel used by that screen
    maxItems: 1

  VCC-supply:
    description: analog regulator for MIPI circuit

  IOVCC-supply:
    description: I/O system regulator

  port: true
  reset-gpios: true
  rotation: true

  backlight: true

required:
  - compatible
  - reg
  - VCC-supply
  - IOVCC-supply
  - port
  - reset-gpios

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "techstar,ts8550b", "sitronix,st7701";
            reg = <0>;
            VCC-supply = <&reg_dldo2>;
            IOVCC-supply = <&reg_dldo2>;
            reset-gpios = <&pio 3 24 GPIO_ACTIVE_HIGH>; /* LCD-RST: PD24 */
            backlight = <&backlight>;

            port {
                mipi_in_panel: endpoint {
                    remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };
