# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/panel-lvds.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Generic LVDS Display Panel

maintainers:
  - La<PERSON> <<EMAIL>>
  - T<PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/display/lvds.yaml#

select:
  properties:
    compatible:
      contains:
        const: panel-lvds

  not:
    properties:
      compatible:
        contains:
          enum:
            - advantech,idk-1110wr
            - advantech,idk-2121wr
            - innolux,ee101ia-01d
            - mitsubishi,aa104xd12
            - mitsubishi,aa121td01
            - sgd,gktw70sdae4se

  required:
    - compatible

properties:
  compatible:
    items:
      - enum:
          - auo,b101ew05
          # Chunghwa Picture Tubes Ltd. 7" WXGA (800x1280) TFT LCD LVDS panel
          - chunghwa,claa070wp03xg
          # HannStar Display Corp. HSD101PWW2 10.1" WXGA (1280x800) LVDS panel
          - hannstar,hsd101pww2
          # Hydis Technologies 7" WXGA (800x1280) TFT LCD LVDS panel
          - hydis,hv070wx2-1e0
          - tbs,a711-panel

      - const: panel-lvds

unevaluatedProperties: false

required:
  - compatible
  - data-mapping
  - width-mm
  - height-mm
  - panel-timing
  - port

...
