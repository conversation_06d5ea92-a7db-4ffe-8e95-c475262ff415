# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/samsung,lms397kf04.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung LMS397KF04 display panel

description: The datasheet claims this is based around a display controller
  named DB7430 with a separate backlight controller.

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    const: samsung,lms397kf04

  reg: true

  reset-gpios: true

  vci-supply:
    description: regulator that supplies the VCI analog voltage
      usually around 3.0 V

  vccio-supply:
    description: regulator that supplies the VCCIO voltage usually
      around 1.8 V

  backlight: true

  spi-cpha: true

  spi-cpol: true

  spi-max-frequency:
    description: inherited as a SPI client node, the datasheet specifies
      maximum 300 ns minimum cycle which gives around 3 MHz max frequency
    maximum: 3000000

  port: true

required:
  - compatible
  - reg
  - spi-cpha
  - spi-cpol
  - port

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    spi {
      compatible = "spi-gpio";
      sck-gpios = <&gpio 0 GPIO_ACTIVE_HIGH>;
      miso-gpios = <&gpio 1 GPIO_ACTIVE_HIGH>;
      mosi-gpios = <&gpio 2 GPIO_ACTIVE_HIGH>;
      cs-gpios = <&gpio 3 GPIO_ACTIVE_HIGH>;
      num-chipselects = <1>;
      #address-cells = <1>;
      #size-cells = <0>;
      panel@0 {
        compatible = "samsung,lms397kf04";
        spi-max-frequency = <3000000>;
        spi-cpha;
        spi-cpol;
        reg = <0>;
        vci-supply = <&lcd_3v0_reg>;
        vccio-supply = <&lcd_1v8_reg>;
        reset-gpios = <&gpio 4 GPIO_ACTIVE_LOW>;
        backlight = <&ktd259>;

        port {
          panel_in: endpoint {
            remote-endpoint = <&display_out>;
          };
        };
      };
    };

...
