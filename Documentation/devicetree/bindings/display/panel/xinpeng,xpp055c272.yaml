# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/xinpeng,xpp055c272.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xinpeng XPP055C272 5.5in 720x1280 DSI panel

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: xinpeng,xpp055c272
  reg: true
  backlight: true
  port: true
  reset-gpios: true
  iovcc-supply:
    description: regulator that supplies the iovcc voltage
  vci-supply:
    description: regulator that supplies the vci voltage

required:
  - compatible
  - reg
  - backlight
  - port
  - iovcc-supply
  - vci-supply

additionalProperties: false

examples:
  - |
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "xinpeng,xpp055c272";
            reg = <0>;
            backlight = <&backlight>;
            iovcc-supply = <&vcc_1v8>;
            vci-supply = <&vcc3v3_lcd>;

            port {
                mipi_in_panel: endpoint {
                    remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };

...
