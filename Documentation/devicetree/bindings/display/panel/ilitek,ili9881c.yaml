# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/ilitek,ili9881c.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ilitek ILI9881c based MIPI-DSI panels

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - bananapi,lhr050h41
          - feixin,k101-im2byl02
          - tdo,tl050hdv35
          - wanchanglong,w552946aba
      - const: ilitek,ili9881c

  backlight: true
  power-supply: true
  reg: true
  reset-gpios: true
  rotation: true

required:
  - compatible
  - power-supply
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "bananapi,lhr050h41", "ilitek,ili9881c";
            reg = <0>;
            power-supply = <&reg_display>;
            reset-gpios = <&r_pio 0 5 GPIO_ACTIVE_LOW>; /* PL05 */
            backlight = <&pwm_bl>;
        };
    };

...
