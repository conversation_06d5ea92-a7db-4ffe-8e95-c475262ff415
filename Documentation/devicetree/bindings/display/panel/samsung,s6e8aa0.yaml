# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/samsung,s6e8aa0.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung S6E8AA0 AMOLED LCD 5.3 inch panel

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: samsung,s6e8aa0

  reg: true
  reset-gpios: true
  display-timings: true

  vdd3-supply:
    description: core voltage supply

  vci-supply:
    description: voltage supply for analog circuits

  power-on-delay:
    description: delay after turning regulators on [ms]
    $ref: /schemas/types.yaml#/definitions/uint32

  reset-delay:
    description: delay after reset sequence [ms]
    $ref: /schemas/types.yaml#/definitions/uint32

  init-delay:
    description: delay after initialization sequence [ms]
    $ref: /schemas/types.yaml#/definitions/uint32

  panel-width-mm:
    description: physical panel width [mm]

  panel-height-mm:
    description: physical panel height [mm]

  flip-horizontal:
    description: boolean to flip image horizontally
    type: boolean

  flip-vertical:
    description: boolean to flip image vertically
    type: boolean

required:
  - compatible
  - reg
  - vdd3-supply
  - vci-supply
  - reset-gpios
  - display-timings

additionalProperties: false

examples:
  - |
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "samsung,s6e8aa0";
            reg = <0>;
            vdd3-supply = <&vcclcd_reg>;
            vci-supply = <&vlcd_reg>;
            reset-gpios = <&gpy4 5 0>;
            power-on-delay = <50>;
            reset-delay = <100>;
            init-delay = <100>;
            panel-width-mm = <58>;
            panel-height-mm = <103>;
            flip-horizontal;
            flip-vertical;

            display-timings {
                timing0: timing-0 {
                    clock-frequency = <57153600>;
                    hactive = <720>;
                    vactive = <1280>;
                    hfront-porch = <5>;
                    hback-porch = <5>;
                    hsync-len = <5>;
                    vfront-porch = <13>;
                    vback-porch = <1>;
                    vsync-len = <2>;
                };
            };
        };
    };

...
