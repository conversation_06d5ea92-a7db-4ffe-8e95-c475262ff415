# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/jadard,jd9365da-h3.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Jadard JD9365DA-HE WXGA DSI panel

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - chongzhou,cz101b4001
          - radxa,display-10hd-ad001
          - radxa,display-8hd-ad002
      - const: jadard,jd9365da-h3

  reg: true

  vdd-supply:
    description: supply regulator for VDD, usually 3.3V

  vccio-supply:
    description: supply regulator for VCCIO, usually 1.8V

  reset-gpios: true

  backlight: true

  port: true

required:
  - compatible
  - reg
  - vdd-supply
  - vccio-supply
  - reset-gpios

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/pinctrl/rockchip.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "chongzhou,cz101b4001", "jadard,jd9365da-h3";
            reg = <0>;
            vdd-supply = <&lcd_3v3>;
            vccio-supply = <&vcca_1v8>;
            reset-gpios = <&gpio1 RK_PC2 GPIO_ACTIVE_HIGH>;
            backlight = <&backlight>;

            port {
                mipi_in_panel: endpoint {
                    remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };

...
