# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/elida,kd35t133.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Elida KD35T133 3.5in 320x480 DSI panel

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: elida,kd35t133
  reg: true
  backlight: true
  port: true
  reset-gpios: true
  rotation: true
  iovcc-supply:
    description: regulator that supplies the iovcc voltage
  vdd-supply:
    description: regulator that supplies the vdd voltage

required:
  - compatible
  - reg
  - backlight
  - port
  - iovcc-supply
  - vdd-supply

additionalProperties: false

examples:
  - |
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "elida,kd35t133";
            reg = <0>;
            backlight = <&backlight>;
            iovcc-supply = <&vcc_1v8>;
            vdd-supply = <&vcc3v3_lcd>;

            port {
                mipi_in_panel: endpoint {
                    remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };

...
