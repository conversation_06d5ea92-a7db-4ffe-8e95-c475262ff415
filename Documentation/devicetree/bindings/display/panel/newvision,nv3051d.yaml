# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/newvision,nv3051d.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NewVision NV3051D based LCD panel

description: |
  The NewVision NV3051D is a driver chip used to drive DSI panels. For now,
  this driver only supports the 640x480 panels found in the Anbernic RG353
  based devices.

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - anbernic,rg353p-panel
          - anbernic,rg353v-panel
      - const: newvision,nv3051d

  reg: true
  backlight: true
  port: true
  reset-gpios:
    description: Active low reset GPIO
  vdd-supply: true

required:
  - compatible
  - reg
  - backlight

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "anbernic,rg353p-panel", "newvision,nv3051d";
            reg = <0>;
            backlight = <&backlight>;
            reset-gpios = <&gpio4 0 GPIO_ACTIVE_LOW>;
            vdd-supply = <&vcc3v3_lcd>;

            port {
                mipi_in_panel: endpoint {
                    remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };

...
