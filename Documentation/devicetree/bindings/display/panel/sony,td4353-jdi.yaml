# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/sony,td4353-jdi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sony TD4353 JDI 5 / 5.7" 2160x1080 MIPI-DSI Panel

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Sony TD4353 JDI is a 5 (XZ2c) / 5.7 (XZ2) inch 2160x1080
  MIPI-DSI panel, used in Xperia XZ2 and XZ2 Compact smartphones.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: sony,td4353-jdi-tama

  reg: true

  backlight: true

  vddio-supply:
    description: VDDIO 1.8V supply

  vsp-supply:
    description: Positive 5.5V supply

  vsn-supply:
    description: Negative 5.5V supply

  panel-reset-gpios:
    description: Display panel reset pin

  touch-reset-gpios:
    description: Touch panel reset pin

  port: true

required:
  - compatible
  - reg
  - vddio-supply
  - vsp-supply
  - vsn-supply
  - panel-reset-gpios
  - touch-reset-gpios
  - port

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel: panel@0 {
            compatible = "sony,td4353-jdi-tama";
            reg = <0>;

            backlight = <&pmi8998_wled>;
            vddio-supply = <&vreg_l14a_1p8>;
            vsp-supply = <&lab>;
            vsn-supply = <&ibb>;
            panel-reset-gpios = <&tlmm 6 GPIO_ACTIVE_HIGH>;
            touch-reset-gpios = <&tlmm 99 GPIO_ACTIVE_HIGH>;

            port {
                panel_in: endpoint {
                    remote-endpoint = <&dsi0_out>;
                };
            };
        };
    };
...
