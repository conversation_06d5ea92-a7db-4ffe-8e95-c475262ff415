# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/sitronix,st7789v.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sitronix ST7789V RGB panel with SPI control bus

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    enum:
      - edt,et028013dma
      - inanbo,t28cp45tn89-v17
      - jasonic,jt240mhqs-hwt-ek-e3
      - sitronix,st7789v

  reg: true
  reset-gpios: true
  power-supply: true
  backlight: true
  port: true
  rotation: true

  spi-cpha: true
  spi-cpol: true

  spi-rx-bus-width:
    minimum: 0
    maximum: 1

  dc-gpios:
    maxItems: 1
    description: DCX pin, Display data/command selection pin in parallel interface

required:
  - compatible
  - reg
  - power-supply

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    spi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "sitronix,st7789v";
            reg = <0>;
            reset-gpios = <&pio 6 11 GPIO_ACTIVE_LOW>;
            backlight = <&pwm_bl>;
            power-supply = <&power>;
            rotation = <180>;
            spi-max-frequency = <100000>;
            spi-cpol;
            spi-cpha;

            port {
                panel_input: endpoint {
                    remote-endpoint = <&tcon0_out_panel>;
                };
            };
        };
    };

...
