# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/novatek,nt36523.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Novatek NT36523 based DSI display Panels

maintainers:
  - <PERSON><PERSON><PERSON> Lu <<EMAIL>>

description: |
  The Novatek NT36523 is a generic DSI Panel IC used to drive dsi
  panels. Support video mode panels from China Star Optoelectronics
  Technology (CSOT) and BOE Technology.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - xiaomi,elish-boe-nt36523
              - xiaomi,elish-csot-nt36523
          - const: novatek,nt36523
      - items:
          - enum:
              - lenovo,j606f-boe-nt36523w
          - const: novatek,nt36523w

  reset-gpios:
    maxItems: 1
    description: phandle of gpio for reset line - This should be 8mA

  vddio-supply:
    description: regulator that supplies the I/O voltage

  reg: true
  ports: true
  rotation: true
  backlight: true

required:
  - compatible
  - reg
  - vddio-supply
  - reset-gpios
  - ports

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "xiaomi,elish-csot-nt36523", "novatek,nt36523";
            reg = <0>;

            vddio-supply = <&vreg_l14a_1p88>;
            reset-gpios = <&tlmm 75 GPIO_ACTIVE_LOW>;
            backlight = <&backlight>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    panel_in_0: endpoint {
                        remote-endpoint = <&dsi0_out>;
                    };
                };

                port@1{
                    reg = <1>;
                    panel_in_1: endpoint {
                        remote-endpoint = <&dsi1_out>;
                    };
                };
            };
        };
    };

...
