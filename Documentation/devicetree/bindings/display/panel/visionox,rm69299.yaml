# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/visionox,rm69299.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Visionox model RM69299 Panels

maintainers:
  - <PERSON><PERSON><PERSON>dan P <<EMAIL>>

description: |
  This binding is for display panels using a Visionox RM692999 panel.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: visionox,rm69299-1080p-display

  reg: true

  vdda-supply:
    description: |
      Phandle of the regulator that provides the vdda supply voltage.

  vdd3p3-supply:
    description: |
      <PERSON>and<PERSON> of the regulator that provides the vdd3p3 supply voltage.

  port: true
  reset-gpios: true

additionalProperties: false

required:
  - compatible
  - reg
  - vdda-supply
  - vdd3p3-supply
  - reset-gpios
  - port

examples:
  - |
    dsi {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "visionox,rm69299-1080p-display";
            reg = <0>;

            vdda-supply = <&src_pp1800_l8c>;
            vdd3p3-supply = <&src_pp2800_l18a>;

            reset-gpios = <&pm6150l_gpio 3 0>;
            port {
                panel0_in: endpoint {
                    remote-endpoint = <&dsi0_out>;
                };
            };
        };
    };
...
