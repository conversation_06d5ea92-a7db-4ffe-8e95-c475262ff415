# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/ilitek,ili9341.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ilitek-9341 Display Panel

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Ilitek ILI9341 TFT panel driver with SPI control bus
  This is a driver for 320x240 TFT panels, accepting a rgb input
  streams with 16 bits or 18 bits.

allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    items:
      - enum:
          - adafruit,yx240qv29
          # ili9341 240*320 Color on stm32f429-disco board
          - st,sf-tc240t-9370-t
          - canaan,kd233-tft
      - const: ilitek,ili9341

  reg: true

  dc-gpios:
    maxItems: 1
    description: Display data/command selection (D/CX) of this DBI panel

  spi-3wire: true

  spi-max-frequency:
    const: 10000000

  port: true

  vci-supply:
    description: Analog voltage supply (2.5 .. 3.3V)

  vddi-supply:
    description: Voltage supply for interface logic (1.65 .. 3.3 V)

  vddi-led-supply:
    description: Voltage supply for the LED driver (1.65 .. 3.3 V)

unevaluatedProperties: false

required:
  - compatible
  - reg
  - dc-gpios

if:
  properties:
    compatible:
      contains:
        enum:
          - st,sf-tc240t-9370-t
then:
  required:
    - port

examples:
  - |+
    #include <dt-bindings/gpio/gpio.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel: display@0 {
            compatible = "st,sf-tc240t-9370-t",
                         "ilitek,ili9341";
            reg = <0>;
            spi-3wire;
            spi-max-frequency = <10000000>;
            dc-gpios = <&gpiod 13 0>;
            port {
                panel_in: endpoint {
                    remote-endpoint = <&display_out>;
                };
            };
        };
        display@1{
            compatible = "adafruit,yx240qv29", "ilitek,ili9341";
            reg = <1>;
            spi-max-frequency = <10000000>;
            dc-gpios = <&gpio0 9 GPIO_ACTIVE_HIGH>;
            reset-gpios = <&gpio0 8 GPIO_ACTIVE_HIGH>;
            rotation = <270>;
            backlight = <&backlight>;
        };
    };
...
