# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/himax,hx8394.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Himax HX8394 MIPI-DSI LCD panel controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  Device tree bindings for panels based on the Himax HX8394 controller,
  such as the HannStar HSD060BHW4 720x1440 TFT LCD panel connected with
  a MIPI-DSI video interface.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - hannstar,hsd060bhw4
      - const: himax,hx8394

  reg: true

  reset-gpios: true

  backlight: true

  port: true

  vcc-supply:
    description: Panel power supply

  iovcc-supply:
    description: I/O voltage supply

required:
  - compatible
  - reg
  - reset-gpios
  - backlight
  - port
  - vcc-supply
  - iovcc-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    dsi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "hannstar,hsd060bhw4", "himax,hx8394";
            reg = <0>;
            vcc-supply = <&reg_2v8_p>;
            iovcc-supply = <&reg_1v8_p>;
            reset-gpios = <&gpio3 13 GPIO_ACTIVE_LOW>;
            backlight = <&backlight>;

            port {
                mipi_in_panel: endpoint {
                    remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };

...
