# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/samsung,ams495qa01.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung AMS495QA01 panel with Magnachip D53E6EA8966 controller

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    const: samsung,ams495qa01

  reg: true
  reset-gpios:
    description: reset gpio, must be GPIO_ACTIVE_LOW
  elvdd-supply:
    description: regulator that supplies voltage to the panel display
  enable-gpios: true
  port: true
  vdd-supply:
    description: regulator that supplies voltage to panel logic

required:
  - compatible
  - reg
  - reset-gpios
  - vdd-supply

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    spi {
        #address-cells = <1>;
        #size-cells = <0>;
        panel@0 {
            compatible = "samsung,ams495qa01";
            reg = <0>;
            reset-gpios = <&gpio4 0 GPIO_ACTIVE_LOW>;
            vdd-supply = <&vcc_3v3>;

            port {
                mipi_in_panel: endpoint {
                  remote-endpoint = <&mipi_out_panel>;
                };
            };
        };
    };

...
