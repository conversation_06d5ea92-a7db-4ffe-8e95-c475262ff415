# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/tpo,tpg110.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TPO TPG110 Panel

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |+
  This panel driver is a component that acts as an intermediary
  between an RGB output and a variety of panels. The panel
  driver is strapped up in electronics to the desired resolution
  and other properties, and has a control interface over 3WIRE
  SPI. By talking to the TPG110 over SPI, the strapped properties
  can be discovered and the hardware is therefore mostly
  self-describing.

         +--------+
  SPI -> |  TPO   | -> physical display
  RGB -> | TPG110 |
         +--------+

  If some electrical strap or alternate resolution is desired,
  this can be set up by taking software control of the display
  over the SPI interface. The interface can also adjust
  for properties of the display such as gamma correction and
  certain electrical driving levels.

  The TPG110 does not know the physical dimensions of the panel
  connected, so this needs to be specified in the device tree.

  It requires a GPIO line for control of its reset line.

  The serial protocol has line names that resemble I2C but the
  protocol is not I2C but 3WIRE SPI.


allOf:
  - $ref: panel-common.yaml#
  - $ref: /schemas/spi/spi-peripheral-props.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - ste,nomadik-nhk15-display
          - const: tpo,tpg110
      - const: tpo,tpg110

  reg: true

  grestb-gpios:
    maxItems: 1
    description: panel reset GPIO

  spi-3wire: true

  spi-max-frequency:
    const: 3000000

required:
  - compatible
  - reg
  - grestb-gpios
  - width-mm
  - height-mm
  - spi-3wire
  - spi-max-frequency
  - port

unevaluatedProperties: false

examples:
  - |+
    spi {
      #address-cells = <1>;
      #size-cells = <0>;

      panel: display@0 {
        compatible = "tpo,tpg110";
        reg = <0>;
        spi-3wire;
        /* 320 ns min period ~= 3 MHz */
        spi-max-frequency = <3000000>;
        /* Width and height from data sheet */
        width-mm = <116>;
        height-mm = <87>;
        grestb-gpios = <&foo_gpio 5 1>;
        backlight = <&bl>;

        port {
          nomadik_clcd_panel: endpoint {
            remote-endpoint = <&foo>;
          };
        };
      };
    };

...
