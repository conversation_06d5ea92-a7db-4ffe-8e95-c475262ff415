# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/panel/novatek,nt36672a.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Novatek NT36672A based DSI display Panels

maintainers:
  - Sumit Semwal <<EMAIL>>

description: |
  The nt36672a IC from Novatek is a generic DSI Panel IC used to drive dsi
  panels.
  Right now, support is added only for a Tianma FHD+ LCD display panel with a
  resolution of 1080x2246. It is a video mode DSI panel.

allOf:
  - $ref: panel-common.yaml#

properties:
  compatible:
    items:
      - enum:
          - tianma,fhd-video
      - const: novatek,nt36672a
    description: This indicates the panel manufacturer of the panel that is
      in turn using the NT36672A panel driver. This compatible string
      determines how the NT36672A panel driver is configured for the indicated
      panel. The novatek,nt36672a compatible shall always be provided as a fallback.

  reset-gpios:
    maxItems: 1
    description: phandle of gpio for reset line - This should be 8mA, gpio
      can be configured using mux, pinctrl, pinctrl-names (active high)

  vddio-supply:
    description: phandle of the regulator that provides the supply voltage
      Power IC supply

  vddpos-supply:
    description: phandle of the positive boost supply regulator

  vddneg-supply:
    description: phandle of the negative boost supply regulator

  reg: true
  port: true
  backlight: true

required:
  - compatible
  - reg
  - vddio-supply
  - vddpos-supply
  - vddneg-supply
  - reset-gpios
  - port

unevaluatedProperties: false

examples:
  - |+
    #include <dt-bindings/gpio/gpio.h>

    dsi0 {
        #address-cells = <1>;
        #size-cells = <0>;

        panel@0 {
            compatible = "tianma,fhd-video", "novatek,nt36672a";
            reg = <0>;
            vddio-supply = <&vreg_l14a_1p88>;
            vddpos-supply = <&lab>;
            vddneg-supply = <&ibb>;

            backlight = <&pmi8998_wled>;
            reset-gpios = <&tlmm 6 GPIO_ACTIVE_HIGH>;

            port {
                tianma_nt36672a_in_0: endpoint {
                    remote-endpoint = <&dsi0_out>;
                };
            };
        };
    };

...
