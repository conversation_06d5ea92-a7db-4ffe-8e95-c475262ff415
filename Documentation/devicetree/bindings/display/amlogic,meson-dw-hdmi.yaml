# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 BayLibre, SAS
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/amlogic,meson-dw-hdmi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic specific extensions to the Synopsys Designware HDMI Controller

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: /schemas/sound/dai-common.yaml#

description: |
  The Amlogic Meson Synopsys Designware Integration is composed of
  - A Synopsys DesignWare HDMI Controller IP
  - A TOP control block controlling the Clocks and PHY
  - A custom HDMI PHY in order to convert video to TMDS signal
   ___________________________________
  |            HDMI TOP               |<= HPD
  |___________________________________|
  |                  |                |
  |  Synopsys HDMI   |   HDMI PHY     |=> TMDS
  |    Controller    |________________|
  |___________________________________|<=> DDC

  The HDMI TOP block only supports HPD sensing.
  The Synopsys HDMI Controller interrupt is routed through the
  TOP Block interrupt.
  Communication to the TOP Block and the Synopsys HDMI Controller is done
  via a pair of dedicated addr+read/write registers.
  The HDMI PHY is configured by registers in the HHI register block.

  Pixel data arrives in "4:4:4" format from the VENC block and the VPU HDMI mux
  selects either the ENCI encoder for the 576i or 480i formats or the ENCP
  encoder for all the other formats including interlaced HD formats.

  The VENC uses a DVI encoder on top of the ENCI or ENCP encoders to generate
  DVI timings for the HDMI controller.

  Amlogic Meson GXBB, GXL and GXM SoCs families embeds the Synopsys DesignWare
  HDMI TX IP version 2.01a with HDCP and I2C & S/PDIF
  audio source interfaces.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - amlogic,meson-gxbb-dw-hdmi # GXBB (S905)
              - amlogic,meson-gxl-dw-hdmi # GXL (S905X, S905D)
              - amlogic,meson-gxm-dw-hdmi # GXM (S912)
          - const: amlogic,meson-gx-dw-hdmi
      - enum:
          - amlogic,meson-g12a-dw-hdmi # G12A (S905X2, S905Y2, S905D2)

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 3

  clock-names:
    items:
      - const: isfr
      - const: iahb
      - const: venci

  resets:
    minItems: 3

  reset-names:
    items:
      - const: hdmitx_apb
      - const: hdmitx
      - const: hdmitx_phy

  hdmi-supply:
    description: phandle to an external 5V regulator to power the HDMI logic

  port@0:
    $ref: /schemas/graph.yaml#/properties/port
    description:
      A port node pointing to the VENC Input port node.

  port@1:
    $ref: /schemas/graph.yaml#/properties/port
    description:
      A port node pointing to the TMDS Output port node.

  "#address-cells":
    const: 1

  "#size-cells":
    const: 0

  "#sound-dai-cells":
    const: 0

  sound-name-prefix: true

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - resets
  - reset-names
  - port@0
  - port@1
  - "#address-cells"
  - "#size-cells"

additionalProperties: false

examples:
  - |
    hdmi_tx: hdmi-tx@c883a000 {
        compatible = "amlogic,meson-gxbb-dw-hdmi", "amlogic,meson-gx-dw-hdmi";
        reg = <0xc883a000 0x1c>;
        interrupts = <57>;
        resets = <&reset_apb>, <&reset_hdmitx>, <&reset_hdmitx_phy>;
        reset-names = "hdmitx_apb", "hdmitx", "hdmitx_phy";
        clocks = <&clk_isfr>, <&clk_iahb>, <&clk_venci>;
        clock-names = "isfr", "iahb", "venci";
        #address-cells = <1>;
        #size-cells = <0>;

        /* VPU VENC Input */
        hdmi_tx_venc_port: port@0 {
            reg = <0>;

            hdmi_tx_in: endpoint {
                remote-endpoint = <&hdmi_tx_out>;
            };
        };

        /* TMDS Output */
        hdmi_tx_tmds_port: port@1 {
             reg = <1>;

             hdmi_tx_tmds_out: endpoint {
                 remote-endpoint = <&hdmi_connector_in>;
             };
        };
    };
