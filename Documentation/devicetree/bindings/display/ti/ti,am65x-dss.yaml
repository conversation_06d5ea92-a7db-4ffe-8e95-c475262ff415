# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Texas Instruments Incorporated
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/ti/ti,am65x-dss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments AM65x Display Subsystem

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The AM625 and AM65x TI Keystone Display SubSystem with two output
  ports and two video planes. In AM65x DSS, the first video port
  supports 1 OLDI TX and in AM625 DSS, the first video port output is
  internally routed to 2 OLDI TXes. The second video port supports DPI
  format. The first plane is full video plane with all features and the
  second is a "lite plane" without scaling support.

properties:
  compatible:
    enum:
      - ti,am625-dss
      - ti,am65x-dss

  reg:
    description:
      Addresses to each DSS memory region described in the SoC's TRM.
    items:
      - description: common DSS register area
      - description: VIDL1 light video plane
      - description: VID video plane
      - description: OVR1 overlay manager for vp1
      - description: OVR2 overlay manager for vp2
      - description: VP1 video port 1
      - description: VP2 video port 2

  reg-names:
    items:
      - const: common
      - const: vidl1
      - const: vid
      - const: ovr1
      - const: ovr2
      - const: vp1
      - const: vp2

  clocks:
    items:
      - description: fck DSS functional clock
      - description: vp1 Video Port 1 pixel clock
      - description: vp2 Video Port 2 pixel clock

  clock-names:
    items:
      - const: fck
      - const: vp1
      - const: vp2

  assigned-clocks:
    minItems: 1
    maxItems: 3

  assigned-clock-parents:
    minItems: 1
    maxItems: 3

  interrupts:
    maxItems: 1

  power-domains:
    maxItems: 1
    description: phandle to the associated power domain

  dma-coherent:
    type: boolean

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description:
          For AM65x DSS, the OLDI output port node from video port 1.
          For AM625 DSS, the internal DPI output port node from video
          port 1.

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description:
          The DSS DPI output port node from video port 2

  ti,am65x-oldi-io-ctrl:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      phandle to syscon device node mapping OLDI IO_CTRL registers.
      The mapped range should point to OLDI_DAT0_IO_CTRL, map it and
      following OLDI_DAT1_IO_CTRL, OLDI_DAT2_IO_CTRL, OLDI_DAT3_IO_CTRL,
      and OLDI_CLK_IO_CTRL registers. This property is needed for OLDI
      interface to work.

  max-memory-bandwidth:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Input memory (from main memory to dispc) bandwidth limit in
      bytes per second

required:
  - compatible
  - reg
  - reg-names
  - clocks
  - clock-names
  - interrupts
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/soc/ti,sci_pm_domain.h>

    dss: dss@4a00000 {
            compatible = "ti,am65x-dss";
            reg =   <0x04a00000 0x1000>, /* common */
                    <0x04a02000 0x1000>, /* vidl1 */
                    <0x04a06000 0x1000>, /* vid */
                    <0x04a07000 0x1000>, /* ovr1 */
                    <0x04a08000 0x1000>, /* ovr2 */
                    <0x04a0a000 0x1000>, /* vp1 */
                    <0x04a0b000 0x1000>; /* vp2 */
            reg-names = "common", "vidl1", "vid",
                    "ovr1", "ovr2", "vp1", "vp2";
            ti,am65x-oldi-io-ctrl = <&dss_oldi_io_ctrl>;
            power-domains = <&k3_pds 67 TI_SCI_PD_EXCLUSIVE>;
            clocks =        <&k3_clks 67 1>,
                            <&k3_clks 216 1>,
                            <&k3_clks 67 2>;
            clock-names = "fck", "vp1", "vp2";
            interrupts = <GIC_SPI 166 IRQ_TYPE_EDGE_RISING>;
            ports {
                    #address-cells = <1>;
                    #size-cells = <0>;
                    port@0 {
                            reg = <0>;
                            oldi_out0: endpoint {
                                    remote-endpoint = <&lcd_in0>;
                            };
                    };
            };
    };
