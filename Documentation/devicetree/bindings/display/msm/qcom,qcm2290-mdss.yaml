# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,qcm2290-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm QCM220 Display MDSS

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  Device tree bindings for MSM Mobile Display Subsystem(MDSS) that encapsulates
  sub-blocks like DPU display controller and DSI. Device tree bindings of MDSS
  are mentioned for QCM2290 target.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,qcm2290-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display AXI clock
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: bus
      - const: core

  iommus:
    maxItems: 2

  interconnects:
    maxItems: 1

  interconnect-names:
    maxItems: 1

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,qcm2290-dpu

  "^dsi@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,dsi-ctrl-6g-qcm2290

  "^phy@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,dsi-phy-14nm-2290

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-qcm2290.h>
    #include <dt-bindings/clock/qcom,gcc-qcm2290.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interconnect/qcom,qcm2290.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-subsystem@5e00000 {
        #address-cells = <1>;
        #size-cells = <1>;
        compatible = "qcom,qcm2290-mdss";
        reg = <0x05e00000 0x1000>;
        reg-names = "mdss";
        power-domains = <&dispcc MDSS_GDSC>;
        clocks = <&gcc GCC_DISP_AHB_CLK>,
                 <&gcc GCC_DISP_HF_AXI_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface", "bus", "core";

        interrupts = <GIC_SPI 186 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        interconnects = <&mmrt_virt MASTER_MDP0 &bimc SLAVE_EBI1>;
        interconnect-names = "mdp0-mem";

        iommus = <&apps_smmu 0x420 0x2>,
                 <&apps_smmu 0x421 0x0>;
        ranges;

        display-controller@5e01000 {
            compatible = "qcom,qcm2290-dpu";
            reg = <0x05e01000 0x8f000>,
                  <0x05eb0000 0x2008>;
            reg-names = "mdp", "vbif";

            clocks = <&gcc GCC_DISP_HF_AXI_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_LUT_CLK>,
                     <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            clock-names = "bus", "iface", "core", "lut", "vsync";

            operating-points-v2 = <&mdp_opp_table>;
            power-domains = <&rpmpd QCM2290_VDDCX>;

            interrupt-parent = <&mdss>;
            interrupts = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dpu_intf1_out: endpoint {
                        remote-endpoint = <&dsi0_in>;
                    };
                };
            };
        };

        dsi@5e94000 {
            compatible = "qcom,dsi-ctrl-6g-qcm2290";
            reg = <0x05e94000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <4>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_HF_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";
            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>, <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
            assigned-clock-parents = <&dsi0_phy 0>, <&dsi0_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmpd QCM2290_VDDCX>;

            phys = <&dsi0_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi0_in: endpoint {
                        remote-endpoint = <&dpu_intf1_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi0_out: endpoint {
                    };
                };
            };
        };

        dsi0_phy: phy@5e94400 {
            compatible = "qcom,dsi-phy-14nm-2290";
            reg = <0x05e94400 0x100>,
                  <0x05e94500 0x300>,
                  <0x05e94800 0x188>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>, <&rpmcc RPM_SMD_XO_CLK_SRC>;
            clock-names = "iface", "ref";
            vcca-supply = <&vreg_dsi_phy>;
        };
    };
...
