# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/mdss-common.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display MDSS common properties

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><PERSON>@quicinc.com>
  - <PERSON> <dmitry.barysh<PERSON>@linaro.org>
  - <PERSON> <<EMAIL>>

description:
  Device tree bindings for MSM Mobile Display Subsystem(MDSS) that encapsulates
  sub-blocks like DPU display controller, DSI and DP interfaces etc.

# Do not select this by default, otherwise it is also selected for qcom,mdss
# devices.
select:
  false

properties:
  $nodename:
    pattern: "^display-subsystem@[0-9a-f]+$"

  reg:
    maxItems: 1

  reg-names:
    const: mdss

  power-domains:
    maxItems: 1

  clocks:
    minItems: 2
    maxItems: 4

  clock-names:
    minItems: 2
    maxItems: 4

  interrupts:
    maxItems: 1

  interrupt-controller: true

  "#address-cells": true

  "#size-cells": true

  "#interrupt-cells":
    const: 1

  iommus:
    minItems: 1
    items:
      - description: Phandle to apps_smmu node with SID mask for Hard-Fail port0
      - description: Phandle to apps_smmu node with SID mask for Hard-Fail port1

  ranges: true

  interconnects:
    minItems: 1
    items:
      - description: Interconnect path from mdp0 (or a single mdp) port to the data bus
      - description: Interconnect path from mdp1 port to the data bus

  interconnect-names:
    minItems: 1
    items:
      - const: mdp0-mem
      - const: mdp1-mem

  resets:
    items:
      - description: MDSS_CORE reset

  memory-region:
    maxItems: 1
    description:
      Phandle to a node describing a reserved framebuffer memory region.
      For example, the splash memory region set up by the bootloader.

required:
  - reg
  - reg-names
  - power-domains
  - clocks
  - interrupts
  - interrupt-controller
  - iommus
  - ranges

additionalProperties: true
