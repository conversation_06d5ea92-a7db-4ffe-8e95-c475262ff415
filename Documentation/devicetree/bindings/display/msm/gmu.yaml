# SPDX-License-Identifier: GPL-2.0-only
# Copyright 2019-2020, The Linux Foundation, All Rights Reserved
%YAML 1.2
---

$id: http://devicetree.org/schemas/display/msm/gmu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: GMU attached to certain Adreno GPUs

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  These bindings describe the Graphics Management Unit (GMU) that is attached
  to members of the Adreno A6xx GPU family. The GMU provides on-device power
  management and support to improve power efficiency and reduce the load on
  the CPU.

properties:
  compatible:
    oneOf:
      - items:
          - pattern: '^qcom,adreno-gmu-6[0-9][0-9]\.[0-9]$'
          - const: qcom,adreno-gmu
      - const: qcom,adreno-gmu-wrapper

  reg:
    minItems: 1
    maxItems: 4

  reg-names:
    minItems: 1
    maxItems: 4

  clocks:
    minItems: 4
    maxItems: 7

  clock-names:
    minItems: 4
    maxItems: 7

  interrupts:
    items:
      - description: GMU HFI interrupt
      - description: GMU interrupt

  interrupt-names:
    items:
      - const: hfi
      - const: gmu

  power-domains:
    items:
      - description: CX power domain
      - description: GX power domain

  power-domain-names:
    items:
      - const: cx
      - const: gx

  iommus:
    maxItems: 1

  operating-points-v2: true

  opp-table:
    type: object

required:
  - compatible
  - reg
  - reg-names
  - power-domains
  - power-domain-names

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,adreno-gmu-618.0
              - qcom,adreno-gmu-630.2
    then:
      properties:
        reg:
          items:
            - description: Core GMU registers
            - description: GMU PDC registers
            - description: GMU PDC sequence registers
        reg-names:
          items:
            - const: gmu
            - const: gmu_pdc
            - const: gmu_pdc_seq
        clocks:
          items:
            - description: GMU clock
            - description: GPU CX clock
            - description: GPU AXI clock
            - description: GPU MEMNOC clock
        clock-names:
          items:
            - const: gmu
            - const: cxo
            - const: axi
            - const: memnoc

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,adreno-gmu-635.0
              - qcom,adreno-gmu-660.1
    then:
      properties:
        reg:
          items:
            - description: Core GMU registers
            - description: Resource controller registers
            - description: GMU PDC registers
        reg-names:
          items:
            - const: gmu
            - const: rscc
            - const: gmu_pdc
        clocks:
          items:
            - description: GMU clock
            - description: GPU CX clock
            - description: GPU AXI clock
            - description: GPU MEMNOC clock
            - description: GPU AHB clock
            - description: GPU HUB CX clock
            - description: GPU SMMU vote clock
        clock-names:
          items:
            - const: gmu
            - const: cxo
            - const: axi
            - const: memnoc
            - const: ahb
            - const: hub
            - const: smmu_vote

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,adreno-gmu-640.1
    then:
      properties:
        reg:
          items:
            - description: Core GMU registers
            - description: GMU PDC registers
            - description: GMU PDC sequence registers
        reg-names:
          items:
            - const: gmu
            - const: gmu_pdc
            - const: gmu_pdc_seq

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,adreno-gmu-650.2
    then:
      properties:
        reg:
          items:
            - description: Core GMU registers
            - description: Resource controller registers
            - description: GMU PDC registers
            - description: GMU PDC sequence registers
        reg-names:
          items:
            - const: gmu
            - const: rscc
            - const: gmu_pdc
            - const: gmu_pdc_seq

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,adreno-gmu-640.1
              - qcom,adreno-gmu-650.2
    then:
      properties:
        clocks:
          items:
            - description: GPU AHB clock
            - description: GMU clock
            - description: GPU CX clock
            - description: GPU AXI clock
            - description: GPU MEMNOC clock
        clock-names:
          items:
            - const: ahb
            - const: gmu
            - const: cxo
            - const: axi
            - const: memnoc

  - if:
      properties:
        compatible:
          contains:
            const: qcom,adreno-gmu-wrapper
    then:
      properties:
        reg:
          items:
            - description: GMU wrapper register space
        reg-names:
          items:
            - const: gmu
    else:
      required:
        - clocks
        - clock-names
        - interrupts
        - interrupt-names
        - iommus
        - operating-points-v2

examples:
  - |
    #include <dt-bindings/clock/qcom,gpucc-sdm845.h>
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    gmu: gmu@506a000 {
        compatible = "qcom,adreno-gmu-630.2", "qcom,adreno-gmu";

        reg = <0x506a000 0x30000>,
              <0xb280000 0x10000>,
              <0xb480000 0x10000>;
        reg-names = "gmu", "gmu_pdc", "gmu_pdc_seq";

        clocks = <&gpucc GPU_CC_CX_GMU_CLK>,
                 <&gpucc GPU_CC_CXO_CLK>,
                 <&gcc GCC_DDRSS_GPU_AXI_CLK>,
                 <&gcc GCC_GPU_MEMNOC_GFX_CLK>;
        clock-names = "gmu", "cxo", "axi", "memnoc";

        interrupts = <GIC_SPI 304 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 305 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-names = "hfi", "gmu";

        power-domains = <&gpucc GPU_CX_GDSC>,
                        <&gpucc GPU_GX_GDSC>;
        power-domain-names = "cx", "gx";

        iommus = <&adreno_smmu 5>;
        operating-points-v2 = <&gmu_opp_table>;
    };

    gmu_wrapper: gmu@596a000 {
        compatible = "qcom,adreno-gmu-wrapper";
        reg = <0x0596a000 0x30000>;
        reg-names = "gmu";
        power-domains = <&gpucc GPU_CX_GDSC>,
                        <&gpucc GPU_GX_GDSC>;
        power-domain-names = "cx", "gx";
    };
