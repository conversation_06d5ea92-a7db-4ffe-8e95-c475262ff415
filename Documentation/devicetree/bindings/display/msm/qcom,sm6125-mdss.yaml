# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sm6125-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM6125 Display MDSS

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  SM6125 MSM Mobile Display Subsystem (MDSS), which encapsulates sub-blocks
  like DPU display controller, DSI and DP interfaces etc.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,sm6125-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display AHB clock
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: ahb
      - const: core

  iommus:
    maxItems: 1

  interconnects:
    maxItems: 2

  interconnect-names:
    maxItems: 2

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sm6125-dpu

  "^dsi@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        items:
          - const: qcom,sm6125-dsi-ctrl
          - const: qcom,mdss-dsi-ctrl

  "^phy@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sm6125-dsi-phy-14nm

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sm6125.h>
    #include <dt-bindings/clock/qcom,gcc-sm6125.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-subsystem@5e00000 {
        compatible = "qcom,sm6125-mdss";
        reg = <0x05e00000 0x1000>;
        reg-names = "mdss";

        interrupts = <GIC_SPI 186 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        clocks = <&gcc GCC_DISP_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface",
                      "ahb",
                      "core";

        power-domains = <&dispcc MDSS_GDSC>;

        iommus = <&apps_smmu 0x400 0x0>;

        #address-cells = <1>;
        #size-cells = <1>;
        ranges;

        display-controller@5e01000 {
            compatible = "qcom,sm6125-dpu";
            reg = <0x05e01000 0x83208>,
                  <0x05eb0000 0x2008>;
            reg-names = "mdp", "vbif";

            interrupt-parent = <&mdss>;
            interrupts = <0>;

            clocks = <&gcc GCC_DISP_HF_AXI_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_ROT_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_LUT_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_CLK>,
                     <&dispcc DISP_CC_MDSS_VSYNC_CLK>,
                     <&gcc GCC_DISP_THROTTLE_CORE_CLK>;
            clock-names = "bus",
                          "iface",
                          "rot",
                          "lut",
                          "core",
                          "vsync",
                          "throttle";
            assigned-clocks = <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            assigned-clock-rates = <19200000>;

            operating-points-v2 = <&mdp_opp_table>;
            power-domains = <&rpmpd SM6125_VDDCX>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dpu_intf1_out: endpoint {
                        remote-endpoint = <&mdss_dsi0_in>;
                    };
                };
            };
        };

        dsi@5e94000 {
            compatible = "qcom,sm6125-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x05e94000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <4>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_HF_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";
            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>,
                      <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
            assigned-clock-parents = <&mdss_dsi0_phy 0>, <&mdss_dsi0_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmpd SM6125_VDDCX>;

            phys = <&mdss_dsi0_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    mdss_dsi0_in: endpoint {
                        remote-endpoint = <&dpu_intf1_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    mdss_dsi0_out: endpoint {
                    };
                };
            };
        };

        phy@5e94400 {
            compatible = "qcom,sm6125-dsi-phy-14nm";
            reg = <0x05e94400 0x100>,
                  <0x05e94500 0x300>,
                  <0x05e94800 0x188>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&rpmcc RPM_SMD_XO_CLK_SRC>;
            clock-names = "iface",
                          "ref";

            required-opps = <&rpmpd_opp_nom>;
            power-domains = <&rpmpd SM6125_VDDMX>;
        };
    };
...
