# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/dsi-controller-main.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display DSI controller

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><PERSON>@quicinc.com>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,apq8064-dsi-ctrl
              - qcom,msm8226-dsi-ctrl
              - qcom,msm8916-dsi-ctrl
              - qcom,msm8953-dsi-ctrl
              - qcom,msm8974-dsi-ctrl
              - qcom,msm8996-dsi-ctrl
              - qcom,msm8998-dsi-ctrl
              - qcom,qcm2290-dsi-ctrl
              - qcom,sc7180-dsi-ctrl
              - qcom,sc7280-dsi-ctrl
              - qcom,sdm660-dsi-ctrl
              - qcom,sdm845-dsi-ctrl
              - qcom,sm6115-dsi-ctrl
              - qcom,sm6125-dsi-ctrl
              - qcom,sm6350-dsi-ctrl
              - qcom,sm6375-dsi-ctrl
              - qcom,sm8150-dsi-ctrl
              - qcom,sm8250-dsi-ctrl
              - qcom,sm8350-dsi-ctrl
              - qcom,sm8450-dsi-ctrl
              - qcom,sm8550-dsi-ctrl
          - const: qcom,mdss-dsi-ctrl
      - enum:
          - qcom,dsi-ctrl-6g-qcm2290
          - qcom,mdss-dsi-ctrl # This should always come with an SoC-specific compatible
        deprecated: true

  reg:
    maxItems: 1

  reg-names:
    const: dsi_ctrl

  interrupts:
    maxItems: 1

  clocks:
    description: |
      Several clocks are used, depending on the variant. Typical ones are::
       - bus:: Display AHB clock.
       - byte:: Display byte clock.
       - byte_intf:: Display byte interface clock.
       - core:: Display core clock.
       - core_mss:: Core MultiMedia SubSystem clock.
       - iface:: Display AXI clock.
       - mdp_core:: MDP Core clock.
       - mnoc:: MNOC clock
       - pixel:: Display pixel clock.
    minItems: 3
    maxItems: 9

  clock-names:
    minItems: 3
    maxItems: 9

  phys:
    maxItems: 1

  phy-names:
    deprecated: true
    const: dsi

  syscon-sfpb:
    description: A phandle to mmss_sfpb syscon node (only for DSIv2).
    $ref: /schemas/types.yaml#/definitions/phandle

  qcom,dual-dsi-mode:
    type: boolean
    description: |
      Indicates if the DSI controller is driving a panel which needs
      2 DSI links.

  qcom,master-dsi:
    type: boolean
    description: |
      Indicates if the DSI controller is the master DSI controller when
      qcom,dual-dsi-mode enabled.

  qcom,sync-dual-dsi:
    type: boolean
    description: |
      Indicates if the DSI controller needs to sync the other DSI controller
      with MIPI DCS commands when qcom,dual-dsi-mode enabled.

  assigned-clocks:
    minItems: 2
    maxItems: 4
    description: |
      Parents of "byte" and "pixel" for the given platform.
      For DSIv2 platforms this should contain "byte", "esc", "src" and
      "pixel_src" clocks.

  assigned-clock-parents:
    minItems: 2
    maxItems: 4
    description: |
      The Byte clock and Pixel clock PLL outputs provided by a DSI PHY block.

  power-domains:
    maxItems: 1

  operating-points-v2: true

  opp-table:
    type: object

  ports:
    $ref: /schemas/graph.yaml#/properties/ports
    description: |
      Contains DSI controller input and output ports as children, each
      containing one endpoint subnode.

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: |
          Input endpoints of the controller.
        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            properties:
              data-lanes:
                maxItems: 4
                minItems: 1
                items:
                  enum: [ 0, 1, 2, 3 ]

      port@1:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: |
          Output endpoints of the controller.
        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            properties:
              data-lanes:
                maxItems: 4
                minItems: 1
                items:
                  enum: [ 0, 1, 2, 3 ]

    required:
      - port@0
      - port@1

  avdd-supply:
    description:
      Phandle to vdd regulator device node

  refgen-supply:
    description:
      Phandle to REFGEN regulator device node

  vcca-supply:
    description:
      Phandle to vdd regulator device node

  vdd-supply:
    description:
      VDD regulator

  vddio-supply:
    description:
      VDD-IO regulator

  vdda-supply:
    description:
      VDDA regulator

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - clocks
  - clock-names
  - phys
  - assigned-clocks
  - assigned-clock-parents
  - ports

allOf:
  - $ref: ../dsi-controller.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,apq8064-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 7
        clock-names:
          items:
            - const: iface
            - const: bus
            - const: core_mmss
            - const: src
            - const: byte
            - const: pixel
            - const: core

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8916-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 6
        clock-names:
          items:
            - const: mdp_core
            - const: iface
            - const: bus
            - const: byte
            - const: pixel
            - const: core

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8953-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 6
        clock-names:
          items:
            - const: mdp_core
            - const: iface
            - const: bus
            - const: byte
            - const: pixel
            - const: core

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8226-dsi-ctrl
              - qcom,msm8974-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 7
        clock-names:
          items:
            - const: mdp_core
            - const: iface
            - const: bus
            - const: byte
            - const: pixel
            - const: core
            - const: core_mmss

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8996-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 7
        clock-names:
          items:
            - const: mdp_core
            - const: byte
            - const: iface
            - const: bus
            - const: core_mmss
            - const: pixel
            - const: core

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,msm8998-dsi-ctrl
              - qcom,sm6125-dsi-ctrl
              - qcom,sm6350-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 6
        clock-names:
          items:
            - const: byte
            - const: byte_intf
            - const: pixel
            - const: core
            - const: iface
            - const: bus

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc7180-dsi-ctrl
              - qcom,sc7280-dsi-ctrl
              - qcom,sm8150-dsi-ctrl
              - qcom,sm8250-dsi-ctrl
              - qcom,sm8350-dsi-ctrl
              - qcom,sm8450-dsi-ctrl
              - qcom,sm8550-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 6
        clock-names:
          items:
            - const: byte
            - const: byte_intf
            - const: pixel
            - const: core
            - const: iface
            - const: bus

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdm660-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 9
        clock-names:
          items:
            - const: mdp_core
            - const: byte
            - const: byte_intf
            - const: mnoc
            - const: iface
            - const: bus
            - const: core_mmss
            - const: pixel
            - const: core

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sdm845-dsi-ctrl
              - qcom,sm6115-dsi-ctrl
              - qcom,sm6375-dsi-ctrl
    then:
      properties:
        clocks:
          maxItems: 6
        clock-names:
          items:
            - const: byte
            - const: byte_intf
            - const: pixel
            - const: core
            - const: iface
            - const: bus

unevaluatedProperties: false

examples:
  - |
     #include <dt-bindings/interrupt-controller/arm-gic.h>
     #include <dt-bindings/clock/qcom,dispcc-sdm845.h>
     #include <dt-bindings/clock/qcom,gcc-sdm845.h>
     #include <dt-bindings/power/qcom-rpmpd.h>

     dsi@ae94000 {
           compatible = "qcom,sc7180-dsi-ctrl", "qcom,mdss-dsi-ctrl";
           reg = <0x0ae94000 0x400>;
           reg-names = "dsi_ctrl";

           #address-cells = <1>;
           #size-cells = <0>;

           interrupt-parent = <&mdss>;
           interrupts = <4>;

           clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                    <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                    <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                    <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                    <&dispcc DISP_CC_MDSS_AHB_CLK>,
                    <&dispcc DISP_CC_MDSS_AXI_CLK>;
           clock-names = "byte",
                         "byte_intf",
                         "pixel",
                         "core",
                         "iface",
                         "bus";

           phys = <&dsi0_phy>;
           phy-names = "dsi";

           assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>, <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
           assigned-clock-parents = <&dsi_phy 0>, <&dsi_phy 1>;

           power-domains = <&rpmhpd SC7180_CX>;
           operating-points-v2 = <&dsi_opp_table>;

           ports {
                  #address-cells = <1>;
                  #size-cells = <0>;

                  port@0 {
                          reg = <0>;
                          dsi0_in: endpoint {
                                   remote-endpoint = <&dpu_intf1_out>;
                          };
                  };

                  port@1 {
                          reg = <1>;
                          dsi0_out: endpoint {
                                   remote-endpoint = <&sn65dsi86_in>;
                                   data-lanes = <0 1 2 3>;
                          };
                  };
           };
     };
...
