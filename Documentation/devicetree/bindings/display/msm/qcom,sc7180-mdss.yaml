# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sc7180-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SC7180 Display MDSS

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><EMAIL>>

description:
  Device tree bindings for MSM Mobile Display Subsystem(MDSS) that encapsulates
  sub-blocks like DPU display controller, DSI and DP interfaces etc. Device tree
  bindings of MDSS are mentioned for SC7180 target.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,sc7180-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display AHB clock from dispcc
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: ahb
      - const: core

  iommus:
    maxItems: 1

  interconnects:
    maxItems: 1

  interconnect-names:
    maxItems: 1

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sc7180-dpu

  "^displayport-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sc7180-dp

  "^dsi@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        items:
          - const: qcom,sc7180-dsi-ctrl
          - const: qcom,mdss-dsi-ctrl

  "^phy@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,dsi-phy-10nm

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sc7180.h>
    #include <dt-bindings/clock/qcom,gcc-sc7180.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interconnect/qcom,sdm845.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-subsystem@ae00000 {
        #address-cells = <1>;
        #size-cells = <1>;
        compatible = "qcom,sc7180-mdss";
        reg = <0xae00000 0x1000>;
        reg-names = "mdss";
        power-domains = <&dispcc MDSS_GDSC>;
        clocks = <&gcc GCC_DISP_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface", "ahb", "core";

        interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        interconnects = <&mmss_noc MASTER_MDP0 &mc_virt SLAVE_EBI1>;
        interconnect-names = "mdp0-mem";

        iommus = <&apps_smmu 0x800 0x2>;
        ranges;

        display-controller@ae01000 {
            compatible = "qcom,sc7180-dpu";
            reg = <0x0ae01000 0x8f000>,
                  <0x0aeb0000 0x2008>;

            reg-names = "mdp", "vbif";

            clocks = <&gcc GCC_DISP_HF_AXI_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_ROT_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_LUT_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_CLK>,
                     <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            clock-names = "bus", "iface", "rot", "lut", "core",
                          "vsync";

            interrupt-parent = <&mdss>;
            interrupts = <0>;
            power-domains = <&rpmhpd SC7180_CX>;
            operating-points-v2 = <&mdp_opp_table>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dpu_intf1_out: endpoint {
                        remote-endpoint = <&dsi0_in>;
                    };
                };

                port@2 {
                    reg = <2>;
                    dpu_intf0_out: endpoint {
                        remote-endpoint = <&dp_in>;
                    };
                };
            };
        };

        dsi@ae94000 {
            compatible = "qcom,sc7180-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x0ae94000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <4>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_HF_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";

            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>, <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
            assigned-clock-parents = <&dsi_phy 0>, <&dsi_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmhpd SC7180_CX>;

            phys = <&dsi_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi0_in: endpoint {
                        remote-endpoint = <&dpu_intf1_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi0_out: endpoint {
                    };
                };
            };

            dsi_opp_table: opp-table {
                compatible = "operating-points-v2";

                opp-187500000 {
                    opp-hz = /bits/ 64 <187500000>;
                    required-opps = <&rpmhpd_opp_low_svs>;
                };

                opp-300000000 {
                    opp-hz = /bits/ 64 <300000000>;
                    required-opps = <&rpmhpd_opp_svs>;
                };

                opp-358000000 {
                    opp-hz = /bits/ 64 <358000000>;
                    required-opps = <&rpmhpd_opp_svs_l1>;
                };
            };
        };

        dsi_phy: phy@ae94400 {
            compatible = "qcom,dsi-phy-10nm";
            reg = <0x0ae94400 0x200>,
                  <0x0ae94600 0x280>,
                  <0x0ae94a00 0x1e0>;
            reg-names = "dsi_phy",
                    "dsi_phy_lane",
                    "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                 <&rpmhcc RPMH_CXO_CLK>;
            clock-names = "iface", "ref";
            vdds-supply = <&vreg_dsi_phy>;
        };

        displayport-controller@ae90000 {
            compatible = "qcom,sc7180-dp";

            reg = <0xae90000 0x200>,
                  <0xae90200 0x200>,
                  <0xae90400 0xc00>,
                  <0xae91000 0x400>,
                  <0xae91400 0x400>;

            interrupt-parent = <&mdss>;
            interrupts = <12>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_DP_AUX_CLK>,
                     <&dispcc DISP_CC_MDSS_DP_LINK_CLK>,
                     <&dispcc DISP_CC_MDSS_DP_LINK_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_DP_PIXEL_CLK>;
            clock-names = "core_iface", "core_aux", "ctrl_link",
                          "ctrl_link_iface", "stream_pixel";
            assigned-clocks = <&dispcc DISP_CC_MDSS_DP_LINK_CLK_SRC>,
                              <&dispcc DISP_CC_MDSS_DP_PIXEL_CLK_SRC>;
            assigned-clock-parents = <&dp_phy 0>, <&dp_phy 1>;
            phys = <&dp_phy>;
            phy-names = "dp";

            operating-points-v2 = <&dp_opp_table>;
            power-domains = <&rpmhpd SC7180_CX>;

            #sound-dai-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;
                port@0 {
                    reg = <0>;
                    dp_in: endpoint {
                        remote-endpoint = <&dpu_intf0_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dp_out: endpoint { };
                };
            };

            dp_opp_table: opp-table {
                compatible = "operating-points-v2";

                opp-160000000 {
                    opp-hz = /bits/ 64 <160000000>;
                    required-opps = <&rpmhpd_opp_low_svs>;
                };

                opp-270000000 {
                    opp-hz = /bits/ 64 <270000000>;
                    required-opps = <&rpmhpd_opp_svs>;
                };

                opp-540000000 {
                    opp-hz = /bits/ 64 <540000000>;
                    required-opps = <&rpmhpd_opp_svs_l1>;
                };

                opp-810000000 {
                    opp-hz = /bits/ 64 <810000000>;
                    required-opps = <&rpmhpd_opp_nom>;
                };
            };
        };
    };
...
