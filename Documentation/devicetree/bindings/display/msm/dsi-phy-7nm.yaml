# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/dsi-phy-7nm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display DSI 7nm PHY

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: dsi-phy-common.yaml#

properties:
  compatible:
    enum:
      - qcom,dsi-phy-7nm
      - qcom,dsi-phy-7nm-8150
      - qcom,sc7280-dsi-phy-7nm
      - qcom,sm6375-dsi-phy-7nm
      - qcom,sm8350-dsi-phy-5nm
      - qcom,sm8450-dsi-phy-5nm
      - qcom,sm8550-dsi-phy-4nm

  reg:
    items:
      - description: dsi phy register set
      - description: dsi phy lane register set
      - description: dsi pll register set

  reg-names:
    items:
      - const: dsi_phy
      - const: dsi_phy_lane
      - const: dsi_pll

  vdds-supply:
    description: |
      Connected to VDD_A_DSI_PLL_0P9 pin (or VDDA_DSI{0,1}_PLL_0P9 for sm8150)

  phy-type:
    description: D-PHY (default) or C-PHY mode
    enum: [ 10, 11 ]
    default: 10

required:
  - compatible
  - reg
  - reg-names

unevaluatedProperties: false

examples:
  - |
     #include <dt-bindings/clock/qcom,dispcc-sm8250.h>
     #include <dt-bindings/clock/qcom,rpmh.h>

     dsi-phy@ae94400 {
         compatible = "qcom,dsi-phy-7nm";
         reg = <0x0ae94400 0x200>,
               <0x0ae94600 0x280>,
               <0x0ae94900 0x260>;
         reg-names = "dsi_phy",
                     "dsi_phy_lane",
                     "dsi_pll";

         #clock-cells = <1>;
         #phy-cells = <0>;

         vdds-supply = <&vreg_l5a_0p88>;
         clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                  <&rpmhcc RPMH_CXO_CLK>;
         clock-names = "iface", "ref";
     };
