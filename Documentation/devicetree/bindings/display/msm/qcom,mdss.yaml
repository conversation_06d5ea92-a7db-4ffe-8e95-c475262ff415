# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Mobile Display SubSystem (MDSS)

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  This is the bindings documentation for the Mobile Display Subsystem(MDSS) that
  encapsulates sub-blocks like MDP5, DSI, HDMI, eDP, etc.

properties:
  $nodename:
    pattern: "^display-subsystem@[0-9a-f]+$"

  compatible:
    enum:
      - qcom,mdss

  reg:
    minItems: 2
    maxItems: 3

  reg-names:
    minItems: 2
    items:
      - const: mdss_phys
      - const: vbif_phys
      - const: vbif_nrt_phys

  interrupts:
    maxItems: 1

  interrupt-controller: true

  "#interrupt-cells":
    const: 1

  power-domains:
    maxItems: 1
    description: |
      The MDSS power domain provided by GCC

  clocks:
    oneOf:
      - minItems: 3
        items:
          - description: Display abh clock
          - description: Display axi clock
          - description: Display vsync clock
          - description: Display core clock
      - minItems: 1
        items:
          - description: Display abh clock
          - description: Display core clock

  clock-names:
    oneOf:
      - minItems: 3
        items:
          - const: iface
          - const: bus
          - const: vsync
          - const: core
      - minItems: 1
        items:
          - const: iface
          - const: core

  "#address-cells":
    const: 1

  "#size-cells":
    const: 1

  ranges: true

  resets:
    items:
      - description: MDSS_CORE reset

required:
  - compatible
  - reg
  - reg-names
  - interrupts
  - interrupt-controller
  - "#interrupt-cells"
  - power-domains
  - clocks
  - clock-names
  - "#address-cells"
  - "#size-cells"
  - ranges

patternProperties:
  "^display-controller@[1-9a-f][0-9a-f]*$":
    type: object
    additionalProperties: true
    properties:
      compatible:
        contains:
          const: qcom,mdp5

  "^dsi@[1-9a-f][0-9a-f]*$":
    type: object
    additionalProperties: true
    properties:
      compatible:
        contains:
          const: qcom,mdss-dsi-ctrl

  "^phy@[1-9a-f][0-9a-f]*$":
    type: object
    additionalProperties: true
    properties:
      compatible:
        enum:
          - qcom,dsi-phy-14nm
          - qcom,dsi-phy-14nm-660
          - qcom,dsi-phy-14nm-8953
          - qcom,dsi-phy-20nm
          - qcom,dsi-phy-28nm-8226
          - qcom,dsi-phy-28nm-hpm
          - qcom,dsi-phy-28nm-lp
          - qcom,hdmi-phy-8084
          - qcom,hdmi-phy-8660
          - qcom,hdmi-phy-8960
          - qcom,hdmi-phy-8974
          - qcom,hdmi-phy-8996

  "^hdmi-tx@[1-9a-f][0-9a-f]*$":
    type: object
    additionalProperties: true
    properties:
      compatible:
        enum:
          - qcom,hdmi-tx-8084
          - qcom,hdmi-tx-8660
          - qcom,hdmi-tx-8960
          - qcom,hdmi-tx-8974
          - qcom,hdmi-tx-8994
          - qcom,hdmi-tx-8996

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8916.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    display-subsystem@1a00000 {
        compatible = "qcom,mdss";
        reg = <0x1a00000 0x1000>,
              <0x1ac8000 0x3000>;
        reg-names = "mdss_phys", "vbif_phys";

        power-domains = <&gcc MDSS_GDSC>;

        clocks = <&gcc GCC_MDSS_AHB_CLK>,
                 <&gcc GCC_MDSS_AXI_CLK>,
                 <&gcc GCC_MDSS_VSYNC_CLK>;
        clock-names = "iface",
                      "bus",
                      "vsync";

        interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;

        interrupt-controller;
        #interrupt-cells = <1>;

        #address-cells = <1>;
        #size-cells = <1>;
        ranges;

        display-controller@1a01000 {
            compatible = "qcom,msm8916-mdp5", "qcom,mdp5";
            reg = <0x01a01000 0x89000>;
            reg-names = "mdp_phys";

            interrupt-parent = <&mdss>;
            interrupts = <0>;

            clocks = <&gcc GCC_MDSS_AHB_CLK>,
                     <&gcc GCC_MDSS_AXI_CLK>,
                     <&gcc GCC_MDSS_MDP_CLK>,
                     <&gcc GCC_MDSS_VSYNC_CLK>;
            clock-names = "iface",
                      "bus",
                      "core",
                      "vsync";

            iommus = <&apps_iommu 4>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    mdp5_intf1_out: endpoint {
                        remote-endpoint = <&dsi0_in>;
                    };
                };
            };
        };
    };
...
