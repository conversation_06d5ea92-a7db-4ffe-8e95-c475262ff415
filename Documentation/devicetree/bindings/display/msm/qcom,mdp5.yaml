# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,mdp5.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Adreno/Snapdragon Mobile Display controller (MDP5)

description:
  MDP5 display controller found in SoCs like MSM8974, APQ8084, MSM8916, MSM8994
  and MSM8996.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: qcom,mdp5
        deprecated: true
      - items:
          - enum:
              - qcom,apq8084-mdp5
              - qcom,msm8226-mdp5
              - qcom,msm8916-mdp5
              - qcom,msm8917-mdp5
              - qcom,msm8953-mdp5
              - qcom,msm8974-mdp5
              - qcom,msm8976-mdp5
              - qcom,msm8994-mdp5
              - qcom,msm8996-mdp5
              - qcom,sdm630-mdp5
              - qcom,sdm660-mdp5
          - const: qcom,mdp5

  $nodename:
    pattern: '^display-controller@[0-9a-f]+$'

  reg:
    maxItems: 1

  reg-names:
    items:
      - const: mdp_phys

  interrupts:
    maxItems: 1

  clocks:
    minItems: 4
    maxItems: 7

  clock-names:
    oneOf:
      - minItems: 4
        items:
          - const: iface
          - const: bus
          - const: core
          - const: vsync
          - const: lut
          - const: tbu
          - const: tbu_rt
        # MSM8996 has additional iommu clock
      - items:
          - const: iface
          - const: bus
          - const: core
          - const: iommu
          - const: vsync

  interconnects:
    minItems: 1
    items:
      - description: Interconnect path from mdp0 (or a single mdp) port to the data bus
      - description: Interconnect path from mdp1 port to the data bus
      - description: Interconnect path from rotator port to the data bus

  interconnect-names:
    minItems: 1
    items:
      - const: mdp0-mem
      - const: mdp1-mem
      - const: rotator-mem

  iommus:
    items:
      - description: apps SMMU with the Stream-ID mask for Hard-Fail port0

  power-domains:
    maxItems: 1

  operating-points-v2: true
  opp-table:
    type: object

  ports:
    $ref: /schemas/graph.yaml#/properties/ports
    description: >
      Contains the list of output ports from DPU device. These ports
      connect to interfaces that are external to the DPU hardware,
      such as DSI, DP etc. MDP5 devices support up to 4 ports:
      one or two DSI ports, HDMI and eDP.

    patternProperties:
      "^port@[0-3]+$":
        $ref: /schemas/graph.yaml#/properties/port

    # at least one port is required
    required:
      - port@0

required:
  - compatible
  - reg
  - reg-names
  - clocks
  - clock-names
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8916.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    display-controller@1a01000 {
        compatible = "qcom,mdp5";
        reg = <0x1a01000 0x90000>;
        reg-names = "mdp_phys";

        interrupt-parent = <&mdss>;
        interrupts = <0>;

        clocks = <&gcc GCC_MDSS_AHB_CLK>,
                 <&gcc GCC_MDSS_AXI_CLK>,
                 <&gcc GCC_MDSS_MDP_CLK>,
                 <&gcc GCC_MDSS_VSYNC_CLK>;
        clock-names = "iface",
                      "bus",
                      "core",
                      "vsync";

        ports {
            #address-cells = <1>;
            #size-cells = <0>;

            port@0 {
                reg = <0>;
                endpoint {
                    remote-endpoint = <&dsi0_in>;
                };
            };
        };
    };
...
