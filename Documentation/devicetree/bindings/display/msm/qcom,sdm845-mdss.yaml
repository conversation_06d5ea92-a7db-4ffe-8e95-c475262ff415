# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sdm845-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SDM845 Display MDSS

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><EMAIL>>

description:
  Device tree bindings for MSM Mobile Display Subsystem(MDSS) that encapsulates
  sub-blocks like DPU display controller, DSI and DP interfaces etc. Device tree
  bindings of MDSS are mentioned for SDM845 target.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,sdm845-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: core

  iommus:
    maxItems: 2

  interconnects:
    maxItems: 2

  interconnect-names:
    maxItems: 2

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sdm845-dpu

  "^displayport-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sdm845-dp

  "^dsi@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        items:
          - const: qcom,sdm845-dsi-ctrl
          - const: qcom,mdss-dsi-ctrl

  "^phy@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,dsi-phy-10nm

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sdm845.h>
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-subsystem@ae00000 {
        #address-cells = <1>;
        #size-cells = <1>;
        compatible = "qcom,sdm845-mdss";
        reg = <0x0ae00000 0x1000>;
        reg-names = "mdss";
        power-domains = <&dispcc MDSS_GDSC>;

        clocks = <&gcc GCC_DISP_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface", "core";

        interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        iommus = <&apps_smmu 0x880 0x8>,
                 <&apps_smmu 0xc80 0x8>;
        ranges;

        display-controller@ae01000 {
            compatible = "qcom,sdm845-dpu";
            reg = <0x0ae01000 0x8f000>,
                  <0x0aeb0000 0x2008>;
            reg-names = "mdp", "vbif";

            clocks = <&gcc GCC_DISP_AXI_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_AXI_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_CLK>,
                     <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            clock-names = "gcc-bus", "iface", "bus", "core", "vsync";

            interrupt-parent = <&mdss>;
            interrupts = <0>;
            power-domains = <&rpmhpd SDM845_CX>;
            operating-points-v2 = <&mdp_opp_table>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dpu_intf1_out: endpoint {
                        remote-endpoint = <&dsi0_in>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dpu_intf2_out: endpoint {
                        remote-endpoint = <&dsi1_in>;
                    };
                };
            };
        };

        dsi@ae94000 {
            compatible = "qcom,sdm845-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x0ae94000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <4>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";
            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>,
                              <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
            assigned-clock-parents = <&dsi0_phy 0>, <&dsi0_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmhpd SDM845_CX>;

            phys = <&dsi0_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi0_in: endpoint {
                        remote-endpoint = <&dpu_intf1_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi0_out: endpoint {
                    };
                };
            };
        };

        dsi0_phy: phy@ae94400 {
            compatible = "qcom,dsi-phy-10nm";
            reg = <0x0ae94400 0x200>,
                  <0x0ae94600 0x280>,
                  <0x0ae94a00 0x1e0>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&rpmhcc RPMH_CXO_CLK>;
            clock-names = "iface", "ref";
            vdds-supply = <&vreg_dsi_phy>;
        };

        dsi@ae96000 {
            compatible = "qcom,sdm845-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x0ae96000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <5>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE1_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE1_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK1_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC1_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc DISP_CC_MDSS_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";
            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE1_CLK_SRC>,
                              <&dispcc DISP_CC_MDSS_PCLK1_CLK_SRC>;
            assigned-clock-parents = <&dsi1_phy 0>, <&dsi1_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmhpd SDM845_CX>;

            phys = <&dsi1_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi1_in: endpoint {
                        remote-endpoint = <&dpu_intf2_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi1_out: endpoint {
                    };
                };
            };
        };

        dsi1_phy: phy@ae96400 {
            compatible = "qcom,dsi-phy-10nm";
            reg = <0x0ae96400 0x200>,
                  <0x0ae96600 0x280>,
                  <0x0ae96a00 0x10e>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&rpmhcc RPMH_CXO_CLK>;
            clock-names = "iface", "ref";
            vdds-supply = <&vreg_dsi_phy>;
        };
    };
...
