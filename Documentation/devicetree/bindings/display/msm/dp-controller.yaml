# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/dp-controller.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MSM Display Port Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Device tree bindings for DisplayPort host controller for MSM targets
  that are compatible with VESA DisplayPort interface specification.

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,sc7180-dp
          - qcom,sc7280-dp
          - qcom,sc7280-edp
          - qcom,sc8180x-dp
          - qcom,sc8180x-edp
          - qcom,sc8280xp-dp
          - qcom,sc8280xp-edp
          - qcom,sdm845-dp
          - qcom,sm8350-dp
      - items:
          - enum:
              - qcom,sm8250-dp
              - qcom,sm8450-dp
              - qcom,sm8550-dp
          - const: qcom,sm8350-dp

  reg:
    minItems: 4
    items:
      - description: ahb register block
      - description: aux register block
      - description: link register block
      - description: p0 register block
      - description: p1 register block

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: AHB clock to enable register access
      - description: Display Port AUX clock
      - description: Display Port Link clock
      - description: Link interface clock between DP and PHY
      - description: Display Port Pixel clock

  clock-names:
    items:
      - const: core_iface
      - const: core_aux
      - const: ctrl_link
      - const: ctrl_link_iface
      - const: stream_pixel

  assigned-clocks:
    items:
      - description: link clock source
      - description: pixel clock source

  assigned-clock-parents:
    items:
      - description: phy 0 parent
      - description: phy 1 parent

  phys:
    maxItems: 1

  phy-names:
    items:
      - const: dp

  operating-points-v2: true

  opp-table:
    type: object

  power-domains:
    maxItems: 1

  aux-bus:
    $ref: /schemas/display/dp-aux-bus.yaml#

  data-lanes:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    deprecated: true
    minItems: 1
    maxItems: 4
    items:
      maximum: 3

  "#sound-dai-cells":
    const: 0

  vdda-0p9-supply:
    deprecated: true
  vdda-1p2-supply:
    deprecated: true

  ports:
    $ref: /schemas/graph.yaml#/properties/ports
    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description: Input endpoint of the controller

      port@1:
        $ref: /schemas/graph.yaml#/$defs/port-base
        description: Output endpoint of the controller
        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            properties:
              data-lanes:
                minItems: 1
                maxItems: 4
                items:
                  enum: [ 0, 1, 2, 3 ]

              link-frequencies:
                minItems: 1
                maxItems: 4
                items:
                  enum: [ 1620000000, 2700000000, 5400000000, 8100000000 ]

    required:
      - port@0
      - port@1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - phys
  - phy-names
  - power-domains
  - ports

allOf:
  # AUX BUS does not exist on DP controllers
  # Audio output also is present only on DP output
  # p1 regions is present on DP, but not on eDP
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc7280-edp
              - qcom,sc8180x-edp
              - qcom,sc8280xp-edp
    then:
      properties:
        "#sound-dai-cells": false
    else:
      properties:
        aux-bus: false
        reg:
          minItems: 5
      required:
        - "#sound-dai-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/qcom,dispcc-sc7180.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    displayport-controller@ae90000 {
        compatible = "qcom,sc7180-dp";
        reg = <0xae90000 0x200>,
              <0xae90200 0x200>,
              <0xae90400 0xc00>,
              <0xae91000 0x400>,
              <0xae91400 0x400>;
        interrupt-parent = <&mdss>;
        interrupts = <12>;
        clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_DP_AUX_CLK>,
                 <&dispcc DISP_CC_MDSS_DP_LINK_CLK>,
                 <&dispcc DISP_CC_MDSS_DP_LINK_INTF_CLK>,
                 <&dispcc DISP_CC_MDSS_DP_PIXEL_CLK>;
        clock-names = "core_iface", "core_aux",
                      "ctrl_link",
                      "ctrl_link_iface", "stream_pixel";

        assigned-clocks = <&dispcc DISP_CC_MDSS_DP_LINK_CLK_SRC>,
                          <&dispcc DISP_CC_MDSS_DP_PIXEL_CLK_SRC>;

        assigned-clock-parents = <&dp_phy 0>, <&dp_phy 1>;

        phys = <&dp_phy>;
        phy-names = "dp";

        #sound-dai-cells = <0>;

        power-domains = <&rpmhpd SC7180_CX>;

        ports {
            #address-cells = <1>;
            #size-cells = <0>;

            port@0 {
                reg = <0>;
                endpoint {
                    remote-endpoint = <&dpu_intf0_out>;
                };
            };

            port@1 {
                reg = <1>;
                endpoint {
                    remote-endpoint = <&typec>;
                    data-lanes = <0 1>;
                    link-frequencies = /bits/ 64 <1620000000 2700000000 5400000000 8100000000>;
                };
            };
        };
    };
...
