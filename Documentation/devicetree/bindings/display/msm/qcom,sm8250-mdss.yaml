# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sm8250-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM8250 Display MDSS

maintainers:
  - <PERSON> <dmitry.barysh<PERSON>@linaro.org>

description:
  Device tree bindings for MSM Mobile Display Subsystem(MDSS) that encapsulates
  sub-blocks like DPU display controller, DSI and DP interfaces etc. Device tree
  bindings of MDSS are mentioned for SM8250 target.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,sm8250-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display hf axi clock
      - description: Display sf axi clock
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: bus
      - const: nrt_bus
      - const: core

  iommus:
    maxItems: 1

  interconnects:
    maxItems: 2

  interconnect-names:
    maxItems: 2

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sm8250-dpu

  "^dsi@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        items:
          - const: qcom,sm8250-dsi-ctrl
          - const: qcom,mdss-dsi-ctrl

  "^phy@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,dsi-phy-7nm

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sm8250.h>
    #include <dt-bindings/clock/qcom,gcc-sm8250.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interconnect/qcom,sm8250.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>

    display-subsystem@ae00000 {
        compatible = "qcom,sm8250-mdss";
        reg = <0x0ae00000 0x1000>;
        reg-names = "mdss";

        interconnects = <&mmss_noc MASTER_MDP_PORT0 &mc_virt SLAVE_EBI_CH0>,
                        <&mmss_noc MASTER_MDP_PORT1 &mc_virt SLAVE_EBI_CH0>;
        interconnect-names = "mdp0-mem", "mdp1-mem";

        power-domains = <&dispcc MDSS_GDSC>;

        clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                 <&gcc GCC_DISP_HF_AXI_CLK>,
                 <&gcc GCC_DISP_SF_AXI_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface", "bus", "nrt_bus", "core";

        interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        iommus = <&apps_smmu 0x820 0x402>;

        #address-cells = <1>;
        #size-cells = <1>;
        ranges;

        display-controller@ae01000 {
            compatible = "qcom,sm8250-dpu";
            reg = <0x0ae01000 0x8f000>,
                  <0x0aeb0000 0x2008>;
            reg-names = "mdp", "vbif";

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_HF_AXI_CLK>,
                     <&dispcc DISP_CC_MDSS_MDP_CLK>,
                     <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            clock-names = "iface", "bus", "core", "vsync";

            assigned-clocks = <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            assigned-clock-rates = <19200000>;

            operating-points-v2 = <&mdp_opp_table>;
            power-domains = <&rpmhpd RPMHPD_MMCX>;

            interrupt-parent = <&mdss>;
            interrupts = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dpu_intf1_out: endpoint {
                        remote-endpoint = <&dsi0_in>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dpu_intf2_out: endpoint {
                        remote-endpoint = <&dsi1_in>;
                    };
                };
            };

            mdp_opp_table: opp-table {
                compatible = "operating-points-v2";

                opp-200000000 {
                    opp-hz = /bits/ 64 <200000000>;
                    required-opps = <&rpmhpd_opp_low_svs>;
                };

                opp-300000000 {
                    opp-hz = /bits/ 64 <300000000>;
                    required-opps = <&rpmhpd_opp_svs>;
                };

                opp-345000000 {
                    opp-hz = /bits/ 64 <345000000>;
                    required-opps = <&rpmhpd_opp_svs_l1>;
                };

                opp-460000000 {
                    opp-hz = /bits/ 64 <460000000>;
                    required-opps = <&rpmhpd_opp_nom>;
                };
            };
        };

        dsi@ae94000 {
            compatible = "qcom,sm8250-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x0ae94000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <4>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_HF_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";

            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>,
                              <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
            assigned-clock-parents = <&dsi0_phy 0>, <&dsi0_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmhpd RPMHPD_MMCX>;

            phys = <&dsi0_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi0_in: endpoint {
                        remote-endpoint = <&dpu_intf1_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi0_out: endpoint {
                    };
                };
            };

            dsi_opp_table: opp-table {
                compatible = "operating-points-v2";

                opp-187500000 {
                    opp-hz = /bits/ 64 <187500000>;
                    required-opps = <&rpmhpd_opp_low_svs>;
                };

                opp-300000000 {
                    opp-hz = /bits/ 64 <300000000>;
                    required-opps = <&rpmhpd_opp_svs>;
                };

                opp-358000000 {
                    opp-hz = /bits/ 64 <358000000>;
                    required-opps = <&rpmhpd_opp_svs_l1>;
                };
            };
        };

        dsi0_phy: phy@ae94400 {
            compatible = "qcom,dsi-phy-7nm";
            reg = <0x0ae94400 0x200>,
                  <0x0ae94600 0x280>,
                  <0x0ae94900 0x260>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&rpmhcc RPMH_CXO_CLK>;
            clock-names = "iface", "ref";
            vdds-supply = <&vreg_dsi_phy>;
        };

        dsi@ae96000 {
            compatible = "qcom,sm8250-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x0ae96000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <5>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE1_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE1_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK1_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC1_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_HF_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";

            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE1_CLK_SRC>,
                              <&dispcc DISP_CC_MDSS_PCLK1_CLK_SRC>;
            assigned-clock-parents = <&dsi1_phy 0>, <&dsi1_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmhpd RPMHPD_MMCX>;

            phys = <&dsi1_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi1_in: endpoint {
                        remote-endpoint = <&dpu_intf2_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi1_out: endpoint {
                    };
                };
            };
        };

        dsi1_phy: phy@ae96400 {
            compatible = "qcom,dsi-phy-7nm";
            reg = <0x0ae96400 0x200>,
                  <0x0ae96600 0x280>,
                  <0x0ae96900 0x260>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&rpmhcc RPMH_CXO_CLK>;
            clock-names = "iface", "ref";
            vdds-supply = <&vreg_dsi_phy>;
        };
    };
...
