# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sdm845-dpu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display DPU on SDM845

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><EMAIL>>

$ref: /schemas/display/msm/dpu-common.yaml#

properties:
  compatible:
    const: qcom,sdm845-dpu

  reg:
    items:
      - description: Address offset and size for mdp register set
      - description: Address offset and size for vbif register set

  reg-names:
    items:
      - const: mdp
      - const: vbif

  clocks:
    items:
      - description: Display GCC bus clock
      - description: Display ahb clock
      - description: Display axi clock
      - description: Display core clock
      - description: Display vsync clock

  clock-names:
    items:
      - const: gcc-bus
      - const: iface
      - const: bus
      - const: core
      - const: vsync

required:
  - compatible
  - reg
  - reg-names
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sdm845.h>
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-controller@ae01000 {
        compatible = "qcom,sdm845-dpu";
        reg = <0x0ae01000 0x8f000>,
              <0x0aeb0000 0x2008>;
        reg-names = "mdp", "vbif";

        clocks = <&gcc GCC_DISP_AXI_CLK>,
                 <&dispcc DISP_CC_MDSS_AHB_CLK>,
                 <&dispcc DISP_CC_MDSS_AXI_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>,
                 <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
        clock-names = "gcc-bus", "iface", "bus", "core", "vsync";

        interrupt-parent = <&mdss>;
        interrupts = <0>;
        power-domains = <&rpmhpd SDM845_CX>;
        operating-points-v2 = <&mdp_opp_table>;

        ports {
            #address-cells = <1>;
            #size-cells = <0>;

            port@0 {
                reg = <0>;
                endpoint {
                    remote-endpoint = <&dsi0_in>;
                };
            };

            port@1 {
                reg = <1>;
                endpoint {
                    remote-endpoint = <&dsi1_in>;
                };
            };
        };
    };
...
