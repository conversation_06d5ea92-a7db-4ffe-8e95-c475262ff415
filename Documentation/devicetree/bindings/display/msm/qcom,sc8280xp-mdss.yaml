# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sc8280xp-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SC8280XP Mobile Display Subsystem

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description:
  Device tree bindings for MSM Mobile Display Subsystem (MDSS) that encapsulates
  sub-blocks like DPU display controller, DSI and DP interfaces etc.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,sc8280xp-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display AHB clock from dispcc
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: ahb
      - const: core

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sc8280xp-dpu

  "^displayport-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        enum:
          - qcom,sc8280xp-dp
          - qcom,sc8280xp-edp

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sc8280xp.h>
    #include <dt-bindings/clock/qcom,gcc-sc8280xp.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interconnect/qcom,sc8280xp.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-subsystem@ae00000 {
        compatible = "qcom,sc8280xp-mdss";
        reg = <0x0ae00000 0x1000>;
        reg-names = "mdss";

        power-domains = <&dispcc0 MDSS_GDSC>;

        clocks = <&gcc GCC_DISP_AHB_CLK>,
                 <&dispcc0 DISP_CC_MDSS_AHB_CLK>,
                 <&dispcc0 DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface",
                      "ahb",
                      "core";

        resets = <&dispcc0 DISP_CC_MDSS_CORE_BCR>;

        interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        interconnects = <&mmss_noc MASTER_MDP0 0 &mc_virt SLAVE_EBI1 0>,
                        <&mmss_noc MASTER_MDP1 0 &mc_virt SLAVE_EBI1 0>;
        interconnect-names = "mdp0-mem", "mdp1-mem";

        iommus = <&apps_smmu 0x1000 0x402>;

        #address-cells = <1>;
        #size-cells = <1>;
        ranges;

        display-controller@ae01000 {
            compatible = "qcom,sc8280xp-dpu";
            reg = <0x0ae01000 0x8f000>,
            <0x0aeb0000 0x2008>;
            reg-names = "mdp", "vbif";

            clocks = <&gcc GCC_DISP_HF_AXI_CLK>,
                     <&gcc GCC_DISP_SF_AXI_CLK>,
                     <&dispcc0 DISP_CC_MDSS_AHB_CLK>,
                     <&dispcc0 DISP_CC_MDSS_MDP_LUT_CLK>,
                     <&dispcc0 DISP_CC_MDSS_MDP_CLK>,
                     <&dispcc0 DISP_CC_MDSS_VSYNC_CLK>;
            clock-names = "bus",
                          "nrt_bus",
                          "iface",
                          "lut",
                          "core",
                          "vsync";

            assigned-clocks = <&dispcc0 DISP_CC_MDSS_VSYNC_CLK>;
            assigned-clock-rates = <19200000>;

            operating-points-v2 = <&mdss0_mdp_opp_table>;
            power-domains = <&rpmhpd SC8280XP_MMCX>;

            interrupt-parent = <&mdss0>;
            interrupts = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    endpoint {
                        remote-endpoint = <&mdss0_dp0_in>;
                    };
                };

                port@4 {
                    reg = <4>;
                    endpoint {
                        remote-endpoint = <&mdss0_dp1_in>;
                    };
                };

                port@5 {
                    reg = <5>;
                    endpoint {
                        remote-endpoint = <&mdss0_dp3_in>;
                    };
                };

                port@6 {
                    reg = <6>;
                    endpoint {
                        remote-endpoint = <&mdss0_dp2_in>;
                    };
                };
            };
        };
    };
...
