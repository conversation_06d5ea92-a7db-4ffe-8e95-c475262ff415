# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/dsi-phy-28nm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display DSI 28nm PHY

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><EMAIL>>

allOf:
  - $ref: dsi-phy-common.yaml#

properties:
  compatible:
    enum:
      - qcom,dsi-phy-28nm-8226
      - qcom,dsi-phy-28nm-8960
      - qcom,dsi-phy-28nm-hpm
      - qcom,dsi-phy-28nm-hpm-fam-b
      - qcom,dsi-phy-28nm-lp

  reg:
    items:
      - description: dsi pll register set
      - description: dsi phy register set
      - description: dsi phy regulator register set

  reg-names:
    items:
      - const: dsi_pll
      - const: dsi_phy
      - const: dsi_phy_regulator

  vddio-supply:
    description: Phandle to vdd-io regulator device node.

  qcom,dsi-phy-regulator-ldo-mode:
    type: boolean
    description: Indicates if the LDO mode PHY regulator is wanted.

required:
  - compatible
  - reg
  - reg-names
  - vddio-supply

unevaluatedProperties: false

examples:
  - |
     #include <dt-bindings/clock/qcom,dispcc-sdm845.h>
     #include <dt-bindings/clock/qcom,rpmh.h>

     dsi-phy@fd922a00 {
         compatible = "qcom,dsi-phy-28nm-lp";
         reg = <0xfd922a00 0xd4>,
               <0xfd922b00 0x2b0>,
               <0xfd922d80 0x7b>;
         reg-names = "dsi_pll",
                     "dsi_phy",
                     "dsi_phy_regulator";

         #clock-cells = <1>;
         #phy-cells = <0>;

         vddio-supply = <&vddio_reg>;

         clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>,
                  <&rpmhcc RPMH_CXO_CLK>;
         clock-names = "iface", "ref";
     };
...
