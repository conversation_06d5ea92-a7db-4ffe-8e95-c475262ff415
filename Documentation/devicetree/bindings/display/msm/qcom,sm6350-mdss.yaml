# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/msm/qcom,sm6350-mdss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM6350 Display MDSS

maintainers:
  - <PERSON> <quic_m<PERSON><PERSON><EMAIL>>

description:
  SM6350 MSM Mobile Display Subsystem (MDSS), which encapsulates sub-blocks
  like DPU display controller, DSI and DP interfaces etc.

$ref: /schemas/display/msm/mdss-common.yaml#

properties:
  compatible:
    const: qcom,sm6350-mdss

  clocks:
    items:
      - description: Display AHB clock from gcc
      - description: Display AXI clock from gcc
      - description: Display core clock

  clock-names:
    items:
      - const: iface
      - const: bus
      - const: core

  iommus:
    maxItems: 1

  interconnects:
    maxItems: 2

  interconnect-names:
    maxItems: 2

patternProperties:
  "^display-controller@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,sm6350-dpu

  "^dsi@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        items:
          - const: qcom,sm6350-dsi-ctrl
          - const: qcom,mdss-dsi-ctrl

  "^phy@[0-9a-f]+$":
    type: object
    properties:
      compatible:
        const: qcom,dsi-phy-10nm

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-sm6350.h>
    #include <dt-bindings/clock/qcom,gcc-sm6350.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    display-subsystem@ae00000 {
        compatible = "qcom,sm6350-mdss";
        reg = <0x0ae00000 0x1000>;
        reg-names = "mdss";

        power-domains = <&dispcc MDSS_GDSC>;

        clocks = <&gcc GCC_DISP_AHB_CLK>,
                 <&gcc GCC_DISP_AXI_CLK>,
                 <&dispcc DISP_CC_MDSS_MDP_CLK>;
        clock-names = "iface", "bus", "core";

        interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-controller;
        #interrupt-cells = <1>;

        iommus = <&apps_smmu 0x800 0x2>;
        #address-cells = <1>;
        #size-cells = <1>;
        ranges;

        display-controller@ae01000 {
            compatible = "qcom,sm6350-dpu";
            reg = <0x0ae01000 0x8f000>,
                  <0x0aeb0000 0x2008>;
            reg-names = "mdp", "vbif";

            clocks = <&gcc GCC_DISP_AXI_CLK>,
              <&dispcc DISP_CC_MDSS_AHB_CLK>,
              <&dispcc DISP_CC_MDSS_ROT_CLK>,
              <&dispcc DISP_CC_MDSS_MDP_LUT_CLK>,
              <&dispcc DISP_CC_MDSS_MDP_CLK>,
              <&dispcc DISP_CC_MDSS_VSYNC_CLK>;
            clock-names = "bus", "iface", "rot", "lut", "core",
                    "vsync";

            assigned-clocks = <&dispcc DISP_CC_MDSS_MDP_CLK>,
                              <&dispcc DISP_CC_MDSS_VSYNC_CLK>,
                              <&dispcc DISP_CC_MDSS_ROT_CLK>,
                              <&dispcc DISP_CC_MDSS_AHB_CLK>;
            assigned-clock-rates = <300000000>,
                                   <19200000>,
                                   <19200000>,
                                   <19200000>;

            interrupt-parent = <&mdss>;
            interrupts = <0>;
            operating-points-v2 = <&mdp_opp_table>;
            power-domains = <&rpmhpd SM6350_CX>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dpu_intf1_out: endpoint {
                        remote-endpoint = <&dsi0_in>;
                    };
                };
            };
        };

        dsi@ae94000 {
            compatible = "qcom,sm6350-dsi-ctrl", "qcom,mdss-dsi-ctrl";
            reg = <0x0ae94000 0x400>;
            reg-names = "dsi_ctrl";

            interrupt-parent = <&mdss>;
            interrupts = <4>;

            clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK>,
                     <&dispcc DISP_CC_MDSS_BYTE0_INTF_CLK>,
                     <&dispcc DISP_CC_MDSS_PCLK0_CLK>,
                     <&dispcc DISP_CC_MDSS_ESC0_CLK>,
                     <&dispcc DISP_CC_MDSS_AHB_CLK>,
                     <&gcc GCC_DISP_AXI_CLK>;
            clock-names = "byte",
                          "byte_intf",
                          "pixel",
                          "core",
                          "iface",
                          "bus";

            assigned-clocks = <&dispcc DISP_CC_MDSS_BYTE0_CLK_SRC>,
                              <&dispcc DISP_CC_MDSS_PCLK0_CLK_SRC>;
            assigned-clock-parents = <&dsi0_phy 0>, <&dsi0_phy 1>;

            operating-points-v2 = <&dsi_opp_table>;
            power-domains = <&rpmhpd SM6350_MX>;

            phys = <&dsi0_phy>;
            phy-names = "dsi";

            #address-cells = <1>;
            #size-cells = <0>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    dsi0_in: endpoint {
                        remote-endpoint = <&dpu_intf1_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    dsi0_out: endpoint {
                    };
                };
            };
        };

        dsi0_phy: phy@ae94400 {
            compatible = "qcom,dsi-phy-10nm";
            reg = <0x0ae94400 0x200>,
                  <0x0ae94600 0x280>,
                  <0x0ae94a00 0x1e0>;
            reg-names = "dsi_phy",
                        "dsi_phy_lane",
                        "dsi_pll";

            #clock-cells = <1>;
            #phy-cells = <0>;

            clocks = <&dispcc DISP_CC_MDSS_AHB_CLK>, <&rpmhcc RPMH_CXO_CLK>;
            clock-names = "iface", "ref";
        };
    };
...
