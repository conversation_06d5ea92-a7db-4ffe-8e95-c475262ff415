# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/bridge/ti,sn65dsi86.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SN65DSI86 DSI to eDP bridge chip

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The Texas Instruments SN65DSI86 bridge takes MIPI DSI in and outputs eDP.
  https://www.ti.com/general/docs/lit/getliterature.tsp?genericPartNumber=sn65dsi86&fileType=pdf

properties:
  compatible:
    const: ti,sn65dsi86

  reg:
    enum: [ 0x2c, 0x2d ]

  enable-gpios:
    maxItems: 1
    description: GPIO specifier for bridge_en pin (active high).

  suspend-gpios:
    maxItems: 1
    description: GPIO specifier for GPIO1 pin on bridge (active low).

  no-hpd:
    type: boolean
    description:
      Set if the HPD line on the bridge isn't hooked up to anything or is
      otherwise unusable.

  vccio-supply:
    description: A 1.8V supply that powers the digital IOs.

  vpll-supply:
    description: A 1.8V supply that powers the DisplayPort PLL.

  vcca-supply:
    description: A 1.2V supply that powers the analog circuits.

  vcc-supply:
    description: A 1.2V supply that powers the digital core.

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1
    description:
      Clock specifier for input reference clock. The reference clock rate must
      be 12 MHz, 19.2 MHz, 26 MHz, 27 MHz or 38.4 MHz.

  clock-names:
    const: refclk

  gpio-controller: true
  '#gpio-cells':
    const: 2
    description:
      First cell is pin number, second cell is flags.  GPIO pin numbers are
      1-based to match the datasheet.  See ../../gpio/gpio.txt for more
      information.

  '#pwm-cells':
    const: 1
    description: See ../../pwm/pwm.yaml for description of the cell formats.

  aux-bus:
    $ref: /schemas/display/dp-aux-bus.yaml#

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description:
          Video port for MIPI DSI input

      port@1:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description:
          Video port for eDP output (panel or connector).

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false

            properties:
              data-lanes:
                oneOf:
                  - minItems: 1
                    maxItems: 1
                    uniqueItems: true
                    items:
                      enum:
                        - 0
                        - 1
                    description:
                      If you have 1 logical lane the bridge supports routing
                      to either port 0 or port 1.  Port 0 is suggested.

                  - minItems: 2
                    maxItems: 2
                    uniqueItems: true
                    items:
                      enum:
                        - 0
                        - 1
                    description:
                      If you have 2 logical lanes the bridge supports
                      reordering but only on physical ports 0 and 1.

                  - minItems: 4
                    maxItems: 4
                    uniqueItems: true
                    items:
                      enum:
                        - 0
                        - 1
                        - 2
                        - 3
                    description:
                      If you have 4 logical lanes the bridge supports
                      reordering in any way.

              lane-polarities:
                minItems: 1
                maxItems: 4
                items:
                  enum:
                    - 0
                    - 1

            dependencies:
              lane-polarities: [data-lanes]

    required:
      - port@0
      - port@1

required:
  - compatible
  - reg
  - vccio-supply
  - vpll-supply
  - vcca-supply
  - vcc-supply
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      bridge@2d {
        compatible = "ti,sn65dsi86";
        reg = <0x2d>;

        interrupt-parent = <&tlmm>;
        interrupts = <10 IRQ_TYPE_LEVEL_HIGH>;

        enable-gpios = <&tlmm 102 GPIO_ACTIVE_HIGH>;

        vpll-supply = <&src_pp1800_s4a>;
        vccio-supply = <&src_pp1800_s4a>;
        vcca-supply = <&src_pp1200_l2a>;
        vcc-supply = <&src_pp1200_l2a>;

        clocks = <&rpmhcc RPMH_LN_BB_CLK2>;
        clock-names = "refclk";

        no-hpd;

        ports {
          #address-cells = <1>;
          #size-cells = <0>;

          port@0 {
            reg = <0>;
            endpoint {
              remote-endpoint = <&dsi0_out>;
            };
          };

          port@1 {
            reg = <1>;
            sn65dsi86_out: endpoint {
              remote-endpoint = <&panel_in_edp>;
            };
          };
        };

        aux-bus {
          panel {
            compatible = "boe,nv133fhm-n62";
            power-supply = <&pp3300_dx_edp>;
            backlight = <&backlight>;
            hpd-gpios = <&sn65dsi86_bridge 2 GPIO_ACTIVE_HIGH>;

            port {
              panel_in_edp: endpoint {
                remote-endpoint = <&sn65dsi86_out>;
              };
            };
          };
        };
      };
    };
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      bridge@2d {
        compatible = "ti,sn65dsi86";
        reg = <0x2d>;

        enable-gpios = <&msmgpio 33 GPIO_ACTIVE_HIGH>;
        suspend-gpios = <&msmgpio 34 GPIO_ACTIVE_LOW>;

        interrupts-extended = <&gpio3 4 IRQ_TYPE_EDGE_FALLING>;

        vccio-supply = <&pm8916_l17>;
        vcca-supply = <&pm8916_l6>;
        vpll-supply = <&pm8916_l17>;
        vcc-supply = <&pm8916_l6>;

        clock-names = "refclk";
        clocks = <&input_refclk>;

        ports {
          #address-cells = <1>;
          #size-cells = <0>;

          port@0 {
            reg = <0>;

            edp_bridge_in: endpoint {
              remote-endpoint = <&dsi_out>;
            };
          };

          port@1 {
            reg = <1>;

            edp_bridge_out: endpoint {
              data-lanes = <2 1 3 0>;
              lane-polarities = <0 1 0 1>;
              remote-endpoint = <&edp_panel_in>;
            };
          };
        };
      };
    };
