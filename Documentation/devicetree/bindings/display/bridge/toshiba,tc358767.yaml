# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/bridge/toshiba,tc358767.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Toshiba TC358767/TC358867/TC9595 DSI/DPI/eDP bridge

maintainers:
  - <PERSON><PERSON> <andrey.gusa<PERSON>@cogentembedded.com>

description: |
  The TC358767/TC358867/TC9595 is bridge device which
  converts DSI/DPI to eDP/DP .

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - toshiba,tc358867
              - toshiba,tc9595
          - const: toshiba,tc358767
      - const: toshiba,tc358767

  reg:
    enum:
      - 0x68
      - 0x0f
    description: |
        i2c address of the bridge, 0x68 or 0x0f, depending on bootstrap pins

  clock-names:
    const: ref

  clocks:
    maxItems: 1
    description: |
        OF device-tree clock specification for refclk input. The reference.
        clock rate must be 13 MHz, 19.2 MHz, 26 MHz, or 38.4 MHz.

  shutdown-gpios:
    maxItems: 1
    description: |
        OF device-tree gpio specification for SD pin(active high shutdown input)

  reset-gpios:
    maxItems: 1
    description: |
        OF device-tree gpio specification for RSTX pin(active low system reset)

  interrupts:
    maxItems: 1

  toshiba,hpd-pin:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum:
      - 0
      - 1
    description: TC358767 GPIO pin number to which HPD is connected to (0 or 1)

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: |
            DSI input port. The remote endpoint phandle should be a
            reference to a valid DSI output endpoint node

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false

            properties:
              data-lanes:
                description: array of physical DSI data lane indexes.
                minItems: 1
                items:
                  - const: 1
                  - const: 2
                  - const: 3
                  - const: 4

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description: |
            DPI input/output port. The remote endpoint phandle should be a
            reference to a valid DPI output or input endpoint node.

      port@2:
        $ref: /schemas/graph.yaml#/properties/port
        description: |
            eDP/DP output port. The remote endpoint phandle should be a
            reference to a valid eDP panel input endpoint node. This port is
            optional, treated as DP panel if not defined

    oneOf:
      - required:
          - port@0
      - required:
          - port@1


required:
  - compatible
  - reg
  - clock-names
  - clocks
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    /* DPI input and eDP output */

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        edp-bridge@68 {
            compatible = "toshiba,tc358767";
            reg = <0x68>;
            shutdown-gpios = <&gpio3 23 GPIO_ACTIVE_HIGH>;
            reset-gpios = <&gpio3 24 GPIO_ACTIVE_LOW>;
            clock-names = "ref";
            clocks = <&edp_refclk>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@1 {
                    reg = <1>;

                    bridge_in_0: endpoint {
                        remote-endpoint = <&dpi_out>;
                    };
                };

                port@2 {
                    reg = <2>;

                    bridge_out: endpoint {
                        remote-endpoint = <&panel_in>;
                    };
                };
            };
        };
    };
  - |
    /* DPI input and DP output */

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        edp-bridge@68 {
            compatible = "toshiba,tc358767";
            reg = <0x68>;
            shutdown-gpios = <&gpio3 23 GPIO_ACTIVE_HIGH>;
            reset-gpios = <&gpio3 24 GPIO_ACTIVE_LOW>;
            clock-names = "ref";
            clocks = <&edp_refclk>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@1 {
                    reg = <1>;

                    bridge_in_1: endpoint {
                        remote-endpoint = <&dpi_out>;
                    };
                };
            };
        };
    };
