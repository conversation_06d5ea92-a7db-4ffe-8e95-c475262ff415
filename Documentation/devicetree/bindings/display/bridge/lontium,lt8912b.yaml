# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/bridge/lontium,lt8912b.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Lontium LT8912B MIPI to HDMI Bridge

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The LT8912B is a bridge device which convert DSI to HDMI

properties:
  compatible:
    enum:
      - lontium,lt8912b

  reg:
    maxItems: 1

  reset-gpios:
    maxItems: 1
    description: GPIO connected to active high RESET pin.

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description:
          Primary MIPI port for MIPI input

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false

            properties:
              data-lanes: true

            required:
              - data-lanes

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description: |
          HDMI port, should be connected to a node compatible with the
          hdmi-connector binding.

    required:
      - port@0
      - port@1

required:
  - compatible
  - reg
  - reset-gpios
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      hdmi-bridge@48 {
        compatible = "lontium,lt8912b";
        reg = <0x48>;
        reset-gpios = <&max7323 0 GPIO_ACTIVE_LOW>;

        ports {
          #address-cells = <1>;
          #size-cells = <0>;

          port@0 {
            reg = <0>;

            hdmi_out_in: endpoint {
              data-lanes = <0 1 2 3>;
              remote-endpoint = <&mipi_dsi_out>;
            };
          };

          port@1 {
              reg = <1>;

              endpoint {
                  remote-endpoint = <&hdmi_in>;
              };
          };
        };
      };
    };

...
