# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/bridge/ite,it6505.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ITE it6505

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The IT6505 is a high-performance DisplayPort 1.1a transmitter,
  fully compliant with DisplayPort 1.1a, HDCP 1.3 specifications.
  The IT6505 supports color depth of up to 36 bits (12 bits/color)
  and ensures robust transmission of high-quality uncompressed video
  content, along with uncompressed and compressed digital audio content.

  Aside from the various video output formats supported, the IT6505
  also encodes and transmits up to 8 channels of I2S digital audio,
  with sampling rate up to 192kHz and sample size up to 24 bits.
  In addition, an S/PDIF input port takes in compressed audio of up to
  192kHz frame rate.

  Each IT6505 chip comes preprogrammed with an unique HDCP key,
  in compliance with the HDCP 1.3 standard so as to provide secure
  transmission of high-definition content. Users of the IT6505 need not
  purchase any HDCP keys or ROMs.

properties:
  compatible:
    const: ite,it6505

  reg:
    maxItems: 1

  ovdd-supply:
    description: I/O voltage

  pwr18-supply:
    description: core voltage

  interrupts:
    maxItems: 1
    description: interrupt specifier of INT pin

  reset-gpios:
    maxItems: 1
    description: gpio specifier of RESET pin

  extcon:
    maxItems: 1
    description: extcon specifier for the Power Delivery

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: A port node pointing to DPI host port node

        properties:
          endpoint:
            $ref: /schemas/graph.yaml#/$defs/endpoint-base
            unevaluatedProperties: false

            properties:
              link-frequencies:
                minItems: 1
                maxItems: 1
                description: Allowed max link frequencies in Hz

      port@1:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: Video port for DP output

        properties:
          endpoint:
            $ref: /schemas/graph.yaml#/$defs/endpoint-base
            unevaluatedProperties: false

            properties:
              data-lanes:
                minItems: 1
                uniqueItems: true
                items:
                  - enum: [ 0, 1 ]
                  - const: 1
                  - const: 2
                  - const: 3

    required:
      - port@0
      - port@1

required:
  - compatible
  - ovdd-supply
  - pwr18-supply
  - interrupts
  - reset-gpios
  - extcon
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        dp-bridge@5c {
            compatible = "ite,it6505";
            interrupts = <152 IRQ_TYPE_EDGE_FALLING 152 0>;
            reg = <0x5c>;
            pinctrl-names = "default";
            pinctrl-0 = <&it6505_pins>;
            ovdd-supply = <&mt6358_vsim1_reg>;
            pwr18-supply = <&it6505_pp18_reg>;
            reset-gpios = <&pio 179 1>;
            extcon = <&usbc_extcon>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    it6505_in: endpoint {
                        remote-endpoint = <&dpi_out>;
                        link-frequencies = /bits/ 64 <150000000>;
                    };
                };

                port@1 {
                    reg = <1>;
                    it6505_out: endpoint {
                        remote-endpoint = <&dp_in>;
                        data-lanes = <0 1>;
                    };
                };
            };
        };
    };
