# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/display/bridge/toshiba,tc358775.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Toshiba TC358775 DSI to LVDS bridge

maintainers:
  - <PERSON>ay <PERSON>mha <PERSON>N <<EMAIL>>

description: |
  This binding supports DSI to LVDS bridge TC358775

  MIPI DSI-RX Data 4-lane, CLK 1-lane with data rates up to 800 Mbps/lane.
  Video frame size:
  Up to 1600x1200 24-bit/pixel resolution for single-link LVDS display panel
  limited by 135 MHz LVDS speed
  Up to WUXGA (1920x1200 24-bit pixels) resolution for dual-link LVDS display
  panel, limited by 270 MHz LVDS speed.

properties:
  compatible:
    const: toshiba,tc358775

  reg:
    maxItems: 1
    description: i2c address of the bridge, 0x0f

  vdd-supply:
    description: 1.2V LVDS Power Supply

  vddio-supply:
    description: 1.8V IO Power Supply

  stby-gpios:
    maxItems: 1
    description: Standby pin, Low active

  reset-gpios:
    maxItems: 1
    description: Hardware reset, Low active

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description: |
          DSI Input. The remote endpoint phandle should be a
          reference to a valid mipi_dsi_host device node.

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description: |
          Video port for LVDS output (panel or connector).

      port@2:
        $ref: /schemas/graph.yaml#/properties/port
        description: |
          Video port for Dual link LVDS output (panel or connector).

    required:
      - port@0
      - port@1

required:
  - compatible
  - reg
  - vdd-supply
  - vddio-supply
  - stby-gpios
  - reset-gpios
  - ports

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    /* For single-link LVDS display panel */

    i2c@78b8000 {
        /* On High speed expansion */
        label = "HS-I2C2";
        reg = <0x078b8000 0x500>;
        clock-frequency = <400000>; /* fastmode operation */
        #address-cells = <1>;
        #size-cells = <0>;

        tc_bridge: bridge@f {
            compatible = "toshiba,tc358775";
            reg = <0x0f>;

            vdd-supply = <&pm8916_l2>;
            vddio-supply = <&pm8916_l6>;

            stby-gpios = <&msmgpio 99 GPIO_ACTIVE_LOW>;
            reset-gpios = <&msmgpio 72 GPIO_ACTIVE_LOW>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    d2l_in_test: endpoint {
                        remote-endpoint = <&dsi0_out>;
                    };
                };

                port@1 {
                    reg = <1>;
                    lvds_out: endpoint {
                        remote-endpoint = <&panel_in>;
                    };
                };
            };
        };
    };

    dsi@1a98000 {
        reg = <0x1a98000 0x25c>;
        reg-names = "dsi_ctrl";

        ports {
            #address-cells = <1>;
            #size-cells = <0>;
            port@1 {
                reg = <1>;
                dsi0_out: endpoint {
                    remote-endpoint = <&d2l_in_test>;
                    data-lanes = <0 1 2 3>;
                };
             };
         };
     };

  - |
    /* For dual-link LVDS display panel */

    i2c@78b8000 {
        /* On High speed expansion */
        label = "HS-I2C2";
        reg = <0x078b8000 0x500>;
        clock-frequency = <400000>; /* fastmode operation */
        #address-cells = <1>;
        #size-cells = <0>;

        tc_bridge_dual: bridge@f {
            compatible = "toshiba,tc358775";
            reg = <0x0f>;

            vdd-supply = <&pm8916_l2>;
            vddio-supply = <&pm8916_l6>;

            stby-gpios = <&msmgpio 99 GPIO_ACTIVE_LOW>;
            reset-gpios = <&msmgpio 72 GPIO_ACTIVE_LOW>;

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    d2l_in_dual: endpoint {
                        remote-endpoint = <&dsi0_out_dual>;
                    };
                };

                port@1 {
                    reg = <1>;
                    lvds0_out: endpoint {
                        remote-endpoint = <&panel_in0>;
                    };
                };

                port@2 {
                    reg = <2>;
                    lvds1_out: endpoint {
                        remote-endpoint = <&panel_in1>;
                    };
                };
            };
        };
    };

    dsi@1a98000 {
        reg = <0x1a98000 0x25c>;
        reg-names = "dsi_ctrl";

        ports {
            #address-cells = <1>;
            #size-cells = <0>;
            port@1 {
                reg = <1>;
                dsi0_out_dual: endpoint {
                    remote-endpoint = <&d2l_in_dual>;
                    data-lanes = <0 1 2 3>;
                };
             };
         };
     };
...
