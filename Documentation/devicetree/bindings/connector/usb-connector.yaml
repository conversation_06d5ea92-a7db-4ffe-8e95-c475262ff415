# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/connector/usb-connector.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: USB Connector

maintainers:
  - <PERSON> <<EMAIL>>

description:
  A USB connector node represents a physical USB connector. It should be a child
  of a USB interface controller or a separate node when it is attached to both
  MUX and USB interface controller.

properties:
  compatible:
    oneOf:
      - enum:
          - usb-a-connector
          - usb-b-connector
          - usb-c-connector

      - items:
          - const: gpio-usb-b-connector
          - const: usb-b-connector

      - items:
          - const: samsung,usb-connector-11pin
          - const: usb-b-connector

  reg:
    maxItems: 1

  label:
    description: Symbolic name for the connector.

  type:
    description: Size of the connector, should be specified in case of
      non-fullsize 'usb-a-connector' or 'usb-b-connector' compatible
      connectors.
    $ref: /schemas/types.yaml#/definitions/string

    enum:
      - mini
      - micro

  self-powered:
    description: Set this property if the USB device has its own power source.
    type: boolean

  # The following are optional properties for "usb-b-connector".
  id-gpios:
    description: An input gpio for USB ID pin.
    maxItems: 1

  vbus-gpios:
    description: An input gpio for USB VBus pin, used to detect presence of
      VBUS 5V.
    maxItems: 1

  vbus-supply:
    description: A phandle to the regulator for USB VBUS if needed when host
      mode or dual role mode is supported.
      Particularly, if use an output GPIO to control a VBUS regulator, should
      model it as a regulator. See bindings/regulator/fixed-regulator.yaml

  # The following are optional properties for "usb-c-connector".
  power-role:
    description: Determines the power role that the Type C connector will
      support. "dual" refers to Dual Role Port (DRP).
    $ref: /schemas/types.yaml#/definitions/string

    enum:
      - source
      - sink
      - dual

  try-power-role:
    description: Preferred power role.
    $ref: /schemas/types.yaml#/definitions/string

    enum:
      - source
      - sink
      - dual

  data-role:
    description: Data role if Type C connector supports USB data. "dual" refers
      Dual Role Device (DRD).
    $ref: /schemas/types.yaml#/definitions/string

    enum:
      - host
      - device
      - dual

  typec-power-opmode:
    description: Determines the power operation mode that the Type C connector
      will support and will advertise through CC pins when it has no power
      delivery support.
      - "default" corresponds to default USB voltage and current defined by the
        USB 2.0 and USB 3.2 specifications, 5V 500mA for USB 2.0 ports and
        5V 900mA or 1500mA for USB 3.2 ports in single-lane or dual-lane
        operation respectively.
      - "1.5A" and "3.0A", 5V 1.5A and 5V 3.0A respectively, as defined in USB
        Type-C Cable and Connector specification, when Power Delivery is not
        supported.
    $ref: /schemas/types.yaml#/definitions/string
    enum:
      - default
      - 1.5A
      - 3.0A

  pd-disable:
    description: Set this property if the Type-C connector has no power delivery support.
    type: boolean

  # The following are optional properties for "usb-c-connector" with power
  # delivery support.
  source-pdos:
    description: An array of u32 with each entry providing supported power
      source data object(PDO), the detailed bit definitions of PDO can be found
      in "Universal Serial Bus Power Delivery Specification" chapter *******
      Source_Capabilities Message, the order of each entry(PDO) should follow
      the PD spec chapter 6.4.1. Required for power source and power dual role.
      User can specify the source PDO array via PDO_FIXED/BATT/VAR/PPS_APDO()
      defined in dt-bindings/usb/pd.h.
    minItems: 1
    maxItems: 7
    $ref: /schemas/types.yaml#/definitions/uint32-array

  sink-pdos:
    description: An array of u32 with each entry providing supported power sink
      data object(PDO), the detailed bit definitions of PDO can be found in
      "Universal Serial Bus Power Delivery Specification" chapter *******
      Sink Capabilities Message, the order of each entry(PDO) should follow the
      PD spec chapter 6.4.1. Required for power sink and power dual role. User
      can specify the sink PDO array via PDO_FIXED/BATT/VAR/PPS_APDO() defined
      in dt-bindings/usb/pd.h.
    minItems: 1
    maxItems: 7
    $ref: /schemas/types.yaml#/definitions/uint32-array

  sink-vdos:
    description: An array of u32 with each entry, a Vendor Defined Message Object (VDO),
      providing additional information corresponding to the product, the detailed bit
      definitions and the order of each VDO can be found in
      "USB Power Delivery Specification Revision 3.0, Version 2.0 + ECNs 2020-12-10"
      chapter *******.1 Discover Identity. User can specify the VDO array via
      VDO_IDH/_CERT/_PRODUCT/_UFP/_DFP/_PCABLE/_ACABLE(1/2)/_VPD() defined in
      dt-bindings/usb/pd.h.
    minItems: 3
    maxItems: 6
    $ref: /schemas/types.yaml#/definitions/uint32-array

  sink-vdos-v1:
    description: An array of u32 with each entry, a Vendor Defined Message Object (VDO),
      providing additional information corresponding to the product, the detailed bit
      definitions and the order of each VDO can be found in
      "USB Power Delivery Specification Revision 2.0, Version 1.3" chapter *******.1 Discover
      Identity. User can specify the VDO array via VDO_IDH/_CERT/_PRODUCT/_CABLE/_AMA defined in
      dt-bindings/usb/pd.h.
    minItems: 3
    maxItems: 6
    $ref: /schemas/types.yaml#/definitions/uint32-array

  op-sink-microwatt:
    description: Sink required operating power in microwatt, if source can't
      offer the power, Capability Mismatch is set. Required for power sink and
      power dual role.

  port:
    $ref: /schemas/graph.yaml#/properties/port
    description: OF graph bindings modeling a data bus to the connector, e.g.
      there is a single High Speed (HS) port present in this connector. If there
      is more than one bus (several port, with 'reg' property), they can be grouped
      under 'ports'.

  ports:
    $ref: /schemas/graph.yaml#/properties/ports
    description: OF graph bindings modeling any data bus to the connector
      unless the bus is between parent node and the connector. Since a single
      connector can have multiple data buses every bus has an assigned OF graph
      port number as described below.

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/properties/port
        description: High Speed (HS), present in all connectors.

      port@1:
        $ref: /schemas/graph.yaml#/properties/port
        description: Super Speed (SS), present in SS capable connectors.

      port@2:
        $ref: /schemas/graph.yaml#/properties/port
        description: Sideband Use (SBU), present in USB-C. This describes the
          alternate mode connection of which SBU is a part.

    required:
      - port@0

  new-source-frs-typec-current:
    description: Initial current capability of the new source when vSafe5V
      is applied during PD3.0 Fast Role Swap. "Table 6-14 Fixed Supply PDO - Sink"
      of "USB Power Delivery Specification Revision 3.0, Version 1.2" provides the
      different power levels and "*******.1.6 Fast Role Swap USB Type-C Current"
      provides a detailed description of the field. The sink PDO from current source
      reflects the current source's(i.e. transmitter of the FRS signal) power
      requirement during fr swap. The current sink (i.e. receiver of the FRS signal),
      a.k.a new source, should check if it will be able to satisfy the current source's,
      new sink's, requirement during frswap before enabling the frs signal reception.
      This property refers to maximum current capability that the current sink can
      satisfy. During FRS, VBUS voltage is at 5V, as the partners are in implicit
      contract, hence, the power level is only a function of the current capability.
      "1" refers to default USB power level as described by "Table 6-14 Fixed Supply PDO - Sink".
      "2" refers to 1.5A@5V.
      "3" refers to 3.0A@5V.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2, 3]

  slow-charger-loop:
    description: Allows PMIC charger loops which are slow(i.e. cannot meet the 15ms deadline) to
      still comply to pSnkStby i.e Maximum power that can be consumed by sink while in Sink Standby
      state as defined in 7.4.2 Sink Electrical Parameters of USB Power Delivery Specification
      Revision 3.0, Version 1.2. When the property is set, the port requests pSnkStby(2.5W -
      5V@500mA) upon entering SNK_DISCOVERY(instead of 3A or the 1.5A, Rp current advertised, during
      SNK_DISCOVERY) and the actual current limit after reception of PS_Ready for PD link or during
      SNK_READY for non-pd link.
    type: boolean

dependencies:
  sink-vdos-v1: [ sink-vdos ]
  sink-vdos: [ sink-vdos-v1 ]

required:
  - compatible

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: gpio-usb-b-connector
    then:
      anyOf:
        - required:
            - vbus-gpios
        - required:
            - id-gpios

  - if:
      properties:
        compatible:
          contains:
            const: samsung,usb-connector-11pin
    then:
      properties:
        type:
          const: micro

anyOf:
  - not:
      required:
        - typec-power-opmode
        - new-source-frs-typec-current

additionalProperties: false

examples:
  # Micro-USB connector with HS lines routed via controller (MUIC).
  - |
    muic-max77843 {
        usb_con1: connector {
            compatible = "usb-b-connector";
            label = "micro-USB";
            type = "micro";
        };
    };

  # USB-C connector attached to CC controller (s2mm005), HS lines routed
  # to companion PMIC (max77865), SS lines to USB3 PHY and SBU to DisplayPort.
  # DisplayPort video lines are routed to the connector via SS mux in USB3 PHY.
  - |
    ccic: s2mm005 {
        usb_con2: connector {
            compatible = "usb-c-connector";
            label = "USB-C";

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    usb_con_hs: endpoint {
                        remote-endpoint = <&max77865_usbc_hs>;
                    };
                };
                port@1 {
                    reg = <1>;
                    usb_con_ss: endpoint {
                        remote-endpoint = <&usbdrd_phy_ss>;
                    };
                };
                port@2 {
                    reg = <2>;
                    usb_con_sbu: endpoint {
                        remote-endpoint = <&dp_aux>;
                    };
                };
            };
        };
    };

  # USB-C connector attached to a typec port controller(ptn5110), which has
  # power delivery support and enables drp.
  - |
    #include <dt-bindings/usb/pd.h>
    typec: ptn5110 {
        usb_con3: connector {
            compatible = "usb-c-connector";
            label = "USB-C";
            power-role = "dual";
            try-power-role = "sink";
            source-pdos = <PDO_FIXED(5000, 2000, PDO_FIXED_USB_COMM)>;
            sink-pdos = <PDO_FIXED(5000, 2000, PDO_FIXED_USB_COMM)
                         PDO_VAR(5000, 12000, 2000)>;
            op-sink-microwatt = <10000000>;
        };
    };

  # USB-C connector attached to SoC with a single High-Speed controller
  - |
    connector {
        compatible = "usb-c-connector";
        label = "USB-C";

        port {
            high_speed_ep: endpoint {
                remote-endpoint = <&usb_hs_ep>;
            };
        };
    };

  # USB-C connector attached to SoC and USB3 typec port controller(hd3ss3220)
  # with SS 2:1 MUX. HS lines routed to SoC, SS lines routed to the MUX and
  # the output of MUX is connected to the SoC.
  - |
    connector {
        compatible = "usb-c-connector";
        label = "USB-C";
        data-role = "dual";

        ports {
            #address-cells = <1>;
            #size-cells = <0>;
            port@0 {
                reg = <0>;
                hs_ep: endpoint {
                    remote-endpoint = <&usb3_hs_ep>;
                };
            };
            port@1 {
                reg = <1>;
                ss_ep: endpoint {
                    remote-endpoint = <&hd3ss3220_in_ep>;
                };
            };
        };
    };

  # USB connector with GPIO control lines
  - |
    #include <dt-bindings/gpio/gpio.h>

    usb {
        connector {
            compatible = "gpio-usb-b-connector", "usb-b-connector";
            type = "micro";
            id-gpios = <&pio 12 GPIO_ACTIVE_HIGH>;
            vbus-supply = <&usb_p0_vbus>;
        };
    };

  # Micro-USB connector with HS lines routed via controller (MUIC) and MHL
  # lines connected to HDMI-MHL bridge (sii8620) on Samsung Exynos5433-based
  # mobile phone
  - |
    muic-max77843 {
        usb_con4: connector {
            compatible = "samsung,usb-connector-11pin", "usb-b-connector";
            label = "micro-USB";
            type = "micro";

            ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                    reg = <0>;
                    muic_to_usb: endpoint {
                        remote-endpoint = <&usb_to_muic>;
                    };
                };
                port@3 {
                    reg = <3>;
                    usb_con_mhl: endpoint {
                        remote-endpoint = <&sii8620_mhl>;
                    };
                };
            };
        };
    };
