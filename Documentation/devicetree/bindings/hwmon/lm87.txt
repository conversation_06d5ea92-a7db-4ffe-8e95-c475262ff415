*LM87 hwmon sensor.

Required properties:
- compatible: Should be
	"ti,lm87"

- reg: I2C address

optional properties:
- has-temp3: This configures pins 18 and 19 to be used as a second
             remote temperature sensing channel. By default the pins
             are configured as voltage input pins in0 and in5.

- has-in6: When set, pin 5 is configured to be used as voltage input
           in6. Otherwise the pin is set as FAN1 input.

- has-in7: When set, pin 6 is configured to be used as voltage input
           in7. Otherwise the pin is set as FAN2 input.

- vcc-supply: a Phandle for the regulator supplying power, can be
              configured to measure 5.0V power supply. Default is 3.3V.

Example:

lm87@2e {
	compatible = "ti,lm87";
	reg = <0x2e>;
	has-temp3;
	vcc-supply = <&reg_5v0>;
};
