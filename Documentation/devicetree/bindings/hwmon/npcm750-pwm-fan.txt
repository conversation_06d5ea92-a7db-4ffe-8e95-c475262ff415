Nuvoton NPCM7xx PWM and Fan Tacho controller device

The Nuvoton BMC NPCM7XX supports 8 Pulse-width modulation (PWM)
controller outputs and 16 Fan tachometer controller inputs.

Required properties for pwm-fan node
- #address-cells : should be 1.
- #size-cells	: should be 0.
- compatible	: "nuvoton,npcm750-pwm-fan" for Poleg NPCM7XX.
- reg			: specifies physical base address and size of the registers.
- reg-names	: must contain:
					* "pwm" for the PWM registers.
					* "fan" for the Fan registers.
- clocks		: phandle of reference clocks.
- clock-names	: must contain
					* "pwm" for PWM controller operating clock.
					* "fan" for Fan controller operating clock.
- interrupts	: contain the Fan interrupts with flags for falling edge.
- pinctrl-names	: a pinctrl state named "default" must be defined.
- pinctrl-0	: phandle referencing pin configuration of the PWM and Fan
					controller ports.

fan subnode format:
===================
Under fan subnode can be upto 8 child nodes, each child node representing a fan.
Each fan subnode must have one PWM channel and at least one Fan tach channel.

For PWM channel can be configured cooling-levels to create cooling device.
Cooling device could be bound to a thermal zone for the thermal control.

Required properties for each child node:
- reg : specify the PWM output channel.
	integer value in the range 0 through 7, that represent
	the PWM channel number that used.

- fan-tach-ch : specify the Fan tach input channel.
		integer value in the range 0 through 15, that represent
		the fan tach channel number that used.

		At least one Fan tach input channel is required

Optional property for each child node:
- cooling-levels: PWM duty cycle values in a range from 0 to 255
                  which correspond to thermal cooling states.

Examples:

pwm_fan:pwm-fan-controller@103000 {
	#address-cells = <1>;
	#size-cells = <0>;
	compatible = "nuvoton,npcm750-pwm-fan";
	reg = <0x103000 0x2000>,
		<0x180000 0x8000>;
	reg-names = "pwm", "fan";
	clocks = <&clk NPCM7XX_CLK_APB3>,
		<&clk NPCM7XX_CLK_APB4>;
	clock-names = "pwm","fan";
	interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
			<GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
	pinctrl-names = "default";
	pinctrl-0 = <&pwm0_pins &pwm1_pins &pwm2_pins
			&fanin0_pins &fanin1_pins &fanin2_pins
			&fanin3_pins &fanin4_pins>;
	fan@0 {
		reg = <0x00>;
		fan-tach-ch = /bits/ 8 <0x00 0x01>;
		cooling-levels = <127 255>;
	};
	fan@1 {
		reg = <0x01>;
		fan-tach-ch = /bits/ 8 <0x02 0x03>;
	};
	fan@2 {
		reg = <0x02>;
		fan-tach-ch = /bits/ 8 <0x04>;
	};

};
