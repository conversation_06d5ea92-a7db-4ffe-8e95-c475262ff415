# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/adi,ltc2945.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices LTC2945 wide range i2c power monitor

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Analog Devices LTC2945 wide range i2c power monitor over I2C.

  https://www.analog.com/media/en/technical-documentation/data-sheets/LTC2945.pdf

properties:
  compatible:
    enum:
      - adi,ltc2945

  reg:
    maxItems: 1

  shunt-resistor-micro-ohms:
    description:
      Shunt resistor value in micro-Ohms
    default: 1000

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        power-monitor@6e {
            compatible = "adi,ltc2945";
            reg = <0x6e>;
            /* 10 milli-Ohm shunt resistor */
            shunt-resistor-micro-ohms = <10000>;
        };
    };
...
