# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/adi,adm1177.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices ADM1177 Hot Swap Controller and Digital Power Monitor

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices ADM1177 Hot Swap Controller and Digital Power Monitor
  https://www.analog.com/media/en/technical-documentation/data-sheets/ADM1177.pdf

properties:
  compatible:
    enum:
      - adi,adm1177

  reg:
    maxItems: 1

  avcc-supply:
    description:
      Phandle to the Avcc power supply

  shunt-resistor-micro-ohms:
    description:
      The value of current sense resistor in microohms. If not provided,
      the current reading and overcurrent alert is disabled.

  adi,shutdown-threshold-microamp:
    description:
      Specifies the current level at which an over current alert occurs.
      If not provided, the overcurrent alert is configured to max ADC range
      based on shunt-resistor-micro-ohms.

  adi,vrange-high-enable:
    description:
      Specifies which internal voltage divider to be used. A 1 selects
      a 7:2 voltage divider while a 0 selects a 14:1 voltage divider.
    type: boolean

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        pwmon@5a {
            compatible = "adi,adm1177";
            reg = <0x5a>;
            shunt-resistor-micro-ohms = <50000>; /* 50 mOhm */
            adi,shutdown-threshold-microamp = <1059000>; /* 1.059 A */
            adi,vrange-high-enable;
        };
    };
...
