# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/hwmon/adi,max31827.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Analog Devices MAX31827, MAX31828, MAX31829 Low-Power Temperature Switch

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Analog Devices MAX31827, MAX31828, MAX31829 Low-Power Temperature Switch with
  I2C Interface
  https://www.analog.com/media/en/technical-documentation/data-sheets/MAX31827-MAX31829.pdf

properties:
  compatible:
    oneOf:
      - const: adi,max31827
      - items:
          - enum:
              - adi,max31828
              - adi,max31829
          - const: adi,max31827

  reg:
    maxItems: 1

  vref-supply:
    description:
      Must have values in the interval (1.6V; 3.6V) in order for the device to
      function correctly.

required:
  - compatible
  - reg
  - vref-supply

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        temperature-sensor@42 {
            compatible = "adi,max31827";
            reg = <0x42>;
            vref-supply = <&reg_vdd>;
        };
    };
...
