# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/media/cec/st,stm32-cec.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 CEC

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: st,stm32-cec

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    items:
      - description: Module Clock
      - description: Bus Clock

  clock-names:
    items:
      - const: cec
      - const: hdmi-cec

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/stm32mp1-clks.h>
    cec: cec@40006c00 {
        compatible = "st,stm32-cec";
        reg = <0x40006c00 0x400>;
        interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&rcc CEC_K>, <&clk_lse>;
        clock-names = "cec", "hdmi-cec";
    };

...
