# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/media/i2c/ti,ds90ub960.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments DS90UB9XX Family FPD-Link Deserializer Hubs

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description:
  The TI DS90UB9XX devices are FPD-Link video deserializers with I2C and GPIO
  forwarding.

allOf:
  - $ref: /schemas/i2c/i2c-atr.yaml#

properties:
  compatible:
    enum:
      - ti,ds90ub960-q1
      - ti,ds90ub9702-q1

  reg:
    maxItems: 1

  clocks:
    maxItems: 1
    description:
      Reference clock connected to the REFCLK pin.

  clock-names:
    items:
      - const: refclk

  powerdown-gpios:
    maxItems: 1
    description:
      Specifier for the GPIO connected to the PDB pin.

  i2c-alias-pool:
    minItems: 1
    maxItems: 32

  links:
    type: object
    additionalProperties: false

    properties:
      '#address-cells':
        const: 1

      '#size-cells':
        const: 0

      ti,manual-strobe:
        type: boolean
        description:
          Enable manual strobe position and EQ level

    patternProperties:
      '^link@[0-3]$':
        type: object
        additionalProperties: false
        properties:
          reg:
            description: The link number
            maxItems: 1

          i2c-alias:
            description:
              The I2C address used for the serializer. Transactions to this
              address on the I2C bus where the deserializer resides are
              forwarded to the serializer.

          ti,rx-mode:
            $ref: /schemas/types.yaml#/definitions/uint32
            enum:
              - 0 # RAW10
              - 1 # RAW12 HF
              - 2 # RAW12 LF
              - 3 # CSI2 SYNC
              - 4 # CSI2 NON-SYNC
            description:
              FPD-Link Input Mode. This should reflect the hardware and the
              default mode of the connected device.

          ti,cdr-mode:
            $ref: /schemas/types.yaml#/definitions/uint32
            enum:
              - 0 # FPD-Link III
              - 1 # FPD-Link IV
            description:
              FPD-Link CDR Mode. This should reflect the hardware and the
              default mode of the connected device.

          ti,strobe-pos:
            $ref: /schemas/types.yaml#/definitions/int32
            minimum: -13
            maximum: 13
            description: Manual strobe position

          ti,eq-level:
            $ref: /schemas/types.yaml#/definitions/uint32
            maximum: 14
            description: Manual EQ level

          serializer:
            type: object
            description: FPD-Link Serializer node

        required:
          - reg
          - i2c-alias
          - ti,rx-mode
          - serializer

  ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port@0:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: FPD-Link input 0

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            description:
              Endpoint for FPD-Link port. If the RX mode for this port is RAW,
              hsync-active and vsync-active must be defined.

      port@1:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: FPD-Link input 1

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            description:
              Endpoint for FPD-Link port. If the RX mode for this port is RAW,
              hsync-active and vsync-active must be defined.

      port@2:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: FPD-Link input 2

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            description:
              Endpoint for FPD-Link port. If the RX mode for this port is RAW,
              hsync-active and vsync-active must be defined.

      port@3:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: FPD-Link input 3

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false
            description:
              Endpoint for FPD-Link port. If the RX mode for this port is RAW,
              hsync-active and vsync-active must be defined.

      port@4:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: CSI-2 Output 0

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false

            properties:
              data-lanes:
                minItems: 1
                maxItems: 4
              link-frequencies:
                maxItems: 1

            required:
              - data-lanes
              - link-frequencies

      port@5:
        $ref: /schemas/graph.yaml#/$defs/port-base
        unevaluatedProperties: false
        description: CSI-2 Output 1

        properties:
          endpoint:
            $ref: /schemas/media/video-interfaces.yaml#
            unevaluatedProperties: false

            properties:
              data-lanes:
                minItems: 1
                maxItems: 4
              link-frequencies:
                maxItems: 1

            required:
              - data-lanes
              - link-frequencies

    required:
      - port@0
      - port@1
      - port@2
      - port@3
      - port@4
      - port@5

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - ports

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
      clock-frequency = <400000>;
      #address-cells = <1>;
      #size-cells = <0>;

      deser@3d {
        compatible = "ti,ds90ub960-q1";
        reg = <0x3d>;

        clock-names = "refclk";
        clocks = <&fixed_clock>;

        powerdown-gpios = <&pca9555 7 GPIO_ACTIVE_LOW>;

        i2c-alias-pool = <0x4a 0x4b 0x4c 0x4d 0x4e 0x4f>;

        ports {
          #address-cells = <1>;
          #size-cells = <0>;

          /* Port 0, Camera 0 */
          port@0 {
            reg = <0>;

            ub960_fpd3_1_in: endpoint {
              remote-endpoint = <&ub953_1_out>;
            };
          };

          /* Port 1, Camera 1 */
          port@1 {
            reg = <1>;

            ub960_fpd3_2_in: endpoint {
              remote-endpoint = <&ub913_2_out>;
              hsync-active = <0>;
              vsync-active = <1>;
            };
          };

          /* Port 2, unconnected */
          port@2 {
            reg = <2>;
          };

          /* Port 3, unconnected */
          port@3 {
            reg = <3>;
          };

          /* Port 4, CSI-2 TX */
          port@4 {
            reg = <4>;
            ds90ub960_0_csi_out: endpoint {
              data-lanes = <1 2 3 4>;
              link-frequencies = /bits/ 64 <800000000>;
              remote-endpoint = <&csi2_phy0>;
            };
          };

          /* Port 5, unconnected */
          port@5 {
            reg = <5>;
          };
        };

        links {
          #address-cells = <1>;
          #size-cells = <0>;

          /* Link 0 has DS90UB953 serializer and IMX274 sensor */

          link@0 {
            reg = <0>;
            i2c-alias = <0x44>;

            ti,rx-mode = <3>;

            serializer1: serializer {
              compatible = "ti,ds90ub953-q1";

              gpio-controller;
              #gpio-cells = <2>;

              #clock-cells = <0>;

              ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                  reg = <0>;
                  ub953_1_in: endpoint {
                    data-lanes = <1 2 3 4>;
                    remote-endpoint = <&sensor_1_out>;
                  };
                };

                port@1 {
                  reg = <1>;

                  ub953_1_out: endpoint {
                    remote-endpoint = <&ub960_fpd3_1_in>;
                  };
                };
              };

              i2c {
                #address-cells = <1>;
                #size-cells = <0>;

                sensor@1a {
                  compatible = "sony,imx274";
                  reg = <0x1a>;

                  reset-gpios = <&serializer1 0 GPIO_ACTIVE_LOW>;

                  port {
                    sensor_1_out: endpoint {
                      remote-endpoint = <&ub953_1_in>;
                    };
                  };
                };
              };
            };
          };  /* End of link@0 */

          /* Link 1 has DS90UB913 serializer and MT9V111 sensor */

          link@1 {
            reg = <1>;
            i2c-alias = <0x45>;

            ti,rx-mode = <0>;

            serializer2: serializer {
              compatible = "ti,ds90ub913a-q1";

              gpio-controller;
              #gpio-cells = <2>;

              clocks = <&clk_cam_48M>;
              clock-names = "clkin";

              #clock-cells = <0>;

              ports {
                #address-cells = <1>;
                #size-cells = <0>;

                port@0 {
                  reg = <0>;
                  ub913_2_in: endpoint {
                    remote-endpoint = <&sensor_2_out>;
                    pclk-sample = <1>;
                  };
                };

                port@1 {
                  reg = <1>;

                  ub913_2_out: endpoint {
                    remote-endpoint = <&ub960_fpd3_2_in>;
                  };
                };
              };

              i2c {
                #address-cells = <1>;
                #size-cells = <0>;

                sensor@48 {
                  compatible = "aptina,mt9v111";
                  reg = <0x48>;

                  clocks = <&serializer2>;

                  port {
                    sensor_2_out: endpoint {
                      remote-endpoint = <&ub913_2_in>;
                    };
                  };
                };
              };
            };
          }; /* End of link@1 */
        };
      };
    };
...
