# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/media/i2c/sony,imx415.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Sony IMX415 CMOS Image Sensor

maintainers:
  - <PERSON> <<EMAIL>>

description: |-
  The Sony IMX415 is a diagonal 6.4 mm (Type 1/2.8) CMOS active pixel type
  solid-state image sensor with a square pixel array and 8.46 M effective
  pixels. This chip operates with analog 2.9 V, digital 1.1 V, and interface
  1.8 V triple power supply, and has low power consumption.
  The IMX415 is programmable through I2C interface. The sensor output is
  available via CSI-2 serial data output (two or four lanes).

allOf:
  - $ref: ../video-interface-devices.yaml#

properties:
  compatible:
    const: sony,imx415

  reg:
    maxItems: 1

  clocks:
    description: Input clock (24 MHz, 27 MHz, 37.125 MHz, 72 MHz or 74.25 MHz)
    maxItems: 1

  avdd-supply:
    description: Analog power supply (2.9 V)

  dvdd-supply:
    description: Digital power supply (1.1 V)

  ovdd-supply:
    description: Interface power supply (1.8 V)

  reset-gpios:
    description: Sensor reset (XCLR) GPIO
    maxItems: 1

  flash-leds: true

  lens-focus: true

  orientation: true

  rotation: true

  port:
    $ref: /schemas/graph.yaml#/$defs/port-base
    unevaluatedProperties: false

    properties:
      endpoint:
        $ref: /schemas/media/video-interfaces.yaml#
        unevaluatedProperties: false

        properties:
          data-lanes:
            oneOf:
              - items:
                  - const: 1
                  - const: 2
              - items:
                  - const: 1
                  - const: 2
                  - const: 3
                  - const: 4

        required:
          - data-lanes
          - link-frequencies

    required:
      - endpoint

required:
  - compatible
  - reg
  - clocks
  - avdd-supply
  - dvdd-supply
  - ovdd-supply
  - port

additionalProperties: false

examples:
  - |
    #include <dt-bindings/gpio/gpio.h>

    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        imx415: camera-sensor@1a {
            compatible = "sony,imx415";
            reg = <0x1a>;
            avdd-supply = <&vcc2v9_cam>;
            clocks = <&clock_cam>;
            dvdd-supply = <&vcc1v1_cam>;
            lens-focus = <&vcm>;
            orientation = <2>;
            ovdd-supply = <&vcc1v8_cam>;
            reset-gpios = <&gpio_expander 14 GPIO_ACTIVE_LOW>;
            rotation = <180>;

            port {
                imx415_ep: endpoint {
                    data-lanes = <1 2 3 4>;
                    link-frequencies = /bits/ 64 <445500000>;
                    remote-endpoint = <&mipi_in>;
                };
            };
        };
    };
...
