# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/xlnx,zynqmp-gpio-modepin.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ZynqMP Mode Pin GPIO controller

description:
  PS_MODE is 4-bits boot mode pins sampled on POR deassertion. Mode Pin
  GPIO controller with configurable from numbers of pins (from 0 to 3 per
  PS_MODE). Every pin can be configured as input/output.

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: xlnx,zynqmp-gpio-modepin

  gpio-controller: true

  "#gpio-cells":
    const: 2

required:
  - compatible
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    zynqmp-firmware {
        gpio {
            compatible = "xlnx,zynqmp-gpio-modepin";
            gpio-controller;
            #gpio-cells = <2>;
        };
    };

...
