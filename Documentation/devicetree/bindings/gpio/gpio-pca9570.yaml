# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-pca9570.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: PCA9570 I2C GPO expander

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - dlg,slg7xl45106
      - nxp,pca9570
      - nxp,pca9571

  reg:
    maxItems: 1

  gpio-controller: true

  '#gpio-cells':
    const: 2

  gpio-line-names:
    minItems: 4
    maxItems: 8

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        gpio@24 {
            compatible = "nxp,pca9570";
            reg = <0x24>;
            gpio-controller;
            #gpio-cells = <2>;
        };
    };

...
