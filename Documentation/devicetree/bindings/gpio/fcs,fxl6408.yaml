# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/fcs,fxl6408.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Fairchild FXL6408 I2C GPIO Expander

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - fcs,fxl6408

  reg:
    maxItems: 1

  "#gpio-cells":
    const: 2

  gpio-controller: true

  gpio-line-names:
    minItems: 1
    maxItems: 8

patternProperties:
  "^(hog-[0-9]+|.+-hog(-[0-9]+)?)$":
    required:
      - gpio-hog

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    i2c {
        #address-cells = <1>;
        #size-cells = <0>;

        gpio_expander_43: gpio-expander@43 {
            compatible = "fcs,fxl6408";
            reg = <0x43>;
            gpio-controller;
            #gpio-cells = <2>;
            gpio-line-names = "Wi-Fi_W_DISABLE", "Wi-Fi_WKUP_WLAN",
                              "PWR_EN_+V3.3_WiFi_N", "PCIe_REF_CLK_EN",
                              "USB_RESET_N", "USB_BYPASS_N", "Wi-Fi_PDn",
                              "Wi-Fi_WKUP_BT";
        };
    };
