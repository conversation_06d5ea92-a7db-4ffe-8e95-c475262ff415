# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/gpio-ep9301.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: EP93xx GPIO controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - const: cirrus,ep9301-gpio
      - items:
          - enum:
              - cirrus,ep9302-gpio
              - cirrus,ep9307-gpio
              - cirrus,ep9312-gpio
              - cirrus,ep9315-gpio
          - const: cirrus,ep9301-gpio

  reg:
    minItems: 2
    items:
      - description: data register
      - description: direction register
      - description: interrupt registers base

  reg-names:
    minItems: 2
    items:
      - const: data
      - const: dir
      - const: intr

  gpio-controller: true

  gpio-ranges: true

  "#gpio-cells":
    const: 2

  interrupt-controller: true

  "#interrupt-cells":
    const: 2

  interrupts:
    oneOf:
      - maxItems: 1
      - description: port F has dedicated irq line for each gpio line
        maxItems: 8

required:
  - compatible
  - reg
  - gpio-controller
  - "#gpio-cells"

additionalProperties: false

examples:
  - |
    gpio@80840000 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840000 0x04>,
            <0x80840010 0x04>,
            <0x80840090 0x1c>;
      reg-names = "data", "dir", "intr";
      gpio-controller;
      #gpio-cells = <2>;
        interrupt-controller;
        interrupt-parent = <&vic1>;
        interrupts = <27>;
    };

    gpio@80840004 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840004 0x04>,
            <0x80840014 0x04>,
            <0x808400ac 0x1c>;
      reg-names = "data", "dir", "intr";
      gpio-controller;
      #gpio-cells = <2>;
      interrupt-controller;
      interrupt-parent = <&vic1>;
      interrupts = <27>;
    };

    gpio@80840008 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840008 0x04>,
            <0x80840018 0x04>;
      reg-names = "data", "dir";
      gpio-controller;
      #gpio-cells = <2>;
    };

    gpio@8084000c {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x8084000c 0x04>,
            <0x8084001c 0x04>;
      reg-names = "data", "dir";
      gpio-controller;
      #gpio-cells = <2>;
    };

    gpio@80840020 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840020 0x04>,
            <0x80840024 0x04>;
      reg-names = "data", "dir";
      gpio-controller;
      #gpio-cells = <2>;
    };

    gpio@80840030 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840030 0x04>,
            <0x80840034 0x04>,
            <0x8084004c 0x1c>;
      reg-names = "data", "dir", "intr";
      gpio-controller;
      #gpio-cells = <2>;
      interrupt-controller;
      interrupts-extended = <&vic0 19>, <&vic0 20>,
                            <&vic0 21>, <&vic0 22>,
                            <&vic1 15>, <&vic1 16>,
                            <&vic1 17>, <&vic1 18>;
    };

    gpio@80840038 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840038 0x04>,
            <0x8084003c 0x04>;
      reg-names = "data", "dir";
      gpio-controller;
      #gpio-cells = <2>;
    };

    gpio@80840040 {
      compatible = "cirrus,ep9301-gpio";
      reg = <0x80840040 0x04>,
            <0x80840044 0x04>;
      reg-names = "data", "dir";
      gpio-controller;
      #gpio-cells = <2>;
    };

...
