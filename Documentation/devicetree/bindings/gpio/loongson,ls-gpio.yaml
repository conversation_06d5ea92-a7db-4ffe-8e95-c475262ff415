# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpio/loongson,ls-gpio.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Loongson GPIO controller.

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - loongson,ls2k-gpio
      - loongson,ls7a-gpio

  reg:
    maxItems: 1

  ngpios:
    minimum: 1
    maximum: 64

  "#gpio-cells":
    const: 2

  gpio-controller: true

  gpio-ranges: true

  interrupts:
    minItems: 1
    maxItems: 64

required:
  - compatible
  - reg
  - ngpios
  - "#gpio-cells"
  - gpio-controller
  - gpio-ranges
  - interrupts

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    gpio0: gpio@1fe00500 {
      compatible = "loongson,ls2k-gpio";
      reg = <0x1fe00500 0x38>;
      ngpios = <64>;
      #gpio-cells = <2>;
      gpio-controller;
      gpio-ranges = <&pctrl 0 0 15>,
                    <&pctrl 16 16 15>,
                    <&pctrl 32 32 10>,
                    <&pctrl 44 44 20>;
      interrupt-parent = <&liointc1>;
      interrupts = <28 IRQ_TYPE_LEVEL_LOW>,
                   <29 IRQ_TYPE_LEVEL_LOW>,
                   <30 IRQ_TYPE_LEVEL_LOW>,
                   <30 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <26 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <>,
                   <>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>,
                   <27 IRQ_TYPE_LEVEL_LOW>;
    };
