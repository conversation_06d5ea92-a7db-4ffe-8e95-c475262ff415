# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpu/arm,mali-utgard.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM Mali Utgard GPU

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: '^gpu@[a-f0-9]+$'
  compatible:
    oneOf:
      - items:
          - const: allwinner,sun8i-a23-mali
          - const: allwinner,sun7i-a20-mali
          - const: arm,mali-400
      - items:
          - enum:
              - allwinner,sun4i-a10-mali
              - allwinner,sun7i-a20-mali
              - allwinner,sun8i-h3-mali
              - allwinner,sun8i-r40-mali
              - allwinner,sun50i-a64-mali
              - rockchip,rk3036-mali
              - rockchip,rk3066-mali
              - rockchip,rk3188-mali
              - rockchip,rk3228-mali
              - samsung,exynos4210-mali
              - ster<PERSON><PERSON>,db8500-mali
              - xlnx,zynqmp-mali
          - const: arm,mali-400
      - items:
          - enum:
              - allwinner,sun50i-h5-mali
              - amlogic,meson8-mali
              - amlogic,meson8b-mali
              - amlogic,meson-gxbb-mali
              - amlogic,meson-gxl-mali
              - hisilicon,hi6220-mali
              - mediatek,mt7623-mali
              - rockchip,rk3328-mali
          - const: arm,mali-450

      # "arm,mali-300"

  reg:
    maxItems: 1

  interrupts:
    minItems: 4
    maxItems: 20

  interrupt-names:
    allOf:
      - additionalItems: true
        minItems: 4
        maxItems: 20
        items:
          # At least enforce the first 2 interrupts
          - const: gp
          - const: gpmmu
      - items:
          # Not ideal as any order and combination are allowed
          enum:
            - gp        # Geometry Processor interrupt
            - gpmmu     # Geometry Processor MMU interrupt
            - pp        # Pixel Processor broadcast interrupt (mali-450 only)
            - pp0       # Pixel Processor X interrupt (X from 0 to 7)
            - ppmmu0    # Pixel Processor X MMU interrupt (X from 0 to 7)
            - pp1
            - ppmmu1
            - pp2
            - ppmmu2
            - pp3
            - ppmmu3
            - pp4
            - ppmmu4
            - pp5
            - ppmmu5
            - pp6
            - ppmmu6
            - pp7
            - ppmmu7
            - pmu       # Power Management Unit interrupt (optional)
            - combined  # stericsson,db8500-mali only

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: bus
      - const: core

  memory-region: true

  mali-supply: true

  opp-table:
    type: object

  power-domains:
    maxItems: 1

  resets:
    maxItems: 1

  operating-points-v2: true

  "#cooling-cells":
    const: 2

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-names

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - allwinner,sun4i-a10-mali
              - allwinner,sun7i-a20-mali
              - allwinner,sun8i-r40-mali
              - allwinner,sun50i-a64-mali
              - allwinner,sun50i-h5-mali
              - amlogic,meson8-mali
              - amlogic,meson8b-mali
              - hisilicon,hi6220-mali
              - mediatek,mt7623-mali
              - rockchip,rk3036-mali
              - rockchip,rk3066-mali
              - rockchip,rk3188-mali
              - rockchip,rk3228-mali
              - rockchip,rk3328-mali
    then:
      required:
        - resets

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    mali: gpu@1c40000 {
      compatible = "allwinner,sun7i-a20-mali", "arm,mali-400";
      reg = <0x01c40000 0x10000>;
      interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
      interrupt-names = "gp",
            "gpmmu",
            "pp0",
            "ppmmu0",
            "pp1",
            "ppmmu1",
            "pmu";
      clocks = <&ccu 1>, <&ccu 2>;
      clock-names = "bus", "core";
      resets = <&ccu 1>;
      #cooling-cells = <2>;
    };

...
