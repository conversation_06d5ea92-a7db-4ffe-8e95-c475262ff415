# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/gpu/arm,mali-midgard.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM Mali Midgard GPU

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    pattern: '^gpu@[a-f0-9]+$'
  compatible:
    oneOf:
      - items:
          - enum:
              - samsung,exynos5250-mali
          - const: arm,mali-t604
      - items:
          - enum:
              - samsung,exynos5420-mali
          - const: arm,mali-t628
      - items:
          - enum:
              - allwinner,sun50i-h6-mali
          - const: arm,mali-t720
      - items:
          - enum:
              - amlogic,meson-gxm-mali
              - realtek,rtd1295-mali
          - const: arm,mali-t820
      - items:
          - enum:
              - arm,juno-mali
          - const: arm,mali-t624
      - items:
          - enum:
              - rockchip,rk3288-mali
              - samsung,exynos5433-mali
          - const: arm,mali-t760
      - items:
          - enum:
              - rockchip,rk3399-mali
          - const: arm,mali-t860

          # "arm,mali-t830"
          # "arm,mali-t880"

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: Job interrupt
      - description: MMU interrupt
      - description: GPU interrupt

  interrupt-names:
    items:
      - const: job
      - const: mmu
      - const: gpu

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    items:
      - const: core
      - const: bus

  mali-supply: true
  opp-table:
    type: object

  power-domains:
    maxItems: 1

  resets:
    minItems: 1
    maxItems: 2

  operating-points-v2: true

  "#cooling-cells":
    const: 2

  dma-coherent: true

  dynamic-power-coefficient:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      A u32 value that represents the running time dynamic
      power coefficient in units of uW/MHz/V^2. The
      coefficient can either be calculated from power
      measurements or derived by analysis.

      The dynamic power consumption of the GPU is
      proportional to the square of the Voltage (V) and
      the clock frequency (f). The coefficient is used to
      calculate the dynamic power as below -

      Pdyn = dynamic-power-coefficient * V^2 * f

      where voltage is in V, frequency is in MHz.

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: allwinner,sun50i-h6-mali
    then:
      properties:
        clocks:
          minItems: 2
      required:
        - clock-names
        - resets
  - if:
      properties:
        compatible:
          contains:
            const: amlogic,meson-gxm-mali
    then:
      properties:
        resets:
          minItems: 2
      required:
        - resets

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    gpu@ffa30000 {
      compatible = "rockchip,rk3288-mali", "arm,mali-t760";
      reg = <0xffa30000 0x10000>;
      interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
             <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
      interrupt-names = "job", "mmu", "gpu";
      clocks = <&cru 0>;
      mali-supply = <&vdd_gpu>;
      operating-points-v2 = <&gpu_opp_table>;
      power-domains = <&power 0>;
      #cooling-cells = <2>;
    };

    gpu_opp_table: opp-table {
      compatible = "operating-points-v2";

      opp-533000000 {
        opp-hz = /bits/ 64 <533000000>;
        opp-microvolt = <1250000>;
      };
      opp-450000000 {
        opp-hz = /bits/ 64 <450000000>;
        opp-microvolt = <1150000>;
      };
      opp-400000000 {
        opp-hz = /bits/ 64 <400000000>;
        opp-microvolt = <1125000>;
      };
      opp-350000000 {
        opp-hz = /bits/ 64 <350000000>;
        opp-microvolt = <1075000>;
      };
      opp-266000000 {
        opp-hz = /bits/ 64 <266000000>;
        opp-microvolt = <1025000>;
      };
      opp-160000000 {
        opp-hz = /bits/ 64 <160000000>;
        opp-microvolt = <925000>;
      };
      opp-100000000 {
        opp-hz = /bits/ 64 <100000000>;
        opp-microvolt = <912500>;
      };
    };

...
