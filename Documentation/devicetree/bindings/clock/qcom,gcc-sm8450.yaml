# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sm8450.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SM8450

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SM8450

  See also:: include/dt-bindings/clock/qcom,gcc-sm8450.h

properties:
  compatible:
    const: qcom,gcc-sm8450

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: PCIE 0 Pipe clock source (Optional clock)
      - description: PCIE 1 Pipe clock source (Optional clock)
      - description: PCIE 1 Phy Auxiliary clock source (Optional clock)
      - description: UFS Phy Rx symbol 0 clock source (Optional clock)
      - description: UFS Phy Rx symbol 1 clock source (Optional clock)
      - description: UFS Phy Tx symbol 0 clock source (Optional clock)
      - description: USB3 Phy wrapper pipe clock source (Optional clock)
    minItems: 2

  clock-names:
    items:
      - const: bi_tcxo
      - const: sleep_clk
      - const: pcie_0_pipe_clk # Optional clock
      - const: pcie_1_pipe_clk # Optional clock
      - const: pcie_1_phy_aux_clk # Optional clock
      - const: ufs_phy_rx_symbol_0_clk # Optional clock
      - const: ufs_phy_rx_symbol_1_clk # Optional clock
      - const: ufs_phy_tx_symbol_0_clk # Optional clock
      - const: usb3_phy_wrapper_gcc_usb30_pipe_clk # Optional clock
    minItems: 2

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,gcc-sm8450";
      reg = <0x00100000 0x001f4200>;
      clocks = <&rpmhcc RPMH_CXO_CLK>, <&sleep_clk>;
      clock-names = "bi_tcxo", "sleep_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };

...
