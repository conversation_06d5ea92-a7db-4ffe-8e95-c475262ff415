# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/atmel,at91sam9x5-sckc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel Slow Clock Controller (SCKC)

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - atmel,at91sam9x5-sckc
          - atmel,sama5d3-sckc
          - atmel,sama5d4-sckc
          - microchip,sam9x60-sckc
      - items:
          - const: microchip,sama7g5-sckc
          - const: microchip,sam9x60-sckc

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  "#clock-cells":
    enum: [0, 1]

  atmel,osc-bypass:
    type: boolean
    description: set when a clock signal is directly provided on XIN

required:
  - compatible
  - reg
  - clocks
  - "#clock-cells"

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - microchip,sam9x60-sckc
    then:
      properties:
        "#clock-cells":
          const: 1
    else:
      properties:
        "#clock-cells":
          const: 0

additionalProperties: false

examples:
  - |
    clk32k: clock-controller@fffffe50 {
        compatible = "microchip,sam9x60-sckc";
        reg = <0xfffffe50 0x4>;
        clocks = <&slow_xtal>;
        #clock-cells = <1>;
    };

...
