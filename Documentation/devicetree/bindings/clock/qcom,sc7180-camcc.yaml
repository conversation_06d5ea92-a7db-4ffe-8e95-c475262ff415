# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sc7180-camcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Camera Clock & Reset Controller on SC7180

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm camera clock control module provides the clocks, resets and power
  domains on SC7180.

  See also:: include/dt-bindings/clock/qcom,camcc-sc7180.h

properties:
  compatible:
    const: qcom,sc7180-camcc

  clocks:
    items:
      - description: Board XO source
      - description: Camera_ahb clock from GCC
      - description: Camera XO clock from GCC

  clock-names:
    items:
      - const: bi_tcxo
      - const: iface
      - const: xo

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sc7180.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@ad00000 {
      compatible = "qcom,sc7180-camcc";
      reg = <0x0ad00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&gcc GCC_CAMERA_AHB_CLK>,
               <&gcc GCC_CAMERA_XO_CLK>;
      clock-names = "bi_tcxo", "iface", "xo";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
