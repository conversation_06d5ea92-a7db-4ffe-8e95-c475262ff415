# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,ipq9574-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on IPQ9574

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on IPQ9574

  See also::
    include/dt-bindings/clock/qcom,ipq9574-gcc.h
    include/dt-bindings/reset/qcom,ipq9574-gcc.h

properties:
  compatible:
    const: qcom,ipq9574-gcc

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: Bias PLL ubi clock source
      - description: PCIE30 PHY0 pipe clock source
      - description: PCIE30 PHY1 pipe clock source
      - description: PCIE30 PHY2 pipe clock source
      - description: PCIE30 PHY3 pipe clock source
      - description: USB3 PHY pipe clock source

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@1800000 {
      compatible = "qcom,ipq9574-gcc";
      reg = <0x01800000 0x80000>;
      clocks = <&xo_board_clk>,
               <&sleep_clk>,
               <&bias_pll_ubi_nc_clk>,
               <&pcie30_phy0_pipe_clk>,
               <&pcie30_phy1_pipe_clk>,
               <&pcie30_phy2_pipe_clk>,
               <&pcie30_phy3_pipe_clk>,
               <&usb3phy_0_cc_pipe_clk>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
