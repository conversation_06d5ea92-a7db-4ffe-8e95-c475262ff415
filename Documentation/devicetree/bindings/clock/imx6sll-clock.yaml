# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/imx6sll-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX6 SLL Clock Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: fsl,imx6sll-ccm

  reg:
    maxItems: 1

  interrupts:
    description: CCM provides 2 interrupt requests, request 1 is to generate
      interrupt for frequency or mux change, request 2 is to generate
      interrupt for oscillator read or PLL lock.
    items:
      - description: CCM interrupt request 1
      - description: CCM interrupt request 2

  '#clock-cells':
    const: 1

  clocks:
    items:
      - description: 32k osc
      - description: 24m osc
      - description: ipp_di0 clock input
      - description: ipp_di1 clock input

  clock-names:
    items:
      - const: ckil
      - const: osc
      - const: ipp_di0
      - const: ipp_di1

required:
  - compatible
  - reg
  - interrupts
  - '#clock-cells'
  - clocks
  - clock-names

additionalProperties: false

examples:
  # Clock Control Module node:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    clock-controller@20c4000 {
        compatible = "fsl,imx6sll-ccm";
        reg = <0x020c4000 0x4000>;
        interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
        #clock-cells = <1>;
        clocks = <&ckil>, <&osc>, <&ipp_di0>, <&ipp_di1>;
        clock-names = "ckil", "osc", "ipp_di0", "ipp_di1";
    };
