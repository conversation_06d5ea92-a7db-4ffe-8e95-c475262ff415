# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sc8280xp-lpasscc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm LPASS Core & Audio Clock Controller on SC8280XP

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm LPASS core and audio clock control module provides the clocks,
  and reset on SC8280XP.

  See also::
    include/dt-bindings/clock/qcom,lpasscc-sc8280xp.h

properties:
  compatible:
    enum:
      - qcom,sc8280xp-lpassaudiocc
      - qcom,sc8280xp-lpasscc

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - reg
  - '#clock-cells'
  - '#reset-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,sc8280xp-lpasscc.h>
    lpass_audiocc: clock-controller@32a9000 {
        compatible = "qcom,sc8280xp-lpassaudiocc";
        reg = <0x032a9000 0x1000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
    };

  - |
    #include <dt-bindings/clock/qcom,sc8280xp-lpasscc.h>
    lpasscc: clock-controller@33e0000 {
        compatible = "qcom,sc8280xp-lpasscc";
        reg = <0x033e0000 0x12000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
    };
...
