# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/imx7d-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX7 Dual Clock Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<PERSON>.<PERSON>@nxp.com>

description: |
  The clock consumer should specify the desired clock by having the clock
  ID in its "clocks" phandle cell. See include/dt-bindings/clock/imx7d-clock.h
  for the full list of i.MX7 Dual clock IDs.

properties:
  compatible:
    const: fsl,imx7d-ccm

  reg:
    maxItems: 1

  interrupts:
    items:
      - description: CCM interrupt request 1
      - description: CCM interrupt request 2

  '#clock-cells':
    const: 1

  clocks:
    items:
      - description: 32k osc
      - description: 24m osc

  clock-names:
    items:
      - const: ckil
      - const: osc

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    clock-controller@30380000 {
        compatible = "fsl,imx7d-ccm";
        reg = <0x30380000 0x10000>;
        interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
        #clock-cells = <1>;
        clocks = <&ckil>, <&osc>;
        clock-names = "ckil", "osc";
    };
