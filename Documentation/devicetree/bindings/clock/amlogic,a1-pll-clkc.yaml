# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/amlogic,a1-pll-clkc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic A1 PLL Clock Control Unit

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: amlogic,a1-pll-clkc

  '#clock-cells':
    const: 1

  reg:
    maxItems: 1

  clocks:
    items:
      - description: input fixpll_in
      - description: input hifipll_in

  clock-names:
    items:
      - const: fixpll_in
      - const: hifipll_in

required:
  - compatible
  - '#clock-cells'
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/amlogic,a1-peripherals-clkc.h>
    apb {
        #address-cells = <2>;
        #size-cells = <2>;

        clock-controller@7c80 {
            compatible = "amlogic,a1-pll-clkc";
            reg = <0 0x7c80 0 0x18c>;
            #clock-cells = <1>;
            clocks = <&clkc_periphs CLKID_FIXPLL_IN>,
                     <&clkc_periphs CLKID_HIFIPLL_IN>;
            clock-names = "fixpll_in", "hifipll_in";
        };
    };
