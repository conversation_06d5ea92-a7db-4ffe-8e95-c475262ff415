# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sc7180-dispcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller on SC7180

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm display clock control module provides the clocks, resets and power
  domains on SC7180.

  See also:: include/dt-bindings/clock/qcom,dispcc-sc7180.h

properties:
  compatible:
    const: qcom,sc7180-dispcc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 source from GCC
      - description: Byte clock from DSI PHY
      - description: Pixel clock from DSI PHY
      - description: Link clock from DP PHY
      - description: VCO DIV clock from DP PHY

  clock-names:
    items:
      - const: bi_tcxo
      - const: gcc_disp_gpll0_clk_src
      - const: dsi0_phy_pll_out_byteclk
      - const: dsi0_phy_pll_out_dsiclk
      - const: dp_phy_pll_link_clk
      - const: dp_phy_pll_vco_div_clk

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sc7180.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@af00000 {
      compatible = "qcom,sc7180-dispcc";
      reg = <0x0af00000 0x200000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&gcc GCC_DISP_GPLL0_CLK_SRC>,
               <&dsi_phy 0>,
               <&dsi_phy 1>,
               <&dp_phy 0>,
               <&dp_phy 1>;
      clock-names = "bi_tcxo",
                    "gcc_disp_gpll0_clk_src",
                    "dsi0_phy_pll_out_byteclk",
                    "dsi0_phy_pll_out_dsiclk",
                    "dp_phy_pll_link_clk",
                    "dp_phy_pll_vco_div_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
