# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm6350-camcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Camera Clock & Reset Controller on SM6350

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm camera clock control module provides the clocks, resets and  power
  domains on SM6350.

  See also:: include/dt-bindings/clock/qcom,sm6350-camcc.h

properties:
  compatible:
    const: qcom,sm6350-camcc

  clocks:
    items:
      - description: Board XO source

  reg:
    maxItems: 1

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@ad00000 {
      compatible = "qcom,sm6350-camcc";
      reg = <0x0ad00000 0x16000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
