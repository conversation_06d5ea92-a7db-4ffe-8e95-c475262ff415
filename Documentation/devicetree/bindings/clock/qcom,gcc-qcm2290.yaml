# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-qcm2290.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on QCM2290

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on QCM2290.

  See also:: include/dt-bindings/clock/qcom,gcc-qcm2290.h

properties:
  compatible:
    const: qcom,gcc-qcm2290

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: bi_tcxo
      - const: sleep_clk

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmcc.h>
    clock-controller@1400000 {
        compatible = "qcom,gcc-qcm2290";
        reg = <0x01400000 0x1f0000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
        #power-domain-cells = <1>;
        clock-names = "bi_tcxo", "sleep_clk";
        clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>, <&sleep_clk>;
    };
...
