# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm6375-dispcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller on SM6375

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm display clock control module provides the clocks, resets and power
  domains on SM6375.

  See also:: include/dt-bindings/clock/qcom,dispcc-sm6375.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    const: qcom,sm6375-dispcc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 source from GCC
      - description: Byte clock from DSI PHY
      - description: Pixel clock from DSI PHY

required:
  - compatible
  - clocks

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,sm6375-gcc.h>
    #include <dt-bindings/clock/qcom,rpmh.h>

    clock-controller@5f00000 {
      compatible = "qcom,sm6375-dispcc";
      reg = <0x05f00000 0x20000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&gcc GCC_DISP_GPLL0_CLK_SRC>,
               <&dsi_phy 0>,
               <&dsi_phy 1>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
