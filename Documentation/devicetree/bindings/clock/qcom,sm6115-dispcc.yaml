# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm6115-dispcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock Controller for SM6115

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description: |
  Qualcomm display clock control module provides the clocks and power domains
  on SM6115.

  See also:: include/dt-bindings/clock/qcom,sm6115-dispcc.h

properties:
  compatible:
    enum:
      - qcom,sm6115-dispcc

  clocks:
    items:
      - description: Board XO source
      - description: Board sleep clock
      - description: Byte clock from DSI PHY0
      - description: Pixel clock from DSI PHY0
      - description: GPLL0 DISP DIV clock from GCC

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmcc.h>
    #include <dt-bindings/clock/qcom,gcc-sm6115.h>
    clock-controller@5f00000 {
      compatible = "qcom,sm6115-dispcc";
      reg = <0x5f00000 0x20000>;
      clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
               <&sleep_clk>,
               <&dsi0_phy 0>,
               <&dsi0_phy 1>,
               <&gcc GCC_DISP_GPLL0_DIV_CLK_SRC>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
