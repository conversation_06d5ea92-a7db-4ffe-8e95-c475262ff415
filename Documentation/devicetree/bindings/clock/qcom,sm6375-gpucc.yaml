# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm6375-gpucc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Graphics Clock & Reset Controller on SM6375

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm graphics clock control module provides clocks, resets and power
  domains on Qualcomm SoCs.

  See also:: include/dt-bindings/clock/qcom,sm6375-gpucc.h

properties:
  compatible:
    enum:
      - qcom,sm6375-gpucc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 main branch source
      - description: GPLL0 div branch source
      - description: SNoC DVM GFX source

  power-domains:
    description:
      A phandle and PM domain specifier for the VDD_GX power rail
    maxItems: 1

  required-opps:
    description:
      A phandle to an OPP node describing required VDD_GX performance point.
    maxItems: 1

required:
  - compatible
  - clocks
  - power-domains
  - required-opps

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,sm6375-gcc.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        clock-controller@5990000 {
            compatible = "qcom,sm6375-gpucc";
            reg = <0 0x05990000 0 0x9000>;
            clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
                     <&gcc GCC_GPU_GPLL0_CLK_SRC>,
                     <&gcc GCC_GPU_GPLL0_DIV_CLK_SRC>,
                     <&gcc GCC_GPU_SNOC_DVM_GFX_CLK>;
            power-domains = <&rpmpd SM6375_VDDGX>;
            required-opps = <&rpmpd_opp_low_svs>;
            #clock-cells = <1>;
            #reset-cells = <1>;
            #power-domain-cells = <1>;
        };
    };
...
