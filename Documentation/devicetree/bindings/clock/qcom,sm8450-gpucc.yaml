# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8450-gpucc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Graphics Clock & Reset Controller on SM8450

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm graphics clock control module provides the clocks, resets and power
  domains on Qualcomm SoCs.

  See also::
    include/dt-bindings/clock/qcom,sm8450-gpucc.h
    include/dt-bindings/clock/qcom,sm8550-gpucc.h
    include/dt-bindings/reset/qcom,sm8450-gpucc.h

properties:
  compatible:
    enum:
      - qcom,sm8450-gpucc
      - qcom,sm8550-gpucc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 main branch source
      - description: GPLL0 div branch source

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sm8450.h>
    #include <dt-bindings/clock/qcom,rpmh.h>

    soc {
        #address-cells = <2>;
        #size-cells = <2>;

        clock-controller@3d90000 {
            compatible = "qcom,sm8450-gpucc";
            reg = <0 0x03d90000 0 0xa000>;
            clocks = <&rpmhcc RPMH_CXO_CLK>,
                     <&gcc GCC_GPU_GPLL0_CLK_SRC>,
                     <&gcc GCC_GPU_GPLL0_DIV_CLK_SRC>;
            #clock-cells = <1>;
            #reset-cells = <1>;
            #power-domain-cells = <1>;
        };
    };
...
