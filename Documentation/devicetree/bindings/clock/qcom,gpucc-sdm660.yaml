# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gpucc-sdm660.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Graphics Clock & Reset Controller on SDM630 and SDM660

maintainers:
  - AngeloGioacchin<PERSON> <<EMAIL>>

description: |
  Qualcomm graphics clock control module provides the clocks, resets and
  power domains on SDM630 and SDM660.

  See also dt-bindings/clock/qcom,gpucc-sdm660.h.

properties:
  compatible:
    enum:
      - qcom,gpucc-sdm630
      - qcom,gpucc-sdm660

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 main gpu branch
      - description: GPLL0 divider gpu branch

  clock-names:
    items:
      - const: xo
      - const: gcc_gpu_gpll0_clk
      - const: gcc_gpu_gpll0_div_clk

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm660.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>

    clock-controller@5065000 {
      compatible = "qcom,gpucc-sdm660";
      reg = <0x05065000 0x9038>;
      clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
               <&gcc GCC_GPU_GPLL0_CLK>,
               <&gcc GCC_GPU_GPLL0_DIV_CLK>;
      clock-names = "xo", "gcc_gpu_gpll0_clk",
                    "gcc_gpu_gpll0_div_clk";
      #clock-cells = <1>;
      #power-domain-cells = <1>;
      #reset-cells = <1>;
    };
...
