# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/idt,versaclock5.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IDT VersaClock 5 and 6 programmable I2C clock generators

description: |
  The IDT VersaClock 5 and VersaClock 6 are programmable I2C
  clock generators providing from 3 to 12 output clocks.

  When referencing the provided clock in the DT using phandle and clock
  specifier, the following mapping applies:

  - 5P49V5923:
    0 -- OUT0_SEL_I2CB
    1 -- OUT1
    2 -- OUT2

  - 5P49V5933:
    0 -- OUT0_SEL_I2CB
    1 -- OUT1
    2 -- OUT4

  - other parts:
    0 -- OUT0_SEL_I2CB
    1 -- OUT1
    2 -- OUT2
    3 -- OUT3
    4 -- OUT4

  The idt,shutdown and idt,output-enable-active properties control the
  SH (en_global_shutdown) and SP bits of the Primary Source and Shutdown
  Register, respectively. Their behavior is summarized by the following
  table:

  SH SP Output when the SD/OE pin is Low/High
  == == =====================================
   0  0 Active/Inactive
   0  1 Inactive/Active
   1  0 Active/Shutdown
   1  1 Inactive/Shutdown

  The case where SH and SP are both 1 is likely not very interesting.

maintainers:
  - Luca Ceresoli <<EMAIL>>

properties:
  compatible:
    enum:
      - idt,5p49v5923
      - idt,5p49v5925
      - idt,5p49v5933
      - idt,5p49v5935
      - idt,5p49v60
      - idt,5p49v6901
      - idt,5p49v6965
      - idt,5p49v6975

  reg:
    description: I2C device address
    enum: [ 0x68, 0x6a ]

  '#clock-cells':
    const: 1

  clock-names:
    minItems: 1
    maxItems: 2
    items:
      enum: [ xin, clkin ]
  clocks:
    minItems: 1
    maxItems: 2

  idt,xtal-load-femtofarads:
    minimum: 9000
    maximum: 22760
    description: Optional load capacitor for XTAL1 and XTAL2

  idt,shutdown:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1]
    description: |
      If 1, this enables the shutdown functionality: the chip will be
      shut down if the SD/OE pin is driven high. If 0, this disables the
      shutdown functionality: the chip will never be shut down based on
      the value of the SD/OE pin. This property corresponds to the SH
      bit of the Primary Source and Shutdown Register.

  idt,output-enable-active:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1]
    description: |
      If 1, this enables output when the SD/OE pin is high, and disables
      output when the SD/OE pin is low. If 0, this disables output when
      the SD/OE pin is high, and enables output when the SD/OE pin is
      low. This corresponds to the SP bit of the Primary Source and
      Shutdown Register.

patternProperties:
  "^OUT[1-4]$":
    type: object
    description:
      Description of one of the outputs (OUT1..OUT4). See "Clock1 Output
      Configuration" in the Versaclock 5/6/6E Family Register Description
      and Programming Guide.
    properties:
      idt,mode:
        description:
          The output drive mode. Values defined in dt-bindings/clock/versaclock.h
        $ref: /schemas/types.yaml#/definitions/uint32
        minimum: 0
        maximum: 6
      idt,voltage-microvolt:
        description: The output drive voltage.
        enum: [ 1800000, 2500000, 3300000 ]
      idt,slew-percent:
        description: The Slew rate control for CMOS single-ended.
        enum: [ 80, 85, 90, 100 ]
    additionalProperties: false

required:
  - compatible
  - reg
  - '#clock-cells'
  - idt,shutdown
  - idt,output-enable-active

allOf:
  - if:
      properties:
        compatible:
          enum:
            - idt,5p49v5933
            - idt,5p49v5935
            - idt,5p49v6975
    then:
      # Devices with builtin crystal + optional external input
      properties:
        clock-names:
          const: clkin
        clocks:
          maxItems: 1
    else:
      # Devices without builtin crystal
      required:
        - clock-names
        - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/versaclock.h>

    /* 25MHz reference crystal */
    ref25: ref25m {
        compatible = "fixed-clock";
        #clock-cells = <0>;
        clock-frequency = <25000000>;
    };

    i2c@0 {
        reg = <0x0 0x100>;
        #address-cells = <1>;
        #size-cells = <0>;

        /* IDT 5P49V5923 I2C clock generator */
        vc5: clock-generator@6a {
            compatible = "idt,5p49v5923";
            reg = <0x6a>;
            #clock-cells = <1>;

            /* Connect XIN input to 25MHz reference */
            clocks = <&ref25m>;
            clock-names = "xin";

            /* Set the SD/OE pin's settings */
            idt,shutdown = <0>;
            idt,output-enable-active = <0>;

            OUT1 {
                idt,mode = <VC5_CMOSD>;
                idt,voltage-microvolt = <1800000>;
                idt,slew-percent = <80>;
            };

            OUT4 {
                idt,mode = <VC5_LVDS>;
            };
        };
    };

...
