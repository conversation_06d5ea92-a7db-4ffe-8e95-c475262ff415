# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sdm660.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SDM660/SDM630/SDM636 Global Clock & Reset Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SDM630, SDM636 and SDM660

  See also::
    include/dt-bindings/clock/qcom,gcc-sdm660.h  (qcom,gcc-sdm630 and qcom,gcc-sdm660)

$ref: qcom,gcc.yaml#

properties:
  compatible:
    enum:
      - qcom,gcc-sdm630
      - qcom,gcc-sdm660

  clocks:
    items:
      - description: XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: xo
      - const: sleep_clk

  power-domains:
    maxItems: 1

required:
  - compatible

unevaluatedProperties: false

examples:
  # Example for GCC for SDM660:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
        compatible = "qcom,gcc-sdm660";
        reg = <0x00100000 0x94000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
        #power-domain-cells = <1>;

        clock-names = "xo", "sleep_clk";
        clocks = <&xo_board>,
                 <&sleep_clk>;
    };
...
