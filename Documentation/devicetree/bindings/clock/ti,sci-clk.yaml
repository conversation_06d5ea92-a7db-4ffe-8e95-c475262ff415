# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/ti,sci-clk.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI-SCI clock controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Some TI SoCs contain a system controller (like the Power Management Micro
  Controller (PMMC) on Keystone 66AK2G SoC) that are responsible for controlling
  the state of the various hardware modules present on the SoC. Communication
  between the host processor running an OS and the system controller happens
  through a protocol called TI System Control Interface (TI-SCI protocol).

  This clock controller node uses the TI SCI protocol to perform various clock
  management of various hardware modules (devices) present on the SoC. This
  node must be a child node of the associated TI-SCI system controller node.

properties:
  $nodename:
    pattern: "^clock-controller$"

  compatible:
    const: ti,k2g-sci-clk

  "#clock-cells":
    const: 2
    description:
      The two cells represent values that the TI-SCI controller defines.

      The first cell should contain the device ID.

      The second cell should contain the clock ID.

      Please see  http://processors.wiki.ti.com/index.php/TISCI for
      protocol documentation for the values to be used for different devices.

additionalProperties: false

examples:
  - |
    k3_clks: clock-controller {
        compatible = "ti,k2g-sci-clk";
        #clock-cells = <2>;
    };
