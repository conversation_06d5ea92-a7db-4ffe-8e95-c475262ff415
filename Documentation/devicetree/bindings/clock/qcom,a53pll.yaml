# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,a53pll.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm A53 PLL clock

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description:
  The A53 PLL on few Qualcomm platforms is the main CPU PLL used used for
  frequencies above 1GHz.

properties:
  compatible:
    enum:
      - qcom,ipq5332-a53pll
      - qcom,ipq6018-a53pll
      - qcom,ipq8074-a53pll
      - qcom,ipq9574-a73pll
      - qcom,msm8916-a53pll
      - qcom,msm8939-a53pll

  reg:
    maxItems: 1

  '#clock-cells':
    const: 0

  clocks:
    items:
      - description: board XO clock

  clock-names:
    items:
      - const: xo

  operating-points-v2: true

required:
  - compatible
  - reg
  - '#clock-cells'

additionalProperties: false

examples:
  # Example 1 - A53 PLL found on MSM8916 devices
  - |
    a53pll: clock@b016000 {
        compatible = "qcom,msm8916-a53pll";
        reg = <0xb016000 0x40>;
        #clock-cells = <0>;
    };
  # Example 2 - A53 PLL found on IPQ6018 devices
  - |
    a53pll_ipq: clock-controller@b116000 {
        compatible = "qcom,ipq6018-a53pll";
        reg = <0x0b116000 0x40>;
        #clock-cells = <0>;
        clocks = <&xo>;
        clock-names = "xo";
    };
