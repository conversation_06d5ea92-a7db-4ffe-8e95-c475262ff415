# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-qcs404.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on QCS404

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on QCS404.

  See also:: include/dt-bindings/clock/qcom,gcc-qcs404.h

properties:
  compatible:
    const: qcom,gcc-qcs404

  clocks:
    items:
      - description: XO source
      - description: Sleep clock source
      - description: PCIe 0 PIPE clock (optional)
      - description: DSI phy instance 0 dsi clock
      - description: DSI phy instance 0 byte clock
      - description: HDMI phy PLL clock

  clock-names:
    items:
      - const: cxo
      - const: sleep_clk
      - const: pcie_0_pipe_clk_src
      - const: dsi0pll
      - const: dsi0pllbyte
      - const: hdmi_pll

required:
  - compatible

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@1800000 {
      compatible = "qcom,gcc-qcs404";
      reg = <0x01800000 0x80000>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
