# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,ipq5018-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on IPQ5018

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on IPQ5018

  See also::
    include/dt-bindings/clock/qcom,ipq5018-gcc.h
    include/dt-bindings/reset/qcom,ipq5018-gcc.h

properties:
  compatible:
    const: qcom,gcc-ipq5018

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: PCIE20 PHY0 pipe clock source
      - description: PCIE20 PHY1 pipe clock source
      - description: USB3 PHY pipe clock source
      - description: GEPHY RX clock source
      - description: GEPHY TX clock source
      - description: UNIPHY RX clock source
      - description: UNIPHY TX clk source

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@1800000 {
      compatible = "qcom,gcc-ipq5018";
      reg = <0x01800000 0x80000>;
      clocks = <&xo_board_clk>,
               <&sleep_clk>,
               <&pcie20_phy0_pipe_clk>,
               <&pcie20_phy1_pipe_clk>,
               <&usb3_phy0_pipe_clk>,
               <&gephy_rx_clk>,
               <&gephy_tx_clk>,
               <&uniphy_rx_clk>,
               <&uniphy_tx_clk>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
