# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sdm845.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SDM670 and SDM845

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SDM670 and SDM845

  See also:: include/dt-bindings/clock/qcom,gcc-sdm845.h

properties:
  compatible:
    enum:
      - qcom,gcc-sdm670
      - qcom,gcc-sdm845

  clocks:
    minItems: 3
    maxItems: 5

  clock-names:
    minItems: 3
    maxItems: 5

  power-domains:
    maxItems: 1

required:
  - compatible

allOf:
  - $ref: qcom,gcc.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: qcom,gcc-sdm670
    then:
      properties:
        clocks:
          items:
            - description: Board XO source
            - description: Board active XO source
            - description: Sleep clock source
        clock-names:
          items:
            - const: bi_tcxo
            - const: bi_tcxo_ao
            - const: sleep_clk

  - if:
      properties:
        compatible:
          contains:
            const: qcom,gcc-sdm845
    then:
      properties:
        clocks:
          items:
            - description: Board XO source
            - description: Board active XO source
            - description: Sleep clock source
            - description: PCIE 0 Pipe clock source
            - description: PCIE 1 Pipe clock source
        clock-names:
          items:
            - const: bi_tcxo
            - const: bi_tcxo_ao
            - const: sleep_clk
            - const: pcie_0_pipe_clk
            - const: pcie_1_pipe_clk

unevaluatedProperties: false

examples:
  # Example for GCC for SDM845:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,gcc-sdm845";
      reg = <0x100000 0x1f0000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>,
               <&pcie0_lane>,
               <&pcie1_lane>;
      clock-names = "bi_tcxo", "bi_tcxo_ao", "sleep_clk", "pcie_0_pipe_clk", "pcie_1_pipe_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
