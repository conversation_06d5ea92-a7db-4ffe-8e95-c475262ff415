# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/renesas,9series.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas 9-series I2C PCIe clock generators

description: |
  The Renesas 9-series are I2C PCIe clock generators providing
  from 1 to 20 output clocks.

  When referencing the provided clock in the DT using phandle
  and clock specifier, the following mapping applies:

  - 9FGV0241:
    0 -- DIF0
    1 -- DIF1
  - 9FGV0441:
    0 -- DIF0
    1 -- DIF1
    2 -- DIF2
    3 -- DIF3

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - renesas,9fgv0241
      - renesas,9fgv0441

  reg:
    description: I2C device address
    enum: [ 0x68, 0x6a ]

  '#clock-cells':
    const: 1

  clocks:
    items:
      - description: XTal input clock

  renesas,out-amplitude-microvolt:
    enum: [ 600000, 700000, 800000, 900000 ]
    description: Output clock signal amplitude

  renesas,out-spread-spectrum:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [ 100000, 99750, 99500 ]
    description: Output clock down spread in pcm (1/1000 of percent)

patternProperties:
  "^DIF[0-19]$":
    type: object
    description:
      Description of one of the outputs (DIF0..DIF19).

    properties:
      renesas,slew-rate:
        $ref: /schemas/types.yaml#/definitions/uint32
        enum: [ 2000000, 3000000 ]
        description: Output clock slew rate select in V/ns

    additionalProperties: false

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    /* 25MHz reference crystal */
    ref25: ref25m {
        compatible = "fixed-clock";
        #clock-cells = <0>;
        clock-frequency = <25000000>;
    };

    i2c@0 {
        reg = <0x0 0x100>;
        #address-cells = <1>;
        #size-cells = <0>;

        rs9: clock-generator@6a {
            compatible = "renesas,9fgv0241";
            reg = <0x6a>;
            #clock-cells = <1>;

            clocks = <&ref25m>;

            DIF0 {
                renesas,slew-rate = <3000000>;
            };
        };
    };

...
