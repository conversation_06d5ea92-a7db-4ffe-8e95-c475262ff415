# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/samsung,exynos850-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Samsung Exynos850 SoC clock controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON>ylwes<PERSON> Nawrocki <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Exynos850 clock controller is comprised of several CMU units, generating
  clocks for different domains. Those CMU units are modeled as separate device
  tree nodes, and might depend on each other. Root clocks in that clock tree are
  two external clocks:: OSCCLK (26 MHz) and RTCCLK (32768 Hz). Those external
  clocks must be defined as fixed-rate clocks in dts.

  CMU_TOP is a top-level CMU, where all base clocks are prepared using PLLs and
  dividers; all other leaf clocks (other CMUs) are usually derived from CMU_TOP.

  Each clock is assigned an identifier and client nodes can use this identifier
  to specify the clock which they consume. All clocks available for usage
  in clock consumer nodes are defined as preprocessor macros in
  'dt-bindings/clock/exynos850.h' header.

properties:
  compatible:
    enum:
      - samsung,exynos850-cmu-top
      - samsung,exynos850-cmu-apm
      - samsung,exynos850-cmu-aud
      - samsung,exynos850-cmu-cmgp
      - samsung,exynos850-cmu-core
      - samsung,exynos850-cmu-dpu
      - samsung,exynos850-cmu-g3d
      - samsung,exynos850-cmu-hsi
      - samsung,exynos850-cmu-is
      - samsung,exynos850-cmu-mfcmscl
      - samsung,exynos850-cmu-peri

  clocks:
    minItems: 1
    maxItems: 5

  clock-names:
    minItems: 1
    maxItems: 5

  "#clock-cells":
    const: 1

  reg:
    maxItems: 1

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-top

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)

        clock-names:
          items:
            - const: oscclk

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-apm

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: CMU_APM bus clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_clkcmu_apm_bus

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-aud

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: AUD clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_aud

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-cmgp

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: CMU_CMGP bus clock (from CMU_APM)

        clock-names:
          items:
            - const: oscclk
            - const: gout_clkcmu_cmgp_bus

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-core

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: CMU_CORE bus clock (from CMU_TOP)
            - description: CCI clock (from CMU_TOP)
            - description: eMMC clock (from CMU_TOP)
            - description: SSS clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_core_bus
            - const: dout_core_cci
            - const: dout_core_mmc_embd
            - const: dout_core_sss

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-dpu

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: DPU clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_dpu

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-g3d

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: G3D clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_g3d_switch

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-hsi

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: External RTC clock (32768 Hz)
            - description: CMU_HSI bus clock (from CMU_TOP)
            - description: SD card clock (from CMU_TOP)
            - description: USB 2.0 DRD clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: rtcclk
            - const: dout_hsi_bus
            - const: dout_hsi_mmc_card
            - const: dout_hsi_usb20drd

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-is

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: CMU_IS bus clock (from CMU_TOP)
            - description: Image Texture Processing core clock (from CMU_TOP)
            - description: Visual Recognition Accelerator clock (from CMU_TOP)
            - description: Geometric Distortion Correction clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_is_bus
            - const: dout_is_itp
            - const: dout_is_vra
            - const: dout_is_gdc

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-mfcmscl

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: Multi-Format Codec clock (from CMU_TOP)
            - description: Memory to Memory Scaler clock (from CMU_TOP)
            - description: Multi-Channel Scaler clock (from CMU_TOP)
            - description: JPEG codec clock (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_mfcmscl_mfc
            - const: dout_mfcmscl_m2m
            - const: dout_mfcmscl_mcsc
            - const: dout_mfcmscl_jpeg

  - if:
      properties:
        compatible:
          contains:
            const: samsung,exynos850-cmu-peri

    then:
      properties:
        clocks:
          items:
            - description: External reference clock (26 MHz)
            - description: CMU_PERI bus clock (from CMU_TOP)
            - description: UART clock (from CMU_TOP)
            - description: Parent clock for HSI2C and SPI (from CMU_TOP)

        clock-names:
          items:
            - const: oscclk
            - const: dout_peri_bus
            - const: dout_peri_uart
            - const: dout_peri_ip

required:
  - compatible
  - "#clock-cells"
  - clocks
  - clock-names
  - reg

additionalProperties: false

examples:
  # Clock controller node for CMU_PERI
  - |
    #include <dt-bindings/clock/exynos850.h>

    cmu_peri: clock-controller@10030000 {
        compatible = "samsung,exynos850-cmu-peri";
        reg = <0x10030000 0x8000>;
        #clock-cells = <1>;

        clocks = <&oscclk>, <&cmu_top CLK_DOUT_PERI_BUS>,
                 <&cmu_top CLK_DOUT_PERI_UART>,
                 <&cmu_top CLK_DOUT_PERI_IP>;
        clock-names = "oscclk", "dout_peri_bus",
                      "dout_peri_uart", "dout_peri_ip";
    };

...
