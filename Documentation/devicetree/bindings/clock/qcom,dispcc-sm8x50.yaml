# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,dispcc-sm8x50.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller on SM8150/SM8250/SM8350

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm display clock control module provides the clocks, resets and power
  domains on SM8150/SM8250/SM8350.

  See also::
    include/dt-bindings/clock/qcom,dispcc-sm8150.h
    include/dt-bindings/clock/qcom,dispcc-sm8250.h
    include/dt-bindings/clock/qcom,dispcc-sm8350.h

properties:
  compatible:
    enum:
      - qcom,sc8180x-dispcc
      - qcom,sm8150-dispcc
      - qcom,sm8250-dispcc
      - qcom,sm8350-dispcc

  clocks:
    items:
      - description: Board XO source
      - description: Byte clock from DSI PHY0
      - description: Pixel clock from DSI PHY0
      - description: Byte clock from DSI PHY1
      - description: Pixel clock from DSI PHY1
      - description: Link clock from DP PHY
      - description: VCO DIV clock from DP PHY

  clock-names:
    items:
      - const: bi_tcxo
      - const: dsi0_phy_pll_out_byteclk
      - const: dsi0_phy_pll_out_dsiclk
      - const: dsi1_phy_pll_out_byteclk
      - const: dsi1_phy_pll_out_dsiclk
      - const: dp_phy_pll_link_clk
      - const: dp_phy_pll_vco_div_clk

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

  power-domains:
    description:
      A phandle and PM domain specifier for the MMCX power domain.
    maxItems: 1

  required-opps:
    description:
      A phandle to an OPP node describing required MMCX performance point.
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>
    clock-controller@af00000 {
      compatible = "qcom,sm8250-dispcc";
      reg = <0x0af00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&dsi0_phy 0>,
               <&dsi0_phy 1>,
               <&dsi1_phy 0>,
               <&dsi1_phy 1>,
               <&dp_phy 0>,
               <&dp_phy 1>;
      clock-names = "bi_tcxo",
                    "dsi0_phy_pll_out_byteclk",
                    "dsi0_phy_pll_out_dsiclk",
                    "dsi1_phy_pll_out_byteclk",
                    "dsi1_phy_pll_out_dsiclk",
                    "dp_phy_pll_link_clk",
                    "dp_phy_pll_vco_div_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      power-domains = <&rpmhpd RPMHPD_MMCX>;
      required-opps = <&rpmhpd_opp_low_svs>;
    };
...
