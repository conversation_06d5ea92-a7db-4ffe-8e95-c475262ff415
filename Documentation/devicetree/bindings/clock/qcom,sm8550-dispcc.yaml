# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8550-dispcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller for SM8550

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm display clock control module provides the clocks, resets and power
  domains on SM8550.

  See also:: include/dt-bindings/clock/qcom,sm8550-dispcc.h

properties:
  compatible:
    enum:
      - qcom,sm8550-dispcc

  clocks:
    items:
      - description: Board XO source
      - description: Board Always On XO source
      - description: Display's AHB clock
      - description: sleep clock
      - description: Byte clock from DSI PHY0
      - description: Pixel clock from DSI PHY0
      - description: Byte clock from DSI PHY1
      - description: Pixel clock from DSI PHY1
      - description: Link clock from DP PHY0
      - description: VCO DIV clock from DP PHY0
      - description: Link clock from DP PHY1
      - description: VCO DIV clock from DP PHY1
      - description: Link clock from DP PHY2
      - description: VCO DIV clock from DP PHY2
      - description: Link clock from DP PHY3
      - description: VCO DIV clock from DP PHY3

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

  power-domains:
    description:
      A phandle and PM domain specifier for the MMCX power domain.
    maxItems: 1

  required-opps:
    description:
      A phandle to an OPP node describing required MMCX performance point.
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,sm8550-gcc.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>
    clock-controller@af00000 {
      compatible = "qcom,sm8550-dispcc";
      reg = <0x0af00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&gcc GCC_DISP_AHB_CLK>,
               <&sleep_clk>,
               <&dsi0_phy 0>,
               <&dsi0_phy 1>,
               <&dsi1_phy 0>,
               <&dsi1_phy 1>,
               <&dp0_phy 0>,
               <&dp0_phy 1>,
               <&dp1_phy 0>,
               <&dp1_phy 1>,
               <&dp2_phy 0>,
               <&dp2_phy 1>,
               <&dp3_phy 0>,
               <&dp3_phy 1>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      power-domains = <&rpmhpd RPMHPD_MMCX>;
      required-opps = <&rpmhpd_opp_low_svs>;
    };
...
