# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8909.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8909, MSM8917 and QM215

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8909, MSM8917 or QM215.

  See also::
    include/dt-bindings/clock/qcom,gcc-msm8909.h
    include/dt-bindings/clock/qcom,gcc-msm8917.h

properties:
  compatible:
    enum:
      - qcom,gcc-msm8909
      - qcom,gcc-msm8917
      - qcom,gcc-qm215

  clocks:
    items:
      - description: XO source
      - description: Sleep clock source
      - description: DSI phy instance 0 dsi clock
      - description: DSI phy instance 0 byte clock

  clock-names:
    items:
      - const: xo
      - const: sleep_clk
      - const: dsi0pll
      - const: dsi0pllbyte

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    gcc: clock-controller@1800000 {
      compatible = "qcom,gcc-msm8909";
      reg = <0x01800000 0x80000>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      clocks = <&xo_board>, <&sleep_clk>, <&dsi0_phy 1>, <&dsi0_phy 0>;
      clock-names = "xo", "sleep_clk", "dsi0pll", "dsi0pllbyte";
    };
...
