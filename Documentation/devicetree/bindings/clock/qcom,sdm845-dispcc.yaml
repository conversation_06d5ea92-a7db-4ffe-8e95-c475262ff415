# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sdm845-dispcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller on SDM845

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm display clock control module provides the clocks, resets and power
  domains on SDM845.

  See also:: include/dt-bindings/clock/qcom,dispcc-sdm845.h

properties:
  compatible:
    const: qcom,sdm845-dispcc

  # NOTE: sdm845.dtsi existed for quite some time and specified no clocks.
  # The code had to use hardcoded mechanisms to find the input clocks.
  # New dts files should have these clocks.
  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 source from GCC
      - description: GPLL0 div source from GCC
      - description: Byte clock from DSI PHY0
      - description: Pixel clock from DSI PHY0
      - description: Byte clock from DSI PHY1
      - description: Pixel clock from DSI PHY1
      - description: Link clock from DP PHY
      - description: VCO DIV clock from DP PHY

  clock-names:
    items:
      - const: bi_tcxo
      - const: gcc_disp_gpll0_clk_src
      - const: gcc_disp_gpll0_div_clk_src
      - const: dsi0_phy_pll_out_byteclk
      - const: dsi0_phy_pll_out_dsiclk
      - const: dsi1_phy_pll_out_byteclk
      - const: dsi1_phy_pll_out_dsiclk
      - const: dp_link_clk_divsel_ten
      - const: dp_vco_divided_clk_src_mux

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@af00000 {
      compatible = "qcom,sdm845-dispcc";
      reg = <0x0af00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&gcc GCC_DISP_GPLL0_CLK_SRC>,
               <&gcc GCC_DISP_GPLL0_DIV_CLK_SRC>,
               <&dsi0_phy 0>,
               <&dsi0_phy 1>,
               <&dsi1_phy 0>,
               <&dsi1_phy 1>,
               <&dp_phy 0>,
               <&dp_phy 1>;
      clock-names = "bi_tcxo",
                    "gcc_disp_gpll0_clk_src",
                    "gcc_disp_gpll0_div_clk_src",
                    "dsi0_phy_pll_out_byteclk",
                    "dsi0_phy_pll_out_dsiclk",
                    "dsi1_phy_pll_out_byteclk",
                    "dsi1_phy_pll_out_dsiclk",
                    "dp_link_clk_divsel_ten",
                    "dp_vco_divided_clk_src_mux";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
