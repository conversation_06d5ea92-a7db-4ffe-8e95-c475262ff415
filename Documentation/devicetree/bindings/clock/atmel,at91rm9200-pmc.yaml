# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/atmel,at91rm9200-pmc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel Power Management Controller (PMC)

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The power management controller optimizes power consumption by controlling all
  system and user peripheral clocks. The PMC enables/disables the clock inputs
  to many of the peripherals and to the processor.

properties:
  compatible:
    oneOf:
      - items:
          - const: atmel,at91sam9g20-pmc
          - const: atmel,at91sam9260-pmc
          - const: syscon
      - items:
          - enum:
              - atmel,at91sam9g15-pmc
              - atmel,at91sam9g25-pmc
              - atmel,at91sam9g35-pmc
              - atmel,at91sam9x25-pmc
              - atmel,at91sam9x35-pmc
          - const: atmel,at91sam9x5-pmc
          - const: syscon
      - items:
          - enum:
              - atmel,at91rm9200-pmc
              - atmel,at91sam9260-pmc
              - atmel,at91sam9g45-pmc
              - atmel,at91sam9n12-pmc
              - atmel,at91sam9rl-pmc
              - atmel,at91sam9x5-pmc
              - atmel,sama5d2-pmc
              - atmel,sama5d3-pmc
              - atmel,sama5d4-pmc
              - microchip,sam9x60-pmc
              - microchip,sama7g5-pmc
          - const: syscon

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  "#clock-cells":
    description: |
      - 1st cell is the clock type, one of PMC_TYPE_CORE, PMC_TYPE_SYSTEM,
        PMC_TYPE_PERIPHERAL, PMC_TYPE_GCK, PMC_TYPE_PROGRAMMABLE (as defined
        in <dt-bindings/clock/at91.h>)
      - 2nd cell is the clock identifier as defined in <dt-bindings/clock/at91.h
        (for core clocks) or as defined in datasheet (for system, peripheral,
        gck and programmable clocks).
    const: 2

  clocks:
    minItems: 2
    maxItems: 3

  clock-names:
    minItems: 2
    maxItems: 3

  atmel,osc-bypass:
    description: set when a clock signal is directly provided on XIN
    type: boolean

required:
  - compatible
  - reg
  - interrupts
  - "#clock-cells"
  - clocks
  - clock-names

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - microchip,sam9x60-pmc
              - microchip,sama7g5-pmc
    then:
      properties:
        clocks:
          minItems: 3
          maxItems: 3
        clock-names:
          items:
            - const: td_slck
            - const: md_slck
            - const: main_xtal

  - if:
      properties:
        compatible:
          contains:
            enum:
              - atmel,at91rm9200-pmc
              - atmel,at91sam9260-pmc
              - atmel,at91sam9g20-pmc
    then:
      properties:
        clocks:
          minItems: 2
          maxItems: 2
        clock-names:
          items:
            - const: slow_xtal
            - const: main_xtal

  - if:
      properties:
        compatible:
          contains:
            enum:
              - atmel,sama5d2-pmc
              - atmel,sama5d3-pmc
              - atmel,sama5d4-pmc
    then:
      properties:
        clocks:
          minItems: 2
          maxItems: 2
        clock-names:
          items:
            - const: slow_clk
            - const: main_xtal

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/irq.h>

    pmc: clock-controller@f0018000 {
        compatible = "atmel,sama5d4-pmc", "syscon";
        reg = <0xf0018000 0x120>;
        interrupts = <1 IRQ_TYPE_LEVEL_HIGH 7>;
        #clock-cells = <2>;
        clocks = <&clk32k>, <&main_xtal>;
        clock-names = "slow_clk", "main_xtal";
    };

...
