# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/mediatek,mt8188-sys-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek System Clock Controller for MT8188

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The clock architecture in MediaTek like below
  PLLs -->
          dividers -->
                      muxes
                           -->
                              clock gate

  The apmixedsys provides most of PLLs which generated from SoC 26m.
  The topckgen provides dividers and muxes which provide the clock source to other IP blocks.
  The infracfg_ao provides clock gate in peripheral and infrastructure IP blocks.
  The mcusys provides mux control to select the clock source in AP MCU.
  The device nodes also provide the system control capacity for configuration.

properties:
  compatible:
    items:
      - enum:
          - mediatek,mt8188-apmixedsys
          - mediatek,mt8188-infracfg-ao
          - mediatek,mt8188-pericfg-ao
          - mediatek,mt8188-topckgen
      - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    clock-controller@10000000 {
        compatible = "mediatek,mt8188-topckgen", "syscon";
        reg = <0x10000000 0x1000>;
        #clock-cells = <1>;
    };
