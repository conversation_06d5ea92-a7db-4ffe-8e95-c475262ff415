# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,msm8998-gpucc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Graphics Clock & Reset Controller on MSM8998

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm graphics clock control module provides the clocks, resets and power
  domains on MSM8998.

  See also:: include/dt-bindings/clock/qcom,gpucc-msm8998.h

properties:
  compatible:
    const: qcom,msm8998-gpucc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 main branch source (gcc_gpu_gpll0_clk_src)

  clock-names:
    items:
      - const: xo
      - const: gpll0

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8998.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    clock-controller@5065000 {
      compatible = "qcom,msm8998-gpucc";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      reg = <0x05065000 0x9000>;
      clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>, <&gcc GPLL0_OUT_MAIN>;
      clock-names = "xo", "gpll0";
    };
...
