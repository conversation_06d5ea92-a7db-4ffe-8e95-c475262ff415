# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8976.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8976

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8976.

  See also:: include/dt-bindings/clock/qcom,gcc-msm8976.h

properties:
  compatible:
    enum:
      - qcom,gcc-msm8976
      - qcom,gcc-msm8976-v1.1

  clocks:
    items:
      - description: XO source
      - description: Always-on XO source
      - description: Pixel clock from DSI PHY0
      - description: Byte clock from DSI PHY0
      - description: Pixel clock from DSI PHY1
      - description: Byte clock from DSI PHY1

  clock-names:
    items:
      - const: xo
      - const: xo_a
      - const: dsi0pll
      - const: dsi0pllbyte
      - const: dsi1pll
      - const: dsi1pllbyte

  vdd_gfx-supply:
    description:
      Phandle to voltage regulator providing power to the GX domain.

required:
  - compatible
  - clocks
  - clock-names
  - vdd_gfx-supply

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@1800000 {
      compatible = "qcom,gcc-msm8976";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      reg = <0x1800000 0x80000>;

      clocks = <&xo_board>,
               <&xo_board>,
               <&dsi0_phy 1>,
               <&dsi0_phy 0>,
               <&dsi1_phy 1>,
               <&dsi1_phy 0>;

      clock-names = "xo",
                    "xo_a",
                    "dsi0pll",
                    "dsi0pllbyte",
                    "dsi1pll",
                    "dsi1pllbyte";

      vdd_gfx-supply = <&pm8004_s5>;
    };
...
