# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8953.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8953

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <sire<PERSON><PERSON><PERSON><EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8953.

  See also: include/dt-bindings/clock/qcom,gcc-msm8953.h

properties:
  compatible:
    const: qcom,gcc-msm8953

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: Byte clock from DSI PHY0
      - description: Pixel clock from DSI PHY0
      - description: Byte clock from DSI PHY1
      - description: Pixel clock from DSI PHY1

  clock-names:
    items:
      - const: xo
      - const: sleep
      - const: dsi0pll
      - const: dsi0pllbyte
      - const: dsi1pll
      - const: dsi1pllbyte

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmcc.h>

    clock-controller@1800000 {
        compatible = "qcom,gcc-msm8953";
        reg = <0x01800000 0x80000>;
        clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
                 <&sleep_clk>,
                 <&dsi0_phy 1>,
                 <&dsi0_phy 0>,
                 <&dsi1_phy 1>,
                 <&dsi1_phy 0>;
        clock-names = "xo",
                      "sleep",
                      "dsi0pll",
                      "dsi0pllbyte",
                      "dsi1pll",
                      "dsi1pllbyte";
        #clock-cells = <1>;
        #reset-cells = <1>;
        #power-domain-cells = <1>;
    };
