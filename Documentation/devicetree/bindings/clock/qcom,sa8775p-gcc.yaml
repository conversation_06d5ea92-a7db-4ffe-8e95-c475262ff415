# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sa8775p-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on sa8775p

maintainers:
  - <PERSON><PERSON><PERSON> <bartosz.go<PERSON>@linaro.org>

description: |
  Qualcomm global clock control module provides the clocks, resets and
  power domains on sa8775p.

  See also:: include/dt-bindings/clock/qcom,sa8775p-gcc.h

properties:
  compatible:
    const: qcom,sa8775p-gcc

  clocks:
    items:
      - description: XO reference clock
      - description: Sleep clock
      - description: UFS memory first RX symbol clock
      - description: UFS memory second RX symbol clock
      - description: UFS memory first TX symbol clock
      - description: UFS card first RX symbol clock
      - description: UFS card second RX symbol clock
      - description: UFS card first TX symbol clock
      - description: Primary USB3 PHY wrapper pipe clock
      - description: Secondary USB3 PHY wrapper pipe clock
      - description: PCIe 0 pipe clock
      - description: PCIe 1 pipe clock
      - description: PCIe PHY clock
      - description: First EMAC controller reference clock
      - description: Second EMAC controller reference clock

  protected-clocks:
    maxItems: 240

  power-domains:
    maxItems: 1

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    gcc: clock-controller@100000 {
        compatible = "qcom,sa8775p-gcc";
        reg = <0x100000 0xc7018>;
        clocks = <&rpmhcc RPMH_CXO_CLK>,
                 <&sleep_clk>,
                 <&ufs_phy_rx_symbol_0_clk>,
                 <&ufs_phy_rx_symbol_1_clk>,
                 <&ufs_phy_tx_symbol_0_clk>,
                 <&ufs_card_rx_symbol_0_clk>,
                 <&ufs_card_rx_symbol_1_clk>,
                 <&ufs_card_tx_symbol_0_clk>,
                 <&usb_0_ssphy>,
                 <&usb_1_ssphy>,
                 <&pcie_0_pipe_clk>,
                 <&pcie_1_pipe_clk>,
                 <&pcie_phy_pipe_clk>,
                 <&rxc0_ref_clk>,
                 <&rxc1_ref_clk>;
        power-domains = <&rpmhpd SA8775P_CX>;

        #clock-cells = <1>;
        #reset-cells = <1>;
        #power-domain-cells = <1>;
    };
...
