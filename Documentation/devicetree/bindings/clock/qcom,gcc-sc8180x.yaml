# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sc8180x.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SC8180x

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SC8180x.

  See also:: include/dt-bindings/clock/qcom,gcc-sc8180x.h

properties:
  compatible:
    const: qcom,gcc-sc8180x

  clocks:
    items:
      - description: Board XO source
      - description: Board active XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: bi_tcxo
      - const: bi_tcxo_ao
      - const: sleep_clk

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,gcc-sc8180x";
      reg = <0x00100000 0x1f0000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>;
      clock-names = "bi_tcxo", "bi_tcxo_ao", "sleep_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
