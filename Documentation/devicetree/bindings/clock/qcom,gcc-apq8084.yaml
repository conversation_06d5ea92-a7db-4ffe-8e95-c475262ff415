# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-apq8084.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on APQ8084

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on APQ8084.

  See also::
    include/dt-bindings/clock/qcom,gcc-apq8084.h
    include/dt-bindings/reset/qcom,gcc-apq8084.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    const: qcom,gcc-apq8084

  clocks:
    items:
      - description: XO source
      - description: Sleep clock source
      - description: UFS RX symbol 0 clock
      - description: UFS RX symbol 1 clock
      - description: UFS TX symbol 0 clock
      - description: UFS TX symbol 1 clock
      - description: SATA ASIC0 clock
      - description: SATA RX clock
      - description: PCIe PIPE clock

  clock-names:
    items:
      - const: xo
      - const: sleep_clk
      - const: ufs_rx_symbol_0_clk_src
      - const: ufs_rx_symbol_1_clk_src
      - const: ufs_tx_symbol_0_clk_src
      - const: ufs_tx_symbol_1_clk_src
      - const: sata_asic0_clk
      - const: sata_rx_clk
      - const: pcie_pipe

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    /* UFS PHY on APQ8084 is not supported (yet), so these bindings just serve an example */
    clock-controller@fc400000 {
        compatible = "qcom,gcc-apq8084";
        reg = <0xfc400000 0x4000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
        #power-domain-cells = <1>;

        clocks = <&xo_board>,
                 <&sleep_clk>,
                 <&ufsphy 0>,
                 <&ufsphy 1>,
                 <&ufsphy 2>,
                 <&ufsphy 3>,
                 <&sata 0>,
                 <&sata 1>,
                 <&pcie_phy>;
        clock-names = "xo",
                      "sleep_clk",
                      "ufs_rx_symbol_0_clk_src",
                      "ufs_rx_symbol_1_clk_src",
                      "ufs_tx_symbol_0_clk_src",
                      "ufs_tx_symbol_1_clk_src",
                      "sata_asic0_clk",
                      "sata_rx_clk",
                      "pcie_pipe";
    };
...
