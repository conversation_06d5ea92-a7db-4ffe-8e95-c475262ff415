# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gpucc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Graphics Clock & Reset Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm graphics clock control module provides the clocks, resets and power
  domains on Qualcomm SoCs.

  See also::
    include/dt-bindings/clock/qcom,gpucc-sdm845.h
    include/dt-bindings/clock/qcom,gpucc-sa8775p.h
    include/dt-bindings/clock/qcom,gpucc-sc7180.h
    include/dt-bindings/clock/qcom,gpucc-sc7280.h
    include/dt-bindings/clock/qcom,gpucc-sc8280xp.h
    include/dt-bindings/clock/qcom,gpucc-sm6350.h
    include/dt-bindings/clock/qcom,gpucc-sm8150.h
    include/dt-bindings/clock/qcom,gpucc-sm8250.h
    include/dt-bindings/clock/qcom,gpucc-sm8350.h

properties:
  compatible:
    enum:
      - qcom,sdm845-gpucc
      - qcom,sa8775p-gpucc
      - qcom,sc7180-gpucc
      - qcom,sc7280-gpucc
      - qcom,sc8180x-gpucc
      - qcom,sc8280xp-gpucc
      - qcom,sm6350-gpucc
      - qcom,sm8150-gpucc
      - qcom,sm8250-gpucc
      - qcom,sm8350-gpucc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 main branch source
      - description: GPLL0 div branch source

  clock-names:
    items:
      - const: bi_tcxo
      - const: gcc_gpu_gpll0_clk_src
      - const: gcc_gpu_gpll0_div_clk_src

  power-domains:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@5090000 {
      compatible = "qcom,sdm845-gpucc";
      reg = <0x05090000 0x9000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&gcc GCC_GPU_GPLL0_CLK_SRC>,
               <&gcc GCC_GPU_GPLL0_DIV_CLK_SRC>;
      clock-names = "bi_tcxo",
                    "gcc_gpu_gpll0_clk_src",
                    "gcc_gpu_gpll0_div_clk_src";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
