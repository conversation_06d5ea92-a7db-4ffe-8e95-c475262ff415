# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-ipq4019.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on IPQ4019

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on IPQ4019.

  See also:: include/dt-bindings/clock/qcom,gcc-ipq4019.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    const: qcom,gcc-ipq4019

  clocks:
    items:
      - description: board XO clock
      - description: sleep clock

  clock-names:
    items:
      - const: xo
      - const: sleep_clk

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    clock-controller@1800000 {
      compatible = "qcom,gcc-ipq4019";
      reg = <0x1800000 0x60000>;
      #clock-cells = <1>;
      #power-domain-cells = <1>;
      #reset-cells = <1>;
      clocks = <&xo>, <&sleep_clk>;
      clock-names = "xo", "sleep_clk";
    };
...
