# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8350-videocc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm SM8350 Video Clock & Reset Controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm video clock control module provides the clocks, resets and power
  domains on Qualcomm SoCs.

  See also::
    include/dt-bindings/clock/qcom,videocc-sm8350.h
    include/dt-bindings/reset/qcom,videocc-sm8350.h

properties:
  compatible:
    enum:
      - qcom,sc8280xp-videocc
      - qcom,sm8350-videocc

  clocks:
    items:
      - description: Board XO source
      - description: Board active XO source
      - description: Board sleep clock

  power-domains:
    description:
      A phandle and PM domain specifier for the MMCX power domain.
    maxItems: 1

  required-opps:
    description:
      A phandle to an OPP node describing required MMCX performance point.
    maxItems: 1

required:
  - compatible
  - clocks
  - power-domains
  - required-opps

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>

    clock-controller@abf0000 {
      compatible = "qcom,sm8350-videocc";
      reg = <0x0abf0000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>;
      power-domains = <&rpmhpd RPMHPD_MMCX>;
      required-opps = <&rpmhpd_opp_low_svs>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
