# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8974.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8974 (including Pro) and MSM8226
  Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8974 (all variants) and MSM8226.

  See also::
    include/dt-bindings/clock/qcom,gcc-msm8974.h (qcom,gcc-msm8226 and qcom,gcc-msm8974)
    include/dt-bindings/reset/qcom,gcc-msm8974.h (qcom,gcc-msm8226 and qcom,gcc-msm8974)

$ref: qcom,gcc.yaml#

properties:
  compatible:
    enum:
      - qcom,gcc-msm8226
      - qcom,gcc-msm8974
      - qcom,gcc-msm8974pro
      - qcom,gcc-msm8974pro-ac

  clocks:
    items:
      - description: XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: xo
      - const: sleep_clk

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@fc400000 {
        compatible = "qcom,gcc-msm8974";
        reg = <0x00100000 0x94000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
        #power-domain-cells = <1>;

        clock-names = "xo", "sleep_clk";
        clocks = <&xo_board>,
                 <&sleep_clk>;
    };
...
