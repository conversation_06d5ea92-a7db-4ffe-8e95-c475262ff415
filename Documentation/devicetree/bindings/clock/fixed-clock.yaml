# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/fixed-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Simple fixed-rate clock sources

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: fixed-clock

  "#clock-cells":
    const: 0

  clock-frequency: true

  clock-accuracy:
    description: accuracy of clock in ppb (parts per billion).
    $ref: /schemas/types.yaml#/definitions/uint32

  clock-output-names:
    maxItems: 1

required:
  - compatible
  - "#clock-cells"
  - clock-frequency

additionalProperties: false

examples:
  - |
    clock {
      compatible = "fixed-clock";
      #clock-cells = <0>;
      clock-frequency = <**********>;
      clock-accuracy = <100>;
    };
...
