# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/rockchip,rk3588-cru.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Rockchip rk3588 Family Clock and Reset Control Module

maintainers:
  - <PERSON> <zhang<PERSON>@rock-chips.com>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The RK3588 clock controller generates the clock and also implements a reset
  controller for SoC peripherals. For example it provides SCLK_UART2 and
  PCLK_UART2, as well as SRST_P_UART2 and SRST_S_UART2 for the second UART
  module.
  Each clock is assigned an identifier and client nodes can use this identifier
  to specify the clock which they consume. All available clock and reset IDs
  are defined as preprocessor macros in dt-binding headers.

properties:
  compatible:
    enum:
      - rockchip,rk3588-cru

  reg:
    maxItems: 1

  "#clock-cells":
    const: 1

  "#reset-cells":
    const: 1

  clocks:
    minItems: 2
    maxItems: 2

  clock-names:
    items:
      - const: xin24m
      - const: xin32k

  assigned-clocks: true

  assigned-clock-rates: true

  rockchip,grf:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: >
      phandle to the syscon managing the "general register files". It is used
      for GRF muxes, if missing any muxes present in the GRF will not be
      available.

required:
  - compatible
  - reg
  - "#clock-cells"
  - "#reset-cells"

additionalProperties: false

examples:
  - |
    cru: clock-controller@fd7c0000 {
      compatible = "rockchip,rk3588-cru";
      reg = <0xfd7c0000 0x5c000>;
      #clock-cells = <1>;
      #reset-cells = <1>;
    };
