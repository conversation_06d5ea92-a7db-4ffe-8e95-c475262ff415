# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller Common Properties

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Common bindings for Qualcomm global clock control module providing the
  clocks, resets and power domains.

properties:
  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

  protected-clocks:
    description:
      Protected clock specifier list as per common clock binding.

required:
  - reg
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: true

...
