# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8450-camcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Camera Clock & Reset Controller on SM8450

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm camera clock control module provides the clocks, resets and power
  domains on SM8450.

  See also:: include/dt-bindings/clock/qcom,sm8450-camcc.h

properties:
  compatible:
    const: qcom,sm8450-camcc

  clocks:
    items:
      - description: Camera AHB clock from GCC
      - description: Board XO source
      - description: Board active XO source
      - description: Sleep clock source

  power-domains:
    maxItems: 1
    description:
      A phandle and PM domain specifier for the MMCX power domain.

  required-opps:
    maxItems: 1
    description:
      A phandle to an OPP node describing required MMCX performance point.

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - power-domains
  - required-opps
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sm8450.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>
    clock-controller@ade0000 {
      compatible = "qcom,sm8450-camcc";
      reg = <0xade0000 0x20000>;
      clocks = <&gcc GCC_CAMERA_AHB_CLK>,
               <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>;
      power-domains = <&rpmhpd RPMHPD_MMCX>;
      required-opps = <&rpmhpd_opp_low_svs>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
