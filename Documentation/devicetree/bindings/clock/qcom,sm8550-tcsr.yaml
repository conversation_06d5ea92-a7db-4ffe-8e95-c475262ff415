# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8550-tcsr.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm TCSR Clock Controller on SM8550

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description: |
  Qualcomm TCSR clock control module provides the clocks, resets and
  power domains on SM8550

  See also:: include/dt-bindings/clock/qcom,sm8550-tcsr.h

properties:
  compatible:
    items:
      - const: qcom,sm8550-tcsr
      - const: syscon

  clocks:
    items:
      - description: TCXO pad clock

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - clocks

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>

    clock-controller@1fc0000 {
      compatible = "qcom,sm8550-tcsr", "syscon";
      reg = <0x1fc0000 0x30000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>;
      #clock-cells = <1>;
      #reset-cells = <1>;
    };

...
