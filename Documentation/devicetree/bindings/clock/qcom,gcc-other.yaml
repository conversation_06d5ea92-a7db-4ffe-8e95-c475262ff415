# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-other.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains.

  See also::
    include/dt-bindings/clock/qcom,gcc-ipq6018.h
    include/dt-bindings/reset/qcom,gcc-ipq6018.h
    include/dt-bindings/clock/qcom,gcc-msm8953.h
    include/dt-bindings/clock/qcom,gcc-mdm9607.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    enum:
      - qcom,gcc-ipq6018
      - qcom,gcc-mdm9607

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    clock-controller@900000 {
      compatible = "qcom,gcc-mdm9607";
      reg = <0x900000 0x4000>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
