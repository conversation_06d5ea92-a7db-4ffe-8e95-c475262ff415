# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm6375-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SM6375

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SM6375

  See also:: include/dt-bindings/clock/qcom,sm6375-gcc.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    const: qcom,sm6375-gcc

  clocks:
    items:
      - description: Board XO source
      - description: Board XO Active-Only source
      - description: Sleep clock source

required:
  - compatible
  - clocks

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmcc.h>
    clock-controller@1400000 {
      compatible = "qcom,sm6375-gcc";
      reg = <0x01400000 0x1f0000>;
      clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
               <&rpmcc RPM_SMD_XO_A_CLK_SRC>,
               <&sleep_clk>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };

...
