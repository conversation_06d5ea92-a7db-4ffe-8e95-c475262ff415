# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sc7280-camcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Camera Clock & Reset Controller on SC7280

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm camera clock control module provides the clocks, resets and
  power domains on SC7280.

  See also:: include/dt-bindings/clock/qcom,camcc-sc7280.h

properties:
  compatible:
    const: qcom,sc7280-camcc

  clocks:
    items:
      - description: Board XO source
      - description: Board XO active source
      - description: Sleep clock source

  clock-names:
    items:
      - const: bi_tcxo
      - const: bi_tcxo_ao
      - const: sleep_clk

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@ad00000 {
      compatible = "qcom,sc7280-camcc";
      reg = <0x0ad00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>;
      clock-names = "bi_tcxo", "bi_tcxo_ao", "sleep_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
