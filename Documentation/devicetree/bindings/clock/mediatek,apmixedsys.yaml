# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/mediatek,apmixedsys.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek AP Mixedsys Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  The Mediatek apmixedsys controller provides PLLs to the system.
  The clock values can be found in <dt-bindings/clock/mt*-clk.h>.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt6797-apmixedsys
          - mediatek,mt7622-apmixedsys
          - mediatek,mt7981-apmixedsys
          - mediatek,mt7986-apmixedsys
          - mediatek,mt8135-apmixedsys
          - mediatek,mt8173-apmixedsys
          - mediatek,mt8516-apmixedsys
      - items:
          - const: mediatek,mt7623-apmixedsys
          - const: mediatek,mt2701-apmixedsys
          - const: syscon
      - items:
          - enum:
              - mediatek,mt2701-apmix<PERSON>ys
              - mediatek,mt2712-apmixedsys
              - mediatek,mt6765-apmixedsys
              - mediatek,mt6779-apmixedsys
              - mediatek,mt6795-apmixedsys
              - mediatek,mt7629-apmixedsys
              - mediatek,mt8167-apmixedsys
              - mediatek,mt8183-apmixedsys
          - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    apmixedsys: clock-controller@10209000 {
        compatible = "mediatek,mt8173-apmixedsys";
        reg = <0x10209000 0x1000>;
        #clock-cells = <1>;
    };
