# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8998.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8998

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8998.

  See also:: include/dt-bindings/clock/qcom,gcc-msm8998.h

properties:
  compatible:
    const: qcom,gcc-msm8998

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: Audio reference clock (Optional clock)
    minItems: 2

  clock-names:
    items:
      - const: xo
      - const: sleep_clk
      - const: aud_ref_clk # Optional clock
    minItems: 2

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmcc.h>
    clock-controller@100000 {
      compatible = "qcom,gcc-msm8998";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      reg = <0x00100000 0xb0000>;
      clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
               <&sleep>,
               <0>;
      clock-names = "xo",
                    "sleep_clk",
                    "aud_ref_clk";
    };
...
