# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sdx65.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SDX65

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SDX65

  See also:: include/dt-bindings/clock/qcom,gcc-sdx65.h

properties:
  compatible:
    const: qcom,gcc-sdx65

  clocks:
    items:
      - description: Board XO source
      - description: Board active XO source
      - description: Sleep clock source
      - description: PCIE Pipe clock source
      - description: USB3 phy wrapper pipe clock source

  clock-names:
    items:
      - const: bi_tcxo
      - const: bi_tcxo_ao
      - const: sleep_clk
      - const: pcie_pipe_clk
      - const: usb3_phy_wrapper_gcc_usb30_pipe_clk

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,gcc-sdx65";
      reg = <0x100000 0x1f7400>;
      clocks = <&rpmhcc RPMH_CXO_CLK>, <&rpmhcc RPMH_CXO_CLK_A>, <&sleep_clk>,
               <&pcie_pipe_clk>, <&usb3_phy_wrapper_gcc_usb30_pipe_clk>;
      clock-names = "bi_tcxo", "bi_tcxo_ao", "sleep_clk",
                    "pcie_pipe_clk", "usb3_phy_wrapper_gcc_usb30_pipe_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
