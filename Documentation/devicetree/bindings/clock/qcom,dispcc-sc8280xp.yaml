# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,dispcc-sc8280xp.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller on SC8280XP

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  Qualcomm display clock control module which supports the clocks, resets and
  power domains for the two MDSS instances on SC8280XP.

  See also:
    include/dt-bindings/clock/qcom,dispcc-sc8280xp.h

properties:
  compatible:
    enum:
      - qcom,sc8280xp-dispcc0
      - qcom,sc8280xp-dispcc1

  clocks:
    items:
      - description: AHB interface clock,
      - description: SoC CXO clock
      - description: SoC sleep clock
      - description: DisplayPort 0 link clock
      - description: DisplayPort 0 VCO div clock
      - description: DisplayPort 1 link clock
      - description: DisplayPort 1 VCO div clock
      - description: DisplayPort 2 link clock
      - description: DisplayPort 2 VCO div clock
      - description: DisplayPort 3 link clock
      - description: DisplayPort 3 VCO div clock
      - description: DSI 0 PLL byte clock
      - description: DSI 0 PLL DSI clock
      - description: DSI 1 PLL byte clock
      - description: DSI 1 PLL DSI clock

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

  power-domains:
    items:
      - description: MMCX power domain

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sc8280xp.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom-rpmpd.h>
    clock-controller@af00000 {
      compatible = "qcom,sc8280xp-dispcc0";
      reg = <0x0af00000 0x20000>;
      clocks = <&gcc GCC_DISP_AHB_CLK>,
               <&rpmhcc RPMH_CXO_CLK>,
               <&sleep_clk>,
               <&mdss0_dp_phy0 0>,
               <&mdss0_dp_phy0 1>,
               <&mdss0_dp_phy1 0>,
               <&mdss0_dp_phy1 1>,
               <&mdss0_dp_phy2 0>,
               <&mdss0_dp_phy2 1>,
               <&mdss0_dp_phy3 0>,
               <&mdss0_dp_phy3 1>,
               <&mdss0_dsi0_phy 0>,
               <&mdss0_dsi0_phy 1>,
               <&mdss0_dsi1_phy 0>,
               <&mdss0_dsi1_phy 1>;
      power-domains = <&rpmhpd SC8280XP_MMCX>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
