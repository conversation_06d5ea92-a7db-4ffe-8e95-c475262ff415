# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/imx8mp-audiomix.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP i.MX8MP AudioMIX Block Control

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  NXP i.MX8M Plus AudioMIX is dedicated clock muxing and gating IP
  used to control Audio related clock on the SoC.

properties:
  compatible:
    const: fsl,imx8mp-audio-blk-ctrl

  reg:
    maxItems: 1

  power-domains:
    maxItems: 1

  clocks:
    minItems: 7
    maxItems: 7

  clock-names:
    items:
      - const: ahb
      - const: sai1
      - const: sai2
      - const: sai3
      - const: sai5
      - const: sai6
      - const: sai7

  '#clock-cells':
    const: 1
    description:
      The clock consumer should specify the desired clock by having the clock
      ID in its "clocks" phandle cell. See include/dt-bindings/clock/imx8mp-clock.h
      for the full list of i.MX8MP IMX8MP_CLK_AUDIOMIX_ clock IDs.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - power-domains
  - '#clock-cells'

additionalProperties: false

examples:
  # Clock Control Module node:
  - |
    #include <dt-bindings/clock/imx8mp-clock.h>

    clock-controller@30e20000 {
        compatible = "fsl,imx8mp-audio-blk-ctrl";
        reg = <0x30e20000 0x10000>;
        #clock-cells = <1>;
        clocks = <&clk IMX8MP_CLK_AUDIO_ROOT>,
                 <&clk IMX8MP_CLK_SAI1>,
                 <&clk IMX8MP_CLK_SAI2>,
                 <&clk IMX8MP_CLK_SAI3>,
                 <&clk IMX8MP_CLK_SAI5>,
                 <&clk IMX8MP_CLK_SAI6>,
                 <&clk IMX8MP_CLK_SAI7>;
        clock-names = "ahb",
                      "sai1", "sai2", "sai3",
                      "sai5", "sai6", "sai7";
        power-domains = <&pgc_audio>;
    };

...
