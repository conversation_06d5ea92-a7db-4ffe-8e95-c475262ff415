# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,qcm2290-dispcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Display Clock & Reset Controller on QCM2290

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm display clock control module provides the clocks, resets and power
  domains on qcm2290.

  See also:: include/dt-bindings/clock/qcom,dispcc-qcm2290.h

properties:
  compatible:
    const: qcom,qcm2290-dispcc

  clocks:
    items:
      - description: Board XO source
      - description: Board active-only XO source
      - description: GPLL0 source from GCC
      - description: GPLL0 div source from GCC
      - description: Byte clock from DSI PHY
      - description: Pixel clock from DSI PHY

  clock-names:
    items:
      - const: bi_tcxo
      - const: bi_tcxo_ao
      - const: gcc_disp_gpll0_clk_src
      - const: gcc_disp_gpll0_div_clk_src
      - const: dsi0_phy_pll_out_byteclk
      - const: dsi0_phy_pll_out_dsiclk

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,dispcc-qcm2290.h>
    #include <dt-bindings/clock/qcom,gcc-qcm2290.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>
    clock-controller@5f00000 {
            compatible = "qcom,qcm2290-dispcc";
            reg = <0x5f00000 0x20000>;
            clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
                     <&rpmcc RPM_SMD_XO_A_CLK_SRC>,
                     <&gcc GCC_DISP_GPLL0_CLK_SRC>,
                     <&gcc GCC_DISP_GPLL0_DIV_CLK_SRC>,
                     <&dsi0_phy 0>,
                     <&dsi0_phy 1>;
            clock-names = "bi_tcxo",
                          "bi_tcxo_ao",
                          "gcc_disp_gpll0_clk_src",
                          "gcc_disp_gpll0_div_clk_src",
                          "dsi0_phy_pll_out_byteclk",
                          "dsi0_phy_pll_out_dsiclk";
            #clock-cells = <1>;
            #reset-cells = <1>;
            #power-domain-cells = <1>;
    };
...
