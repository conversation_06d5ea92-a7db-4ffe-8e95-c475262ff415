Binding for TI mux clock.

Binding status: Unstable - ABI compatibility may be broken in the future

This binding uses the common clock binding[1].  It assumes a
register-mapped multiplexer with multiple input clock signals or
parents, one of which can be selected as output.  This clock does not
gate or adjust the parent rate via a divider or multiplier.

By default the "clocks" property lists the parents in the same order
as they are programmed into the register.  E.g:

	clocks = <&foo_clock>, <&bar_clock>, <&baz_clock>;

results in programming the register as follows:

register value		selected parent clock
0			foo_clock
1			bar_clock
2			baz_clock

Some clock controller IPs do not allow a value of zero to be programmed
into the register, instead indexing begins at 1.  The optional property
"index-starts-at-one" modified the scheme as follows:

register value		selected clock parent
1			foo_clock
2			bar_clock
3			baz_clock

The binding must provide the register to control the mux. Optionally
the number of bits to shift the control field in the register can be
supplied. If the shift value is missing it is the same as supplying
a zero shift.

[1] Documentation/devicetree/bindings/clock/clock-bindings.txt

Required properties:
- compatible : shall be "ti,mux-clock" or "ti,composite-mux-clock".
- #clock-cells : from common clock binding; shall be set to 0.
- clocks : link phandles of parent clocks
- reg : register offset for register controlling adjustable mux

Optional properties:
- clock-output-names : from common clock binding.
- ti,bit-shift : number of bits to shift the bit-mask, defaults to
  0 if not present
- ti,index-starts-at-one : valid input select programming starts at 1, not
  zero
- ti,set-rate-parent : clk_set_rate is propagated to parent clock,
  not supported by the composite-mux-clock subtype
- ti,latch-bit : latch the mux value to HW, only needed if the register
  access requires this. As an example, dra7x DPLL_GMAC H14 muxing
  implements such behavior.

Examples:

sys_clkin_ck: sys_clkin_ck@4a306110 {
	#clock-cells = <0>;
	compatible = "ti,mux-clock";
	clocks = <&virt_12000000_ck>, <&virt_13000000_ck>, <&virt_16800000_ck>, <&virt_19200000_ck>, <&virt_26000000_ck>, <&virt_27000000_ck>, <&virt_38400000_ck>;
	reg = <0x0110>;
	ti,index-starts-at-one;
};

abe_dpll_bypass_clk_mux_ck: abe_dpll_bypass_clk_mux_ck@4a306108 {
	#clock-cells = <0>;
	compatible = "ti,mux-clock";
	clocks = <&sys_clkin_ck>, <&sys_32k_ck>;
	ti,bit-shift = <24>;
	reg = <0x0108>;
};

mcbsp5_mux_fck: mcbsp5_mux_fck {
	#clock-cells = <0>;
	compatible = "ti,composite-mux-clock";
	clocks = <&core_96m_fck>, <&mcbsp_clks>;
	ti,bit-shift = <4>;
	reg = <0x02d8>;
};
