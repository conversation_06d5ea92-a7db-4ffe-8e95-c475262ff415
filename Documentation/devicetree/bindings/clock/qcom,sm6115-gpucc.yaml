# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm6115-gpucc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Graphics Clock & Reset Controller on SM6115

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm graphics clock control module provides clocks, resets and power
  domains on Qualcomm SoCs.

  See also:: include/dt-bindings/clock/qcom,sm6115-gpucc.h

properties:
  compatible:
    enum:
      - qcom,sm6115-gpucc

  clocks:
    items:
      - description: Board XO source
      - description: GPLL0 main branch source
      - description: GPLL0 main div source

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sm6115.h>
    #include <dt-bindings/clock/qcom,rpmcc.h>

    soc {
        #address-cells = <1>;
        #size-cells = <1>;

        clock-controller@5990000 {
            compatible = "qcom,sm6115-gpucc";
            reg = <0x05990000 0x9000>;
            clocks = <&rpmcc RPM_SMD_XO_CLK_SRC>,
                     <&gcc GCC_GPU_GPLL0_CLK_SRC>,
                     <&gcc GCC_GPU_GPLL0_DIV_CLK_SRC>;
            #clock-cells = <1>;
            #reset-cells = <1>;
            #power-domain-cells = <1>;
        };
    };
...
