# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-ipq8074.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on IPQ8074

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on IPQ8074.

  See also:: include/dt-bindings/clock/qcom,gcc-ipq8074.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    const: qcom,gcc-ipq8074

  clocks:
    items:
      - description: board XO clock
      - description: sleep clock

  clock-names:
    items:
      - const: xo
      - const: sleep_clk

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    clock-controller@1800000 {
      compatible = "qcom,gcc-ipq8074";
      reg = <0x01800000 0x80000>;
      #clock-cells = <1>;
      #power-domain-cells = <1>;
      #reset-cells = <1>;
    };
...
