# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8450-videocc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Video Clock & Reset Controller on SM8450

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm video clock control module provides the clocks, resets and power
  domains on SM8450.

  See also:: include/dt-bindings/clock/qcom,videocc-sm8450.h

properties:
  compatible:
    enum:
      - qcom,sm8450-videocc
      - qcom,sm8550-videocc

  reg:
    maxItems: 1

  clocks:
    items:
      - description: Board XO source
      - description: Video AHB clock from GCC

  power-domains:
    maxItems: 1
    description:
      MMCX power domain.

  required-opps:
    maxItems: 1
    description:
      A phandle to an OPP node describing required MMCX performance point.

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

required:
  - compatible
  - reg
  - clocks
  - power-domains
  - required-opps
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sm8450.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>
    videocc: clock-controller@aaf0000 {
      compatible = "qcom,sm8450-videocc";
      reg = <0x0aaf0000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&gcc GCC_VIDEO_AHB_CLK>;
      power-domains = <&rpmhpd RPMHPD_MMCX>;
      required-opps = <&rpmhpd_opp_low_svs>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
