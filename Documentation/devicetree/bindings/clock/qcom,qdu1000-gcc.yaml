# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,qdu1000-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller for QDU1000 and QRU1000

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module which supports the clocks, resets and
  power domains on QDU1000 and QRU1000

  See also:: include/dt-bindings/clock/qcom,qdu1000-gcc.h

properties:
  compatible:
    const: qcom,qdu1000-gcc

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: PCIE 0 Pipe clock source
      - description: PCIE 0 Phy Auxiliary clock source
      - description: USB3 Phy wrapper pipe clock source

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,qdu1000-gcc";
      reg = <0x00100000 0x001f4200>;
      clocks = <&rpmhcc RPMH_CXO_CLK>, <&sleep_clk>,
               <&pcie_0_pipe_clk>, <&pcie_0_phy_aux_clk>,
               <&usb3_phy_wrapper_pipe_clk>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
