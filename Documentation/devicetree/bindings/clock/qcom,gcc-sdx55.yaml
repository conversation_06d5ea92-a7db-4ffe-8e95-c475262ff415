# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sdx55.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SDX55

maintainers:
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON>m <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and
  power domains on SDX55

  See also:: include/dt-bindings/clock/qcom,gcc-sdx55.h

properties:
  compatible:
    const: qcom,gcc-sdx55

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: bi_tcxo
      - const: sleep_clk

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,gcc-sdx55";
      reg = <0x00100000 0x1f0000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&sleep_clk>;
      clock-names = "bi_tcxo",
                    "sleep_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };

...
