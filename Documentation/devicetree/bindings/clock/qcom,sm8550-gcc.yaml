# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sm8550-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SM8550

maintainers:
  - <PERSON><PERSON><PERSON> <and<PERSON><PERSON>@kernel.org>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SM8550

  See also:: include/dt-bindings/clock/qcom,sm8550-gcc.h

properties:
  compatible:
    const: qcom,sm8550-gcc

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: PCIE 0 Pipe clock source
      - description: PCIE 1 Pipe clock source
      - description: PCIE 1 Phy Auxiliary clock source
      - description: UFS Phy Rx symbol 0 clock source
      - description: UFS Phy Rx symbol 1 clock source
      - description: UFS Phy Tx symbol 0 clock source
      - description: USB3 Phy wrapper pipe clock source

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@100000 {
      compatible = "qcom,sm8550-gcc";
      reg = <0x00100000 0x001f4200>;
      clocks = <&rpmhcc RPMH_CXO_CLK>, <&sleep_clk>,
               <&pcie0_phy>,
               <&pcie1_phy>,
               <&pcie_1_phy_aux_clk>,
               <&ufs_mem_phy 0>,
               <&ufs_mem_phy 1>,
               <&ufs_mem_phy 2>,
               <&usb_1_qmpphy>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };

...
