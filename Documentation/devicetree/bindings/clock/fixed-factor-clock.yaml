# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/fixed-factor-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Simple fixed factor rate clock sources

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    enum:
      - fixed-factor-clock

  "#clock-cells":
    const: 0

  clocks:
    maxItems: 1

  clock-div:
    description: Fixed divider
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 1

  clock-mult:
    description: Fixed multiplier
    $ref: /schemas/types.yaml#/definitions/uint32

  clock-output-names:
    maxItems: 1

required:
  - compatible
  - clocks
  - "#clock-cells"
  - clock-div
  - clock-mult

additionalProperties: false

examples:
  - |
    clock {
      compatible = "fixed-factor-clock";
      clocks = <&parentclk>;
      #clock-cells = <0>;
      clock-div = <2>;
      clock-mult = <1>;
    };
...
