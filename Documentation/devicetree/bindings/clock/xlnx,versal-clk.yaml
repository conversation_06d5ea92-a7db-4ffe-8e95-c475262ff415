# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/xlnx,versal-clk.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx Versal clock controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  The clock controller is a hardware block of Xilinx versal clock tree. It
  reads required input clock frequencies from the devicetree and acts as clock
  provider for all clock consumers of PS clocks.

properties:
  compatible:
    oneOf:
      - enum:
          - xlnx,versal-clk
          - xlnx,zynqmp-clk
      - items:
          - enum:
              - xlnx,versal-net-clk
          - const: xlnx,versal-clk

  "#clock-cells":
    const: 1

  clocks:
    description: List of clock specifiers which are external input
      clocks to the given clock controller.
    minItems: 3
    maxItems: 8

  clock-names:
    minItems: 3
    maxItems: 8

required:
  - compatible
  - "#clock-cells"
  - clocks
  - clock-names

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - xlnx,versal-clk

    then:
      properties:
        clocks:
          items:
            - description: reference clock
            - description: alternate reference clock
            - description: alternate reference clock for programmable logic

        clock-names:
          items:
            - const: ref
            - const: alt_ref
            - const: pl_alt_ref

  - if:
      properties:
        compatible:
          contains:
            enum:
              - xlnx,zynqmp-clk

    then:
      properties:
        clocks:
          minItems: 5
          items:
            - description: PS reference clock
            - description: reference clock for video system
            - description: alternative PS reference clock
            - description: auxiliary reference clock
            - description: transceiver reference clock
            - description: (E)MIO clock source  (Optional clock)
            - description: GEM emio clock  (Optional clock)
            - description: Watchdog external clock (Optional clock)

        clock-names:
          minItems: 5
          items:
            - const: pss_ref_clk
            - const: video_clk
            - const: pss_alt_ref_clk
            - const: aux_ref_clk
            - const: gt_crx_ref_clk
            - pattern: "^mio_clk[00-77]+.*$"
            - pattern: "gem[0-3]+_emio_clk.*$"
            - pattern: "swdt[0-1]+_ext_clk.*$"

examples:
  - |
    firmware {
      zynqmp_firmware: zynqmp-firmware {
        compatible = "xlnx,zynqmp-firmware";
        method = "smc";
        versal_clk: clock-controller {
          #clock-cells = <1>;
          compatible = "xlnx,versal-clk";
          clocks = <&ref>, <&alt_ref>, <&pl_alt_ref>;
          clock-names = "ref", "alt_ref", "pl_alt_ref";
        };
      };
    };

    clock-controller {
        #clock-cells = <1>;
        compatible = "xlnx,zynqmp-clk";
        clocks = <&pss_ref_clk>, <&video_clk>, <&pss_alt_ref_clk>,
                 <&aux_ref_clk>, <&gt_crx_ref_clk>;
        clock-names = "pss_ref_clk", "video_clk", "pss_alt_ref_clk",
                      "aux_ref_clk", "gt_crx_ref_clk";
    };
...
