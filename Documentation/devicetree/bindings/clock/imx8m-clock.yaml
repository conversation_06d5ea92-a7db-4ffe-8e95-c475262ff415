# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/imx8m-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NXP i.MX8M Family Clock Control Module

maintainers:
  - <PERSON><PERSON> <<PERSON><PERSON>.<EMAIL>>

description: |
  NXP i.MX8M Mini/Nano/Plus/Quad clock control module is an integrated clock
  controller, which generates and supplies to all modules.

properties:
  compatible:
    enum:
      - fsl,imx8mm-ccm
      - fsl,imx8mn-ccm
      - fsl,imx8mp-ccm
      - fsl,imx8mq-ccm

  reg:
    maxItems: 1

  interrupts:
    maxItems: 2

  clocks:
    minItems: 6
    maxItems: 7

  clock-names:
    minItems: 6
    maxItems: 7

  '#clock-cells':
    const: 1
    description:
      The clock consumer should specify the desired clock by having the clock
      ID in its "clocks" phandle cell. See include/dt-bindings/clock/imx8m-clock.h
      for the full list of i.MX8M clock IDs.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'

allOf:
  - if:
      properties:
        compatible:
          contains:
            const: fsl,imx8mq-ccm
    then:
      properties:
        clocks:
          items:
            - description: 32k osc
            - description: 25m osc
            - description: 27m osc
            - description: ext1 clock input
            - description: ext2 clock input
            - description: ext3 clock input
            - description: ext4 clock input
        clock-names:
          items:
            - const: ckil
            - const: osc_25m
            - const: osc_27m
            - const: clk_ext1
            - const: clk_ext2
            - const: clk_ext3
            - const: clk_ext4
    else:
      properties:
        clocks:
          items:
            - description: 32k osc
            - description: 24m osc
            - description: ext1 clock input
            - description: ext2 clock input
            - description: ext3 clock input
            - description: ext4 clock input

        clock-names:
          items:
            - const: osc_32k
            - const: osc_24m
            - const: clk_ext1
            - const: clk_ext2
            - const: clk_ext3
            - const: clk_ext4

additionalProperties: false

examples:
  # Clock Control Module node:
  - |
    clock-controller@30380000 {
        compatible = "fsl,imx8mm-ccm";
        reg = <0x30380000 0x10000>;
        #clock-cells = <1>;
        clocks = <&osc_32k>, <&osc_24m>, <&clk_ext1>, <&clk_ext2>,
                 <&clk_ext3>, <&clk_ext4>;
        clock-names = "osc_32k", "osc_24m", "clk_ext1", "clk_ext2",
                      "clk_ext3", "clk_ext4";
    };

  - |
    clock-controller@30380000 {
        compatible = "fsl,imx8mq-ccm";
        reg = <0x30380000 0x10000>;
        #clock-cells = <1>;
        clocks = <&ckil>, <&osc_25m>, <&osc_27m>, <&clk_ext1>,
                 <&clk_ext2>, <&clk_ext3>, <&clk_ext4>;
        clock-names = "ckil", "osc_25m", "osc_27m", "clk_ext1",
                      "clk_ext2", "clk_ext3", "clk_ext4";
    };

...
