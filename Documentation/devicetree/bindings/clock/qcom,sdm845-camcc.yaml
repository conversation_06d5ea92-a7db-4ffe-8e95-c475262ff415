# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sdm845-camcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Camera Clock & Reset Controller on SDM845

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  Qualcomm camera clock control module provides the clocks, resets and power
  domains on SDM845.

  See also:: include/dt-bindings/clock/qcom,camcc-sm845.h

properties:
  compatible:
    const: qcom,sdm845-camcc

  clocks:
    items:
      - description: Board XO source

  clock-names:
    items:
      - const: bi_tcxo

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@ad00000 {
      compatible = "qcom,sdm845-camcc";
      reg = <0x0ad00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>;
      clock-names = "bi_tcxo";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
