# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/st,stm32mp1-rcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32MP1 Reset Clock Controller

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The RCC IP is both a reset and a clock controller.
  RCC makes also power management (resume/supend and wakeup interrupt).
  Please also refer to reset.txt for common reset controller binding usage.

  This binding uses common clock bindings
  Documentation/devicetree/bindings/clock/clock-bindings.txt

  Specifying clocks
  =================

  All available clocks are defined as preprocessor macros in
  dt-bindings/clock/stm32mp1-clks.h header and can be used in device
  tree sources.

  Specifying softreset control of devices
  =======================================

  Device nodes should specify the reset channel required in their "resets"
  property, containing a phandle to the reset device node and an index specifying
  which channel to use.
  The index is the bit number within the RCC registers bank, starting from RCC
  base address.
  It is calculated as: index = register_offset / 4 * 32 + bit_offset.
  Where bit_offset is the bit offset within the register.

  For example on STM32MP1, for LTDC reset:
     ltdc = APB4_RSTSETR_offset / 4 * 32 + LTDC_bit_offset
          = 0x180 / 4 * 32 + 0 = 3072

  The list of valid indices for STM32MP1 is available in:
  include/dt-bindings/reset-controller/stm32mp1-resets.h
  include/dt-bindings/reset-controller/stm32mp13-resets.h

  This file implements defines like:
  #define LTDC_R	3072

properties:
  "#clock-cells":
    const: 1

  "#reset-cells":
    const: 1

  compatible:
    items:
      - enum:
          - st,stm32mp1-rcc-secure
          - st,stm32mp1-rcc
          - st,stm32mp13-rcc
      - const: syscon
  clocks: true
  clock-names: true

  reg:
    maxItems: 1

required:
  - "#clock-cells"
  - "#reset-cells"
  - compatible
  - reg

if:
  properties:
    compatible:
      contains:
        enum:
          - st,stm32mp1-rcc-secure
          - st,stm32mp13-rcc
then:
  properties:
    clocks:
      description: Specifies oscillators.
      maxItems: 5

    clock-names:
      items:
        - const: hse
        - const: hsi
        - const: csi
        - const: lse
        - const: lsi
  required:
    - clocks
    - clock-names
else:
  properties:
    clocks:
      description:
        Specifies the external RX clock for ethernet MAC.
      maxItems: 1

    clock-names:
      const: ETH_RX_CLK/ETH_REF_CLK

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/stm32mp1-clks.h>
    rcc: rcc@50000000 {
        compatible = "st,stm32mp1-rcc-secure", "syscon";
        reg = <0x50000000 0x1000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
        clock-names = "hse", "hsi", "csi", "lse", "lsi";
        clocks = <&scmi_clk CK_SCMI_HSE>,
                 <&scmi_clk CK_SCMI_HSI>,
                 <&scmi_clk CK_SCMI_CSI>,
                 <&scmi_clk CK_SCMI_LSE>,
                 <&scmi_clk CK_SCMI_LSI>;
    };
...
