# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8994.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8994

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8994 and MSM8992.

  See also:: include/dt-bindings/clock/qcom,gcc-msm8994.h

properties:
  compatible:
    enum:
      - qcom,gcc-msm8992
      - qcom,gcc-msm8994

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: xo
      - const: sleep

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@300000 {
      compatible = "qcom,gcc-msm8994";
      reg = <0x00300000 0x90000>;
      clocks = <&xo_board>, <&sleep_clk>;
      clock-names = "xo", "sleep";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };

...
