# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8660.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8660

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks and resets on
  MSM8660

  See also::
    include/dt-bindings/clock/qcom,gcc-msm8660.h
    include/dt-bindings/reset/qcom,gcc-msm8660.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    enum:
      - qcom,gcc-msm8660

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: pxo
      - const: cxo

required:
  - compatible

unevaluatedProperties: false

examples:
  # Example for GCC for MSM8974:
  - |
    clock-controller@900000 {
      compatible = "qcom,gcc-msm8660";
      reg = <0x900000 0x4000>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      clocks = <&pxo_board>, <&cxo_board>;
      clock-names = "pxo", "cxo";
    };
...
