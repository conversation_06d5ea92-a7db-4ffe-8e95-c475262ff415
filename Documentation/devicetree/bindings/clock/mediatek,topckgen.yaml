# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/mediatek,topckgen.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek Top Clock Generator Controller

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description:
  The Mediatek topckgen controller provides various clocks to the system.
  The clock values can be found in <dt-bindings/clock/mt*-clk.h>.

properties:
  compatible:
    oneOf:
      - enum:
          - mediatek,mt6797-topckgen
          - mediatek,mt7622-topckgen
          - mediatek,mt8135-topckgen
          - mediatek,mt8173-topckgen
          - mediatek,mt8516-topckgen
      - items:
          - const: mediatek,mt7623-topckgen
          - const: mediatek,mt2701-topckgen
          - const: syscon
      - items:
          - enum:
              - mediatek,mt2701-topckgen
              - mediatek,mt2712-topckgen
              - mediatek,mt6765-topckgen
              - mediatek,mt6779-topckgen
              - mediatek,mt6795-topckgen
              - mediatek,mt7629-topckgen
              - mediatek,mt7981-topckgen
              - mediatek,mt7986-topckgen
              - mediatek,mt8167-topckgen
              - mediatek,mt8183-topckgen
          - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    topckgen: clock-controller@10000000 {
        compatible = "mediatek,mt8173-topckgen";
        reg = <0x10000000 0x1000>;
        #clock-cells = <1>;
    };
