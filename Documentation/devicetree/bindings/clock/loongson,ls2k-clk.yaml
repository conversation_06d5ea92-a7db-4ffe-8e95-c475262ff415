# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/loongson,ls2k-clk.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Loongson-2 SoC Clock Control Module

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Loongson-2 SoC clock control module is an integrated clock controller, which
  generates and supplies to all modules.

properties:
  compatible:
    enum:
      - loongson,ls2k-clk

  reg:
    maxItems: 1

  clocks:
    items:
      - description: 100m ref

  clock-names:
    items:
      - const: ref_100m

  '#clock-cells':
    const: 1
    description:
      The clock consumer should specify the desired clock by having the clock
      ID in its "clocks" phandle cell. See include/dt-bindings/clock/loongson,ls2k-clk.h
      for the full list of Loongson-2 SoC clock IDs.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    ref_100m: clock-ref-100m {
        compatible = "fixed-clock";
        #clock-cells = <0>;
        clock-frequency = <100000000>;
        clock-output-names = "ref_100m";
    };

    clk: clock-controller@1fe00480 {
        compatible = "loongson,ls2k-clk";
        reg = <0x1fe00480 0x58>;
        #clock-cells = <1>;
        clocks = <&ref_100m>;
        clock-names = "ref_100m";
    };
