# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8916.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8916 and MSM8939

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on MSM8916 or MSM8939.

  See also::
    include/dt-bindings/clock/qcom,gcc-msm8916.h
    include/dt-bindings/clock/qcom,gcc-msm8939.h
    include/dt-bindings/reset/qcom,gcc-msm8916.h
    include/dt-bindings/reset/qcom,gcc-msm8939.h

properties:
  compatible:
    enum:
      - qcom,gcc-msm8916
      - qcom,gcc-msm8939

  clocks:
    items:
      - description: XO source
      - description: Sleep clock source
      - description: DSI phy instance 0 dsi clock
      - description: DSI phy instance 0 byte clock
      - description: External MCLK clock
      - description: External Primary I2S clock
      - description: External Secondary I2S clock

  clock-names:
    items:
      - const: xo
      - const: sleep_clk
      - const: dsi0pll
      - const: dsi0pllbyte
      - const: ext_mclk
      - const: ext_pri_i2s
      - const: ext_sec_i2s

required:
  - compatible

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@300000 {
      compatible = "qcom,gcc-msm8916";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      reg = <0x300000 0x90000>;
    };
...
