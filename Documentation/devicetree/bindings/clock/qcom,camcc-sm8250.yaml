# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,camcc-sm8250.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Camera Clock & Reset Controller on SM8250

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  Qualcomm camera clock control module provides the clocks, resets and
  power domains on SM8250.

  See also:: include/dt-bindings/clock/qcom,camcc-sm8250.h

properties:
  compatible:
    const: qcom,sm8250-camcc

  clocks:
    items:
      - description: AHB
      - description: Board XO source
      - description: Board active XO source
      - description: Sleep clock source

  clock-names:
    items:
      - const: iface
      - const: bi_tcxo
      - const: bi_tcxo_ao
      - const: sleep_clk

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  power-domains:
    items:
      - description: MMCX power domain

  reg:
    maxItems: 1

  required-opps:
    maxItems: 1
    description:
      OPP node describing required MMCX performance point.

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sm8250.h>
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@ad00000 {
      compatible = "qcom,sm8250-camcc";
      reg = <0x0ad00000 0x10000>;
      clocks = <&gcc GCC_CAMERA_AHB_CLK>,
               <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>;
      clock-names = "iface", "bi_tcxo", "bi_tcxo_ao", "sleep_clk";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
