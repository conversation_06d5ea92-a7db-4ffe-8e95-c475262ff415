# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,videocc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Video Clock & Reset Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm video clock control module provides the clocks, resets and power
  domains on Qualcomm SoCs.

  See also::
    include/dt-bindings/clock/qcom,videocc-sc7180.h
    include/dt-bindings/clock/qcom,videocc-sc7280.h
    include/dt-bindings/clock/qcom,videocc-sdm845.h
    include/dt-bindings/clock/qcom,videocc-sm8150.h
    include/dt-bindings/clock/qcom,videocc-sm8250.h

properties:
  compatible:
    enum:
      - qcom,sc7180-videocc
      - qcom,sc7280-videocc
      - qcom,sdm845-videocc
      - qcom,sm8150-videocc
      - qcom,sm8250-videocc

  clocks:
    minItems: 1
    maxItems: 3

  clock-names:
    minItems: 1
    maxItems: 3

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

  '#power-domain-cells':
    const: 1

  reg:
    maxItems: 1

  power-domains:
    description:
      A phandle and PM domain specifier for the MMCX power domain.
    maxItems: 1

  required-opps:
    description:
      A phandle to an OPP node describing required MMCX performance point.
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#reset-cells'
  - '#power-domain-cells'

allOf:
  - if:
      properties:
        compatible:
          enum:
            - qcom,sc7180-videocc
            - qcom,sdm845-videocc
            - qcom,sm8150-videocc
    then:
      properties:
        clocks:
          items:
            - description: Board XO source
        clock-names:
          items:
            - const: bi_tcxo

  - if:
      properties:
        compatible:
          enum:
            - qcom,sc7280-videocc
    then:
      properties:
        clocks:
          items:
            - description: Board XO source
            - description: Board active XO source
        clock-names:
          items:
            - const: bi_tcxo
            - const: bi_tcxo_ao

  - if:
      properties:
        compatible:
          enum:
            - qcom,sm8250-videocc
    then:
      properties:
        clocks:
          items:
            - description: AHB
            - description: Board XO source
            - description: Board active XO source
        clock-names:
          items:
            - const: iface
            - const: bi_tcxo
            - const: bi_tcxo_ao

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom,rpmhpd.h>
    clock-controller@ab00000 {
      compatible = "qcom,sdm845-videocc";
      reg = <0x0ab00000 0x10000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>;
      clock-names = "bi_tcxo";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      power-domains = <&rpmhpd RPMHPD_MMCX>;
      required-opps = <&rpmhpd_opp_low_svs>;
    };
...
