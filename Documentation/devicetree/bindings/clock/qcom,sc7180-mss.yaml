# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sc7180-mss.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Modem Clock Controller on SC7180

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm modem clock control module provides the clocks on SC7180.

  See also:: include/dt-bindings/clock/qcom,mss-sc7180.h

properties:
  compatible:
    const: qcom,sc7180-mss

  clocks:
    items:
      - description: gcc_mss_mfab_axi clock from GCC
      - description: gcc_mss_nav_axi clock from GCC
      - description: gcc_mss_cfg_ahb clock from GCC

  clock-names:
    items:
      - const: gcc_mss_mfab_axis
      - const: gcc_mss_nav_axi
      - const: cfg_ahb

  '#clock-cells':
    const: 1

  reg:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sc7180.h>
    clock-controller@41a8000 {
      compatible = "qcom,sc7180-mss";
      reg = <0x041a8000 0x8000>;
      clocks = <&gcc GCC_MSS_MFAB_AXIS_CLK>,
               <&gcc GCC_MSS_NAV_AXI_CLK>,
               <&gcc GCC_MSS_CFG_AHB_CLK>;
      clock-names = "gcc_mss_mfab_axis",
                    "gcc_mss_nav_axi",
                    "cfg_ahb";
      #clock-cells = <1>;
    };
...
