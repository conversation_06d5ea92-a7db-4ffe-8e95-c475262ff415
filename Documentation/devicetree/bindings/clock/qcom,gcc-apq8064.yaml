# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-apq8064.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on APQ8064/MSM8960

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on APQ8064.

  See also::
    include/dt-bindings/clock/qcom,gcc-msm8960.h
    include/dt-bindings/reset/qcom,gcc-msm8960.h

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - qcom,gcc-apq8064
              - qcom,gcc-msm8960
          - const: syscon
      - enum:
          - qcom,gcc-apq8064
          - qcom,gcc-msm8960
        deprecated: true

  thermal-sensor:
    description: child tsens device
    $ref: /schemas/thermal/qcom-tsens.yaml#

  clocks:
    maxItems: 3

  clock-names:
    items:
      - const: cxo
      - const: pxo
      - const: pll4

  nvmem-cells:
    minItems: 1
    maxItems: 2
    deprecated: true
    description:
      Qualcomm TSENS (thermal sensor device) on some devices can
      be part of GCC and hence the TSENS properties can also be part
      of the GCC/clock-controller node.
      For more details on the TSENS properties please refer
      Documentation/devicetree/bindings/thermal/qcom-tsens.yaml

  nvmem-cell-names:
    minItems: 1
    deprecated: true
    items:
      - const: calib
      - const: calib_backup

  '#thermal-sensor-cells':
    const: 1
    deprecated: true

required:
  - compatible

unevaluatedProperties: false

examples:
  - |
    clock-controller@900000 {
      compatible = "qcom,gcc-apq8064", "syscon";
      reg = <0x00900000 0x4000>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;

      thermal-sensor {
        compatible = "qcom,msm8960-tsens";

        nvmem-cells = <&tsens_calib>, <&tsens_backup>;
        nvmem-cell-names = "calib", "calib_backup";
        interrupts = <0 178 4>;
        interrupt-names = "uplow";

        #qcom,sensors = <11>;
        #thermal-sensor-cells = <1>;
      };
    };
...
