# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-msm8996.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on MSM8996

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module which provides the clocks, resets and
  power domains on MSM8996.

  See also:: include/dt-bindings/clock/qcom,gcc-msm8996.h

properties:
  compatible:
    const: qcom,gcc-msm8996

  clocks:
    minItems: 3
    items:
      - description: XO source
      - description: Second XO source
      - description: Sleep clock source
      - description: PCIe 0 PIPE clock (optional)
      - description: PCIe 1 PIPE clock (optional)
      - description: PCIe 2 PIPE clock (optional)
      - description: USB3 PIPE clock (optional)
      - description: UFS RX symbol 0 clock (optional)
      - description: UFS RX symbol 1 clock (optional)
      - description: UFS TX symbol 0 clock (optional)

  clock-names:
    minItems: 3
    items:
      - const: cxo
      - const: cxo2
      - const: sleep_clk
      - const: pcie_0_pipe_clk_src
      - const: pcie_1_pipe_clk_src
      - const: pcie_2_pipe_clk_src
      - const: usb3_phy_pipe_clk_src
      - const: ufs_rx_symbol_0_clk_src
      - const: ufs_rx_symbol_1_clk_src
      - const: ufs_tx_symbol_0_clk_src

required:
  - compatible

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    clock-controller@300000 {
      compatible = "qcom,gcc-msm8996";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
      reg = <0x300000 0x90000>;
    };
...
