# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/xlnx,clocking-wizard.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Xilinx clocking wizard

maintainers:
  - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The clocking wizard is a soft ip clocking block of Xilinx versal. It
  reads required input clock frequencies from the devicetree and acts as clock
  clock output.

properties:
  compatible:
    enum:
      - xlnx,clocking-wizard
      - xlnx,clocking-wizard-v5.2
      - xlnx,clocking-wizard-v6.0


  reg:
    maxItems: 1

  "#clock-cells":
    const: 1

  clocks:
    items:
      - description: clock input
      - description: axi clock

  clock-names:
    items:
      - const: clk_in1
      - const: s_axi_aclk


  xlnx,speed-grade:
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2, 3]
    description:
      Speed grade of the device. Higher the speed grade faster is the FPGA device.

  xlnx,nr-outputs:
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 1
    maximum: 8
    description:
      Number of outputs.

required:
  - compatible
  - reg
  - "#clock-cells"
  - clocks
  - clock-names
  - xlnx,speed-grade
  - xlnx,nr-outputs

additionalProperties: false

examples:
  - |
    clock-controller@b0000000  {
        compatible = "xlnx,clocking-wizard";
        reg = <0xb0000000 0x10000>;
        #clock-cells = <1>;
        xlnx,speed-grade = <1>;
        xlnx,nr-outputs = <6>;
        clock-names = "clk_in1", "s_axi_aclk";
        clocks = <&clkc 15>, <&clkc 15>;
    };
...
