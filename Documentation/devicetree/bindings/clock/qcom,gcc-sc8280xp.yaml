# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sc8280xp.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SC8280xp

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>

description: |
  Qualcomm global clock control module provides the clocks, resets and
  power domains on SC8280xp.

  See also:: include/dt-bindings/clock/qcom,gcc-sc8280xp.h

properties:
  compatible:
    const: qcom,gcc-sc8280xp

  clocks:
    items:
      - description: XO reference clock
      - description: Sleep clock
      - description: UFS memory first RX symbol clock
      - description: UFS memory second RX symbol clock
      - description: UFS memory first TX symbol clock
      - description: UFS card first RX symbol clock
      - description: UFS card second RX symbol clock
      - description: UFS card first TX symbol clock
      - description: Primary USB SuperSpeed pipe clock
      - description: USB4 PHY pipegmux clock source
      - description: USB4 PHY DP gmux clock source
      - description: USB4 PHY sys pipegmux clock source
      - description: USB4 PHY PCIe pipe clock
      - description: USB4 PHY router max pipe clock
      - description: Primary USB4 RX0 clock
      - description: Primary USB4 RX1 clock
      - description: Secondary USB SuperSpeed pipe clock
      - description: Second USB4 PHY pipegmux clock source
      - description: Second USB4 PHY DP gmux clock source
      - description: Second USB4 PHY sys pipegmux clock source
      - description: Second USB4 PHY PCIe pipe clock
      - description: Second USB4 PHY router max pipe clock
      - description: Secondary USB4 RX0 clock
      - description: Secondary USB4 RX1 clock
      - description: Multiport USB first SuperSpeed pipe clock
      - description: Multiport USB second SuperSpeed pipe clock
      - description: PCIe 2a pipe clock
      - description: PCIe 2b pipe clock
      - description: PCIe 3a pipe clock
      - description: PCIe 3b pipe clock
      - description: PCIe 4 pipe clock
      - description: First EMAC controller reference clock
      - description: Second EMAC controller reference clock

  power-domains:
    items:
      - description: CX domain

  protected-clocks:
    maxItems: 389

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    clock-controller@100000 {
      compatible = "qcom,gcc-sc8280xp";
      reg = <0x00100000 0x1f0000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&sleep_clk>,
               <&ufs_phy_rx_symbol_0_clk>,
               <&ufs_phy_rx_symbol_1_clk>,
               <&ufs_phy_tx_symbol_0_clk>,
               <&ufs_card_rx_symbol_0_clk>,
               <&ufs_card_rx_symbol_1_clk>,
               <&ufs_card_tx_symbol_0_clk>,
               <&usb_0_ssphy>,
               <&gcc_usb4_phy_pipegmux_clk_src>,
               <&gcc_usb4_phy_dp_gmux_clk_src>,
               <&gcc_usb4_phy_sys_pipegmux_clk_src>,
               <&usb4_phy_gcc_usb4_pcie_pipe_clk>,
               <&usb4_phy_gcc_usb4rtr_max_pipe_clk>,
               <&qusb4phy_gcc_usb4_rx0_clk>,
               <&qusb4phy_gcc_usb4_rx1_clk>,
               <&usb_1_ssphy>,
               <&gcc_usb4_1_phy_pipegmux_clk_src>,
               <&gcc_usb4_1_phy_dp_gmux_clk_src>,
               <&gcc_usb4_1_phy_sys_pipegmux_clk_src>,
               <&usb4_1_phy_gcc_usb4_pcie_pipe_clk>,
               <&usb4_1_phy_gcc_usb4rtr_max_pipe_clk>,
               <&qusb4phy_1_gcc_usb4_rx0_clk>,
               <&qusb4phy_1_gcc_usb4_rx1_clk>,
               <&usb_2_ssphy>,
               <&usb_3_ssphy>,
               <&pcie2a_lane>,
               <&pcie2b_lane>,
               <&pcie3a_lane>,
               <&pcie3b_lane>,
               <&pcie4_lane>,
               <&rxc0_ref_clk>,
               <&rxc1_ref_clk>;
      power-domains = <&rpmhpd SC8280XP_CX>;

      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
