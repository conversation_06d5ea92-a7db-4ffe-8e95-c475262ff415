# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-sc7280.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SC7280

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SC7280.

  See also:: include/dt-bindings/clock/qcom,gcc-sc7280.h

properties:
  compatible:
    const: qcom,gcc-sc7280

  clocks:
    items:
      - description: Board XO source
      - description: Board active XO source
      - description: Sleep clock source
      - description: PCIE-0 pipe clock source
      - description: PCIE-1 pipe clock source
      - description: USF phy rx symbol 0 clock source
      - description: USF phy rx symbol 1 clock source
      - description: USF phy tx symbol 0 clock source
      - description: USB30 phy wrapper pipe clock source

  clock-names:
    items:
      - const: bi_tcxo
      - const: bi_tcxo_ao
      - const: sleep_clk
      - const: pcie_0_pipe_clk
      - const: pcie_1_pipe_clk
      - const: ufs_phy_rx_symbol_0_clk
      - const: ufs_phy_rx_symbol_1_clk
      - const: ufs_phy_tx_symbol_0_clk
      - const: usb3_phy_wrapper_gcc_usb30_pipe_clk

  power-domains:
    items:
      - description: CX domain

required:
  - compatible
  - clocks
  - clock-names

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    #include <dt-bindings/power/qcom-rpmpd.h>

    clock-controller@100000 {
      compatible = "qcom,gcc-sc7280";
      reg = <0x00100000 0x1f0000>;
      clocks = <&rpmhcc RPMH_CXO_CLK>,
               <&rpmhcc RPMH_CXO_CLK_A>,
               <&sleep_clk>,
               <&pcie_0_pipe_clk>, <&pcie_1_pipe_clk>,
               <&ufs_phy_rx_symbol_0_clk>, <&ufs_phy_rx_symbol_1_clk>,
               <&ufs_phy_tx_symbol_0_clk>,
               <&usb3_phy_wrapper_gcc_usb30_pipe_clk>;

      clock-names = "bi_tcxo", "bi_tcxo_ao", "sleep_clk", "pcie_0_pipe_clk",
                     "pcie_1_pipe_clk", "ufs_phy_rx_symbol_0_clk",
                     "ufs_phy_rx_symbol_1_clk", "ufs_phy_tx_symbol_0_clk",
                     "usb3_phy_wrapper_gcc_usb30_pipe_clk";
      power-domains = <&rpmhpd SC7280_CX>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
