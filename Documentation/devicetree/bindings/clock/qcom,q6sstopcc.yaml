# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,q6sstopcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Q6SSTOP clock Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: qcom,qcs404-q6sstopcc

  reg:
    items:
      - description: Q6SSTOP clocks register region
      - description: Q6SSTOP_TCSR register region

  clocks:
    items:
      - description: ahb clock for the q6sstopCC

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg
  - clocks
  - '#clock-cells'

additionalProperties: false

examples:
  - |
    q6sstopcc: clock-controller@7500000 {
      compatible = "qcom,qcs404-q6sstopcc";
      reg = <0x07500000 0x4e000>, <0x07550000 0x10000>;
      clocks = <&gcc 141>;
      #clock-cells = <1>;
    };
