# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/imx6sl-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale i.MX6 SoloLite Clock Controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    const: fsl,imx6sl-ccm

  reg:
    maxItems: 1

  interrupts:
    description: CCM provides 2 interrupt requests, request 1 is to generate
      interrupt for frequency or mux change, request 2 is to generate
      interrupt for oscillator read or PLL lock.
    items:
      - description: CCM interrupt request 1
      - description: CCM interrupt request 2

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - '#clock-cells'

additionalProperties: false

examples:
  # Clock Control Module node:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    clock-controller@20c4000 {
        compatible = "fsl,imx6sl-ccm";
        reg = <0x020c4000 0x4000>;
        interrupts = <0 87 IRQ_TYPE_LEVEL_HIGH>,
                     <0 88 IRQ_TYPE_LEVEL_HIGH>;
        #clock-cells = <1>;
    };
