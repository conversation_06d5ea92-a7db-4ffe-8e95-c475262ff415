# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/amlogic,a1-peripherals-clkc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic A1 Peripherals Clock Control Unit

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    const: amlogic,a1-peripherals-clkc

  '#clock-cells':
    const: 1

  reg:
    maxItems: 1

  clocks:
    items:
      - description: input fixed pll div2
      - description: input fixed pll div3
      - description: input fixed pll div5
      - description: input fixed pll div7
      - description: input hifi pll
      - description: input oscillator (usually at 24MHz)

  clock-names:
    items:
      - const: fclk_div2
      - const: fclk_div3
      - const: fclk_div5
      - const: fclk_div7
      - const: hifi_pll
      - const: xtal

required:
  - compatible
  - '#clock-cells'
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/amlogic,a1-pll-clkc.h>
    apb {
        #address-cells = <2>;
        #size-cells = <2>;

        clock-controller@800 {
            compatible = "amlogic,a1-peripherals-clkc";
            reg = <0 0x800 0 0x104>;
            #clock-cells = <1>;
            clocks = <&clkc_pll CLKID_FCLK_DIV2>,
                     <&clkc_pll CLKID_FCLK_DIV3>,
                     <&clkc_pll CLKID_FCLK_DIV5>,
                     <&clkc_pll CLKID_FCLK_DIV7>,
                     <&clkc_pll CLKID_HIFI_PLL>,
                     <&xtal>;
            clock-names = "fclk_div2", "fclk_div3",
                          "fclk_div5", "fclk_div7",
                          "hifi_pll", "xtal";
        };
    };
