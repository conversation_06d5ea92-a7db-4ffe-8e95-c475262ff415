# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,sdx75-gcc.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on SDX75

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on SDX75

  See also:: include/dt-bindings/clock/qcom,sdx75-gcc.h

properties:
  compatible:
    const: qcom,sdx75-gcc

  clocks:
    items:
      - description: Board XO source
      - description: Sleep clock source
      - description: EMAC0 sgmiiphy mac rclk source
      - description: EMAC0 sgmiiphy mac tclk source
      - description: EMAC0 sgmiiphy rclk source
      - description: EMAC0 sgmiiphy tclk source
      - description: EMAC1 sgmiiphy mac rclk source
      - description: EMAC1 sgmiiphy mac tclk source
      - description: EMAC1 sgmiiphy rclk source
      - description: EMAC1 sgmiiphy tclk source
      - description: PCIE20 phy aux clock source
      - description: PCIE_1 Pipe clock source
      - description: PCIE_2 Pipe clock source
      - description: PCIE Pipe clock source
      - description: USB3 phy wrapper pipe clock source

required:
  - compatible
  - clocks

allOf:
  - $ref: qcom,gcc.yaml#

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,rpmh.h>
    clock-controller@80000 {
      compatible = "qcom,sdx75-gcc";
      reg = <0x80000 0x1f7400>;
      clocks = <&rpmhcc RPMH_CXO_CLK>, <&sleep_clk>, <&emac0_sgmiiphy_mac_rclk>,
               <&emac0_sgmiiphy_mac_tclk>, <&emac0_sgmiiphy_rclk>, <&emac0_sgmiiphy_tclk>,
               <&emac1_sgmiiphy_mac_rclk>, <&emac1_sgmiiphy_mac_tclk>, <&emac1_sgmiiphy_rclk>,
               <&emac1_sgmiiphy_tclk>, <&pcie20_phy_aux_clk>, <&pcie_1_pipe_clk>,
               <&pcie_2_pipe_clk>, <&pcie_pipe_clk>, <&usb3_phy_wrapper_gcc_usb30_pipe_clk>;
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;
    };
...
