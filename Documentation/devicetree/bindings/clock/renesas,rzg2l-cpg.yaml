# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/renesas,rzg2l-cpg.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/{G2L,V2L,V2M} Clock Pulse Generator / Module Standby Mode

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  On Renesas RZ/{G2L,V2L}-alike SoC's, the CPG (Clock Pulse Generator) and Module
  Standby Mode share the same register block. On RZ/V2M, the functionality is
  similar, but does not have Clock Monitor Registers.

  They provide the following functionalities:
    - The CPG block generates various core clocks,
    - The Module Standby Mode block provides two functions:
        1. Module Standby, providing a Clock Domain to control the clock supply
           to individual SoC devices,
        2. Reset Control, to perform a software reset of individual SoC devices.

properties:
  compatible:
    enum:
      - renesas,r9a07g043-cpg # RZ/G2UL{Type-1,Type-2} and RZ/Five
      - renesas,r9a07g044-cpg # RZ/G2{L,LC}
      - renesas,r9a07g054-cpg # RZ/V2L
      - renesas,r9a09g011-cpg # RZ/V2M

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    description:
      Clock source to CPG can be either from external clock input (EXCLK) or
      crystal oscillator (XIN/XOUT).
    const: extal

  '#clock-cells':
    description: |
      - For CPG core clocks, the two clock specifier cells must be "CPG_CORE"
        and a core clock reference, as defined in
        <dt-bindings/clock/r9a0*-cpg.h>,
      - For module clocks, the two clock specifier cells must be "CPG_MOD" and
        a module number, as defined in <dt-bindings/clock/r9a0*-cpg.h>.
    const: 2

  '#power-domain-cells':
    description:
      SoC devices that are part of the CPG/Module Standby Mode Clock Domain and
      can be power-managed through Module Standby should refer to the CPG device
      node in their "power-domains" property, as documented by the generic PM
      Domain bindings in Documentation/devicetree/bindings/power/power-domain.yaml.
    const: 0

  '#reset-cells':
    description:
      The single reset specifier cell must be the module number, as defined in
      <dt-bindings/clock/r9a0*-cpg.h>.
    const: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#clock-cells'
  - '#power-domain-cells'
  - '#reset-cells'

additionalProperties: false

examples:
  - |
    cpg: clock-controller@11010000 {
            compatible = "renesas,r9a07g044-cpg";
            reg = <0x11010000 0x10000>;
            clocks = <&extal_clk>;
            clock-names = "extal";
            #clock-cells = <2>;
            #power-domain-cells = <0>;
            #reset-cells = <1>;
    };
