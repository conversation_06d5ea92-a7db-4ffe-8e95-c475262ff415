# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/clock/qcom,gcc-ipq8064.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Global Clock & Reset Controller on IPQ8064

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Qualcomm global clock control module provides the clocks, resets and power
  domains on IPQ8064.

  See also::
    include/dt-bindings/clock/qcom,gcc-ipq806x.h (qcom,gcc-ipq8064)
    include/dt-bindings/reset/qcom,gcc-ipq806x.h (qcom,gcc-ipq8064)

allOf:
  - $ref: qcom,gcc.yaml#

properties:
  compatible:
    items:
      - const: qcom,gcc-ipq8064
      - const: syscon

  clocks:
    minItems: 2
    items:
      - description: PXO source
      - description: CXO source
      - description: PLL4 from LCC

  clock-names:
    minItems: 2
    items:
      - const: pxo
      - const: cxo
      - const: pll4

  thermal-sensor:
    type: object

    allOf:
      - $ref: /schemas/thermal/qcom-tsens.yaml#

required:
  - compatible
  - clocks
  - clock-names

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,lcc-ipq806x.h>
    #include <dt-bindings/interrupt-controller/arm-gic.h>

    gcc: clock-controller@900000 {
      compatible = "qcom,gcc-ipq8064", "syscon";
      reg = <0x00900000 0x4000>;
      clocks = <&pxo_board>, <&cxo_board>, <&lcc PLL4>;
      clock-names = "pxo", "cxo", "pll4";
      #clock-cells = <1>;
      #reset-cells = <1>;
      #power-domain-cells = <1>;

      tsens: thermal-sensor {
        compatible = "qcom,ipq8064-tsens";

        nvmem-cells = <&tsens_calib>, <&tsens_calib_backup>;
        nvmem-cell-names = "calib", "calib_backup";
        interrupts = <GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>;
        interrupt-names = "uplow";

        #qcom,sensors = <11>;
        #thermal-sensor-cells = <1>;
      };
    };
