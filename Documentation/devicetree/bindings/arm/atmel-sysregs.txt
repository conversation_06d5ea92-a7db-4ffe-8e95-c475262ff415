Atmel system registers

Chipid required properties:
- compatible: Should be "atmel,sama5d2-chipid" or "microchip,sama7g5-chipid"
- reg : Should contain registers location and length

PIT Timer required properties:
- compatible: Should be "atmel,at91sam9260-pit"
- reg: Should contain registers location and length
- interrupts: Should contain interrupt for the PIT which is the IRQ line
  shared across all System Controller members.

PIT64B Timer required properties:
- compatible: Should be "microchip,sam9x60-pit64b"
- reg: Should contain registers location and length
- interrupts: Should contain interrupt for PIT64B timer
- clocks: Should contain the available clock sources for PIT64B timer.

System Timer (ST) required properties:
- compatible: Should be "atmel,at91rm9200-st", "syscon", "simple-mfd"
- reg: Should contain registers location and length
- interrupts: Should contain interrupt for the ST which is the IRQ line
  shared across all System Controller members.
- clocks: phandle to input clock.
Its subnodes can be:
- watchdog: compatible should be "atmel,at91rm9200-wdt"

RAMC SDRAM/DDR Controller required properties:
- compatible: Should be "atmel,at91rm9200-sdramc", "syscon"
			"atmel,at91sam9260-sdramc",
			"atmel,at91sam9g45-ddramc",
			"atmel,sama5d3-ddramc",
			"microchip,sam9x60-ddramc",
			"microchip,sama7g5-uddrc"
- reg: Should contain registers location and length

Examples:

	ramc0: ramc@ffffe800 {
		compatible = "atmel,at91sam9g45-ddramc";
		reg = <0xffffe800 0x200>;
	};

RAMC PHY Controller required properties:
- compatible: Should be "microchip,sama7g5-ddr3phy", "syscon"
- reg: Should contain registers location and length

Example:

	ddr3phy: ddr3phy@e3804000 {
		compatible = "microchip,sama7g5-ddr3phy", "syscon";
		reg = <0xe3804000 0x1000>;
};

Special Function Registers (SFR)

Special Function Registers (SFR) manage specific aspects of the integrated
memory, bridge implementations, processor and other functionality not controlled
elsewhere.

required properties:
- compatible: Should be "atmel,<chip>-sfr", "syscon" or
	"atmel,<chip>-sfrbu", "syscon"
  <chip> can be "sama5d3", "sama5d4" or "sama5d2".
  It also can be "microchip,sam9x60-sfr", "syscon".
- reg: Should contain registers location and length

	sfr@f0038000 {
		compatible = "atmel,sama5d3-sfr", "syscon";
		reg = <0xf0038000 0x60>;
	};

Security Module (SECUMOD)

The Security Module macrocell provides all necessary secure functions to avoid
voltage, temperature, frequency and mechanical attacks on the chip. It also
embeds secure memories that can be scrambled.

The Security Module also offers the PIOBU pins which can be used as GPIO pins.
Note that they maintain their voltage during Backup/Self-refresh.

required properties:
- compatible: Should be "atmel,<chip>-secumod", "syscon".
  <chip> can be "sama5d2".
- reg: Should contain registers location and length
- gpio-controller:	Marks the port as GPIO controller.
- #gpio-cells:		There are 2. The pin number is the
			first, the second represents additional
			parameters such as GPIO_ACTIVE_HIGH/LOW.


	secumod@fc040000 {
		compatible = "atmel,sama5d2-secumod", "syscon";
		reg = <0xfc040000 0x100>;
		gpio-controller;
		#gpio-cells = <2>;
	};
