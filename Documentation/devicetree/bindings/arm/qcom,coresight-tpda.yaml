# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
# Copyright (c) 2023 Qualcomm Innovation Center, Inc. All rights reserved.
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/qcom,coresight-tpda.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Trace, Profiling and Diagnostics Aggregator - TPDA

description: |
  TPDAs are responsible for packetization and timestamping of data sets
  utilizing the MIPI STPv2 packet protocol. Pulling data sets from one or
  more attached TPDM and pushing the resultant (packetized) data out a
  master ATB interface. Performing an arbitrated ATB interleaving (funneling)
  task for free-flowing data from TPDM (i.e. CMB and DSB data set flows).

  There is no strict binding between TPDM and TPDA. TPDA can have multiple
  TPDMs connect to it. But There must be only one TPDA in the path from the
  TPDM source to TMC sink. TPDM can directly connect to TPDA's inport or
  connect to funnel which will connect to TPDA's inport.

  We can use the commands are similar to the below to validate TPDMs.
  Enable coresight sink first.

  echo 1 > /sys/bus/coresight/devices/tmc_etf0/enable_sink
  echo 1 > /sys/bus/coresight/devices/tpdm0/enable_source
  echo 1 > /sys/bus/coresight/devices/tpdm0/integration_test
  echo 2 > /sys/bus/coresight/devices/tpdm0/integration_test

  The test data will be collected in the coresight sink which is enabled.
  If rwp register of the sink is keeping updating when do integration_test
  (by cat tmc_etf0/mgmt/rwp), it means there is data generated from TPDM
  to sink.

maintainers:
  - Mao Jinlong <<EMAIL>>
  - Tao Zhang <<EMAIL>>

# Need a custom select here or 'arm,primecell' will match on lots of nodes
select:
  properties:
    compatible:
      contains:
        enum:
          - qcom,coresight-tpda
  required:
    - compatible

properties:
  $nodename:
    pattern: "^tpda(@[0-9a-f]+)$"
  compatible:
    items:
      - const: qcom,coresight-tpda
      - const: arm,primecell

  reg:
    minItems: 1
    maxItems: 2

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: apb_pclk

  in-ports:
    type: object
    description: |
      Input connections from TPDM to TPDA
    $ref: /schemas/graph.yaml#/properties/ports

  out-ports:
    type: object
    description: |
      Output connections from the TPDA to legacy CoreSight trace bus.
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port:
        description:
          Output connection from the TPDA to legacy CoreSight Trace bus.
        $ref: /schemas/graph.yaml#/properties/port

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - in-ports
  - out-ports

additionalProperties: false

examples:
  # minimum tpda definition.
  - |
    tpda@6004000 {
       compatible = "qcom,coresight-tpda", "arm,primecell";
       reg = <0x6004000 0x1000>;

       clocks = <&aoss_qmp>;
       clock-names = "apb_pclk";

       in-ports {
         #address-cells = <1>;
         #size-cells = <0>;

        port@0 {
          reg = <0>;
          tpda_qdss_0_in_tpdm_dcc: endpoint {
            remote-endpoint =
              <&tpdm_dcc_out_tpda_qdss_0>;
            };
        };
      };

       out-ports {
         port {
                 tpda_qdss_out_funnel_in0: endpoint {
                    remote-endpoint =
                    <&funnel_in0_in_tpda_qdss>;
                  };
          };
       };
    };

...
