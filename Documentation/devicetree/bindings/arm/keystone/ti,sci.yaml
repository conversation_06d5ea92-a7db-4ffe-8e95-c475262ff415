# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/keystone/ti,sci.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: TI-SCI controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  Texas Instrument's processors including those belonging to Keystone generation
  of processors have separate hardware entity which is now responsible for the
  management of the System on Chip (SoC) system. These include various system
  level functions as well.

  An example of such an SoC is K2G, which contains the system control hardware
  block called Power Management Micro Controller (PMMC). This hardware block is
  initialized early into boot process and provides services to Operating Systems
  on multiple processors including ones running Linux.

  See http://processors.wiki.ti.com/index.php/TISCI for protocol definition.

  The TI-SCI node describes the Texas Instrument's System Controller entity node.
  This parent node may optionally have additional children nodes which describe
  specific functionality such as clocks, power domain, reset or additional
  functionality as may be required for the SoC. This hierarchy also describes the
  relationship between the TI-SCI parent node to the child node.

properties:
  $nodename:
    pattern: "^system-controller@[0-9a-f]+$"

  compatible:
    oneOf:
      - description: System controller on TI 66AK2G SoC and other K3 SoCs
        items:
          - const: ti,k2g-sci
      - description: System controller on TI AM654 SoC
        items:
          - const: ti,am654-sci

  reg-names:
    description: |
      Specifies the debug messages memory mapped region that is optionally
      made available from TI-SCI controller.
    const: debug_messages

  reg:
    minItems: 1

  mbox-names:
    description: |
      Specifies the mailboxes used to communicate with TI-SCI Controller
      made available from TI-SCI controller.
    items:
      - const: rx
      - const: tx

  mboxes:
    minItems: 2

  ti,system-reboot-controller:
    description: Determines If system reboot can be triggered by SoC reboot
    type: boolean

  ti,host-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Value corresponding to the host ID assigned by Firmware
      for identification of host processing entities such as virtual machines.

  power-controller:
    type: object
    $ref: /schemas/soc/ti/sci-pm-domain.yaml#

  clock-controller:
    type: object
    $ref: /schemas/clock/ti,sci-clk.yaml#

  reset-controller:
    type: object
    $ref: /schemas/reset/ti,sci-reset.yaml#

required:
  - compatible
  - mbox-names
  - mboxes

additionalProperties: false

examples:
  - |
    pmmc: system-controller@2921800 {
      compatible = "ti,k2g-sci";
      ti,system-reboot-controller;
      mbox-names = "rx", "tx";
      mboxes = <&msgmgr 5 2>,
               <&msgmgr 0 0>;
      reg-names = "debug_messages";
      reg = <0x02921800 0x800>;
    };

  - |
    dmsc: system-controller@44083000 {
      compatible = "ti,k2g-sci";
      ti,host-id = <12>;
      mbox-names = "rx", "tx";
      mboxes = <&secure_proxy_main 11>,
               <&secure_proxy_main 13>;
      reg-names = "debug_messages";
      reg = <0x44083000 0x1000>;

      k3_pds: power-controller {
        compatible = "ti,sci-pm-domain";
        #power-domain-cells = <2>;
      };

      k3_clks: clock-controller {
        compatible = "ti,k2g-sci-clk";
        #clock-cells = <2>;
      };

      k3_reset: reset-controller {
        compatible = "ti,sci-reset";
        #reset-cells = <2>;
      };
    };
