# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/bcm/bcm2835.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom BCM2711/BCM2835 Platforms

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - description: BCM2711 based Boards
        items:
          - enum:
              - raspberrypi,400
              - raspberrypi,4-compute-module
              - raspberrypi,4-model-b
          - const: brcm,bcm2711

      - description: BCM2835 based Boards
        items:
          - enum:
              - raspberrypi,model-a
              - raspberrypi,model-a-plus
              - raspberrypi,model-b
              - raspberrypi,model-b-i2c0  # Raspberry Pi Model B (no P5)
              - raspberry<PERSON>,model-b-rev2
              - raspberry<PERSON>,model-b-plus
              - raspberry<PERSON>,compute-module
              - rasp<PERSON><PERSON>,model-zero
              - rasp<PERSON><PERSON>,model-zero-w
          - const: brcm,bcm2835

      - description: BCM2836 based Boards
        items:
          - enum:
              - rasp<PERSON><PERSON>,2-model-b
          - const: brcm,bcm2836

      - description: BCM2837 based Boards
        items:
          - enum:
              - raspberrypi,3-model-a-plus
              - raspberrypi,3-model-b
              - raspberrypi,3-model-b-plus
              - raspberrypi,3-compute-module
              - raspberrypi,3-compute-module-lite
              - raspberrypi,model-zero-2-w
          - const: brcm,bcm2837

additionalProperties: true

...
