# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/bcm/raspberrypi,bcm2835-firmware.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Raspberry Pi VideoCore firmware driver

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

select:
  properties:
    compatible:
      contains:
        const: raspberrypi,bcm2835-firmware

  required:
    - compatible

properties:
  compatible:
    items:
      - const: raspberrypi,bcm2835-firmware
      - const: simple-mfd

  mboxes:
    maxItems: 1

  clocks:
    type: object
    additionalProperties: false

    properties:
      compatible:
        const: raspberrypi,firmware-clocks

      "#clock-cells":
        const: 1
        description: >
          The argument is the ID of the clocks contained by the
          firmware messages.

    required:
      - compatible
      - "#clock-cells"

  reset:
    type: object
    additionalProperties: false

    properties:
      compatible:
        const: raspberrypi,firmware-reset

      "#reset-cells":
        const: 1
        description: >
          The argument is the ID of the firmware reset line to affect.

    required:
      - compatible
      - "#reset-cells"

  pwm:
    type: object
    additionalProperties: false

    properties:
      compatible:
        const: raspberrypi,firmware-poe-pwm

      "#pwm-cells":
        # See pwm.yaml in this directory for a description of the cells format.
        const: 2

    required:
      - compatible
      - "#pwm-cells"

required:
  - compatible
  - mboxes

additionalProperties: false

examples:
  - |
    firmware {
        compatible = "raspberrypi,bcm2835-firmware", "simple-mfd";
        mboxes = <&mailbox>;

        firmware_clocks: clocks {
            compatible = "raspberrypi,firmware-clocks";
            #clock-cells = <1>;
        };

        reset: reset {
            compatible = "raspberrypi,firmware-reset";
            #reset-cells = <1>;
        };

        pwm: pwm {
            compatible = "raspberrypi,firmware-poe-pwm";
            #pwm-cells = <2>;
        };
    };
...
