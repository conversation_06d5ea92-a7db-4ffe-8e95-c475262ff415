# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/atmel-at91.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Atmel AT91.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <nicola<PERSON>.<EMAIL>>

description: |
  Boards with a SoC of the Atmel AT91 or SMART family shall have the following

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:
      - items:
          - const: atmel,at91rm9200
      - items:
          - enum:
              - olimex,sam9-l9260
          - enum:
              - atmel,at91sam9260
              - atmel,at91sam9261
              - atmel,at91sam9263
              - atmel,at91sam9g20
              - atmel,at91sam9g45
              - atmel,at91sam9n12
              - atmel,at91sam9rl
              - atmel,at91sam9xe
              - atmel,at91sam9x60
          - const: atmel,at91sam9

      - items:
          - enum:
              - overkiz,kizboxmini-base # Overkiz kizbox Mini Base Board
              - overkiz,kizboxmini-mb   # Overkiz kizbox Mini Mother Board
              - overkiz,kizboxmini-rd   # Overkiz kizbox Mini RailDIN
              - overkiz,smartkiz        # Overkiz SmartKiz Board
              - gardena,smart-gateway-at91sam # GARDENA smart Gateway (Article No. 19000)
          - const: atmel,at91sam9g25
          - const: atmel,at91sam9x5
          - const: atmel,at91sam9

      - items:
          - enum:
              - atmel,at91sam9g15
              - atmel,at91sam9g25
              - atmel,at91sam9g35
              - atmel,at91sam9x25
              - atmel,at91sam9x35
          - const: atmel,at91sam9x5
          - const: atmel,at91sam9

      - description: Overkiz kizbox3 board
        items:
          - const: overkiz,kizbox3-hs
          - const: atmel,sama5d27
          - const: atmel,sama5d2
          - const: atmel,sama5

      - description: Microchip SAMA5D27 WLSOM1
        items:
          - const: microchip,sama5d27-wlsom1
          - const: atmel,sama5d27
          - const: atmel,sama5d2
          - const: atmel,sama5

      - description: Microchip SAMA5D27 WLSOM1 Evaluation Kit
        items:
          - const: microchip,sama5d27-wlsom1-ek
          - const: microchip,sama5d27-wlsom1
          - const: atmel,sama5d27
          - const: atmel,sama5d2
          - const: atmel,sama5

      - items:
          - const: atmel,sama5d27
          - const: atmel,sama5d2
          - const: atmel,sama5

      - description: Microchip SAMA5D2 Industrial Connectivity Platform
        items:
          - const: microchip,sama5d2-icp
          - const: atmel,sama5d27
          - const: atmel,sama5d2
          - const: atmel,sama5

      - description: Microchip SAM9X60 Evaluation Boards
        items:
          - enum:
              - microchip,sam9x60ek
              - microchip,sam9x60-curiosity
          - const: microchip,sam9x60
          - const: atmel,at91sam9

      - description: Nattis v2 board with Natte v2 power board
        items:
          - const: axentia,nattis-2
          - const: axentia,natte-2
          - const: axentia,linea
          - const: atmel,sama5d31
          - const: atmel,sama5d3
          - const: atmel,sama5

      - description: TSE-850 v3 board
        items:
          - const: axentia,tse850v3
          - const: axentia,linea
          - const: atmel,sama5d31
          - const: atmel,sama5d3
          - const: atmel,sama5

      - items:
          - const: axentia,linea
          - const: atmel,sama5d31
          - const: atmel,sama5d3
          - const: atmel,sama5

      - description: Overkiz kizbox2 board with two heads
        items:
          - const: overkiz,kizbox2-2
          - const: atmel,sama5d31
          - const: atmel,sama5d3
          - const: atmel,sama5

      - description: Microchip SAMA5D3 Ethernet Development System Board
        items:
          - const: microchip,sama5d3-eds
          - const: atmel,sama5d36
          - const: atmel,sama5d3
          - const: atmel,sama5

      - description: CalAmp LMU5000 board
        items:
          - const: calamp,lmu5000
          - const: atmel,at91sam9g20
          - const: atmel,at91sam9

      - description: Exegin Q5xR5 board
        items:
          - const: exegin,q5xr5
          - const: atmel,at91sam9g20
          - const: atmel,at91sam9

      - items:
          - enum:
              - atmel,sama5d31
              - atmel,sama5d33
              - atmel,sama5d34
              - atmel,sama5d35
              - atmel,sama5d36
          - const: atmel,sama5d3
          - const: atmel,sama5

      - items:
          - enum:
              - atmel,sama5d41
              - atmel,sama5d42
              - atmel,sama5d43
              - atmel,sama5d44
          - const: atmel,sama5d4
          - const: atmel,sama5

      - items:
          - const: microchip,sama7g5ek # SAMA7G5 Evaluation Kit
          - const: microchip,sama7g5
          - const: microchip,sama7

      - description: Microchip LAN9662 Evaluation Boards.
        items:
          - enum:
              - microchip,lan9662-pcb8291
              - microchip,lan9662-pcb8309
          - const: microchip,lan9662
          - const: microchip,lan966

      - description: Microchip LAN9668 PCB8290 Evaluation Board.
        items:
          - const: microchip,lan9668-pcb8290
          - const: microchip,lan9668
          - const: microchip,lan966

      - description: Kontron KSwitch D10 MMT series
        items:
          - enum:
              - kontron,kswitch-d10-mmt-8g
              - kontron,kswitch-d10-mmt-6g-2gs
          - const: kontron,s1921
          - const: microchip,lan9668
          - const: microchip,lan966

      - items:
          - enum:
              - atmel,sams70j19
              - atmel,sams70j20
              - atmel,sams70j21
              - atmel,sams70n19
              - atmel,sams70n20
              - atmel,sams70n21
              - atmel,sams70q19
              - atmel,sams70q20
              - atmel,sams70q21
          - const: atmel,sams70
          - const: atmel,samv7

      - items:
          - enum:
              - atmel,samv70j19
              - atmel,samv70j20
              - atmel,samv70n19
              - atmel,samv70n20
              - atmel,samv70q19
              - atmel,samv70q20
          - const: atmel,samv70
          - const: atmel,samv7

      - items:
          - enum:
              - atmel,samv71j19
              - atmel,samv71j20
              - atmel,samv71j21
              - atmel,samv71n19
              - atmel,samv71n20
              - atmel,samv71n21
              - atmel,samv71q19
              - atmel,samv71q20
              - atmel,samv71q21
          - const: atmel,samv71
          - const: atmel,samv7

additionalProperties: true

...
