# SPDX-License-Identifier: (GPL-2.0+ OR X11)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/marvell/armada-7k-8k.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Marvell Armada 7K/8K Platforms

maintainers:
  - <PERSON>LEMENT <<EMAIL>>

properties:
  $nodename:
    const: '/'
  compatible:
    oneOf:

      - description: Armada 7020 SoC
        items:
          - const: marvell,armada7020
          - const: marvell,armada-ap806-dual
          - const: marvell,armada-ap806

      - description: Armada 7040 SoC
        items:
          - const: marvell,armada7040
          - const: marvell,armada-ap806-quad
          - const: marvell,armada-ap806

      - description: Armada 8020 SoC
        items:
          - const: marvell,armada8020
          - const: marvell,armada-ap806-dual
          - const: marvell,armada-ap806

      - description: Armada 8040 SoC
        items:
          - const: marvell,armada8040
          - const: marvell,armada-ap806-quad
          - const: marvell,armada-ap806

      - description: Armada CN9130 SoC with no external CP
        items:
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description: Armada CN9131 SoC with one external CP
        items:
          - const: marvell,cn9131
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

      - description: Armada CN9132 SoC with two external CPs
        items:
          - const: marvell,cn9132
          - const: marvell,cn9131
          - const: marvell,cn9130
          - const: marvell,armada-ap807-quad
          - const: marvell,armada-ap807

additionalProperties: true
