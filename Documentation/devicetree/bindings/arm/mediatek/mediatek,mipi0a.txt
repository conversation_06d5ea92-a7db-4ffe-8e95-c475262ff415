Mediatek mipi0a (mipi_rx_ana_csi0a) controller
============================

The Mediatek mipi0a controller provides various clocks
to the system.

Required Properties:

- compatible: Should be one of:
	- "mediatek,mt6765-mipi0a", "syscon"
- #clock-cells: Must be 1

The mipi0a controller uses the common clk binding from
Documentation/devicetree/bindings/clock/clock-bindings.txt
The available clocks are defined in dt-bindings/clock/mt*-clk.h.

The mipi0a controller also uses the common power domain from
Documentation/devicetree/bindings/soc/mediatek/scpsys.txt
The available power domains are defined in dt-bindings/power/mt*-power.h.

Example:

mipi0a: clock-controller@11c10000 {
	compatible = "mediatek,mt6765-mipi0a", "syscon";
	reg = <0 0x11c10000 0 0x1000>;
	power-domains = <&scpsys MT6765_POWER_DOMAIN_CAM>;
	#clock-cells = <1>;
};
