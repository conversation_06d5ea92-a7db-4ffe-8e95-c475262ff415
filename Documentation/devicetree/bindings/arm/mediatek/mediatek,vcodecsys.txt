Mediatek vcodecsys controller
============================

The Mediatek vcodecsys controller provides various clocks to the system.

Required Properties:

- compatible: Should be one of:
	- "mediatek,mt6765-vcodecsys", "syscon"
- #clock-cells: Must be 1

The vcodecsys controller uses the common clk binding from
Documentation/devicetree/bindings/clock/clock-bindings.txt
The available clocks are defined in dt-bindings/clock/mt*-clk.h.

The vcodecsys controller also uses the common power domain from
Documentation/devicetree/bindings/soc/mediatek/scpsys.txt
The available power domains are defined in dt-bindings/power/mt*-power.h.

Example:

venc_gcon: clock-controller@17000000 {
	compatible = "mediatek,mt6765-vcodecsys", "syscon";
	reg = <0 0x17000000 0 0x10000>;
	power-domains = <&scpsys MT6765_POWER_DOMAIN_VCODEC>;
	#clock-cells = <1>;
};
