# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/mediatek/mediatek,mmsys.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek mmsys controller

maintainers:
  - <PERSON> <<EMAIL>>

description:
  The MediaTek mmsys system controller provides clock control, routing control,
  and miscellaneous control in mmsys partition.

properties:
  $nodename:
    pattern: "^syscon@[0-9a-f]+$"

  compatible:
    oneOf:
      - items:
          - enum:
              - mediatek,mt2701-mmsys
              - mediatek,mt2712-mmsys
              - mediatek,mt6765-mmsys
              - mediatek,mt6779-mmsys
              - mediatek,mt6795-mmsys
              - mediatek,mt6797-mmsys
              - mediatek,mt8167-mmsys
              - mediatek,mt8173-mmsys
              - mediatek,mt8183-mmsys
              - mediatek,mt8186-mmsys
              - mediatek,mt8188-vdosys0
              - mediatek,mt8192-mmsys
              - mediatek,mt8195-vdosys1
              - mediatek,mt8195-vppsys0
              - mediatek,mt8195-vppsys1
              - mediatek,mt8365-mmsys
          - const: syscon

      - description: vdosys0 and vdosys1 are 2 display HW pipelines,
                     so mt8195 binding should be deprecated.
        deprecated: true
        items:
          - const: mediatek,mt8195-mmsys
          - const: syscon

      - items:
          - const: mediatek,mt7623-mmsys
          - const: mediatek,mt2701-mmsys
          - const: syscon

      - items:
          - const: mediatek,mt8195-vdosys0
          - const: mediatek,mt8195-mmsys
          - const: syscon

  reg:
    maxItems: 1

  power-domains:
    description:
      A phandle and PM domain specifier as defined by bindings
      of the power controller specified by phandle. See
      Documentation/devicetree/bindings/power/power-domain.yaml for details.

  mboxes:
    description:
      Using mailbox to communicate with GCE, it should have this
      property and list of phandle, mailbox specifiers. See
      Documentation/devicetree/bindings/mailbox/mediatek,gce-mailbox.yaml
      for details.
    $ref: /schemas/types.yaml#/definitions/phandle-array

  mediatek,gce-client-reg:
    description:
      The register of client driver can be configured by gce with 4 arguments
      defined in this property, such as phandle of gce, subsys id,
      register offset and size.
      Each subsys id is mapping to a base address of display function blocks
      register which is defined in the gce header
      include/dt-bindings/gce/<chip>-gce.h.
    $ref: /schemas/types.yaml#/definitions/phandle-array
    maxItems: 1

  "#clock-cells":
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - reg
  - "#clock-cells"

additionalProperties: false

examples:
  - |
    #include <dt-bindings/power/mt8173-power.h>
    #include <dt-bindings/gce/mt8173-gce.h>

    mmsys: syscon@14000000 {
        compatible = "mediatek,mt8173-mmsys", "syscon";
        reg = <0x14000000 0x1000>;
        power-domains = <&spm MT8173_POWER_DOMAIN_MM>;
        #clock-cells = <1>;
        #reset-cells = <1>;
        mboxes = <&gce 0 CMDQ_THR_PRIO_HIGHEST>,
                 <&gce 1 CMDQ_THR_PRIO_HIGHEST>;
        mediatek,gce-client-reg = <&gce SUBSYS_1400XXXX 0 0x1000>;
    };
