# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/mediatek/mediatek,pericfg.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek Peripheral Configuration Controller

maintainers:
  - <PERSON><PERSON><PERSON> <b<PERSON><PERSON><PERSON><PERSON>@baylibre.com>

description:
  The Mediatek pericfg controller provides various clocks and reset outputs
  to the system.

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - mediatek,mt2701-pericfg
              - mediatek,mt2712-pericfg
              - mediatek,mt6765-pericfg
              - mediatek,mt6795-pericfg
              - mediatek,mt7622-pericfg
              - mediatek,mt7629-pericfg
              - mediatek,mt8135-pericfg
              - mediatek,mt8173-pericfg
              - mediatek,mt8183-pericfg
              - mediatek,mt8186-pericfg
              - mediatek,mt8195-pericfg
              - mediatek,mt8516-pericfg
          - const: syscon
      - items:
          # Special case for mt7623 for backward compatibility
          - const: mediatek,mt7623-pericfg
          - const: mediatek,mt2701-pericfg
          - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    pericfg@10003000 {
        compatible = "mediatek,mt8173-pericfg", "syscon";
        reg = <0x10003000 0x1000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
    };

  - |
    pericfg@10003000 {
        compatible =  "mediatek,mt7623-pericfg", "mediatek,mt2701-pericfg", "syscon";
        reg = <0x10003000 0x1000>;
        #clock-cells = <1>;
        #reset-cells = <1>;
    };
