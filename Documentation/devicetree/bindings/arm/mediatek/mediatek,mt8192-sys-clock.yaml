# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/mediatek/mediatek,mt8192-sys-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek System Clock Controller for MT8192

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The Mediatek system clock controller provides various clocks and system configuration
  like reset and bus protection on MT8192.

properties:
  compatible:
    items:
      - enum:
          - mediatek,mt8192-topckgen
          - mediatek,mt8192-infracfg
          - mediatek,mt8192-pericfg
          - mediatek,mt8192-apmixedsys
      - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    topckgen: syscon@10000000 {
        compatible = "mediatek,mt8192-topckgen", "syscon";
        reg = <0x10000000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    infracfg: syscon@10001000 {
        compatible = "mediatek,mt8192-infracfg", "syscon";
        reg = <0x10001000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    pericfg: syscon@10003000 {
        compatible = "mediatek,mt8192-pericfg", "syscon";
        reg = <0x10003000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    apmixedsys: syscon@1000c000 {
        compatible = "mediatek,mt8192-apmixedsys", "syscon";
        reg = <0x1000c000 0x1000>;
        #clock-cells = <1>;
    };
