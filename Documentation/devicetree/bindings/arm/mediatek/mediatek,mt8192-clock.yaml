# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/mediatek/mediatek,mt8192-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek Functional Clock Controller for MT8192

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The Mediatek functional clock controller provides various clocks on MT8192.

properties:
  compatible:
    items:
      - enum:
          - mediatek,mt8192-scp_adsp
          - mediatek,mt8192-imp_iic_wrap_c
          - mediatek,mt8192-imp_iic_wrap_e
          - mediatek,mt8192-imp_iic_wrap_s
          - mediatek,mt8192-imp_iic_wrap_ws
          - mediatek,mt8192-imp_iic_wrap_w
          - mediatek,mt8192-imp_iic_wrap_n
          - mediatek,mt8192-msdc_top
          - mediatek,mt8192-mfgcfg
          - mediatek,mt8192-imgsys
          - mediatek,mt8192-imgsys2
          - mediatek,mt8192-vdecsys_soc
          - mediatek,mt8192-vdecsys
          - mediatek,mt8192-vencsys
          - mediatek,mt8192-camsys
          - mediatek,mt8192-camsys_rawa
          - mediatek,mt8192-camsys_rawb
          - mediatek,mt8192-camsys_rawc
          - mediatek,mt8192-ipesys
          - mediatek,mt8192-mdpsys

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    scp_adsp: clock-controller@10720000 {
        compatible = "mediatek,mt8192-scp_adsp";
        reg = <0x10720000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imp_iic_wrap_c: clock-controller@11007000 {
        compatible = "mediatek,mt8192-imp_iic_wrap_c";
        reg = <0x11007000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imp_iic_wrap_e: clock-controller@11cb1000 {
        compatible = "mediatek,mt8192-imp_iic_wrap_e";
        reg = <0x11cb1000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imp_iic_wrap_s: clock-controller@11d03000 {
        compatible = "mediatek,mt8192-imp_iic_wrap_s";
        reg = <0x11d03000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imp_iic_wrap_ws: clock-controller@11d23000 {
        compatible = "mediatek,mt8192-imp_iic_wrap_ws";
        reg = <0x11d23000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imp_iic_wrap_w: clock-controller@11e01000 {
        compatible = "mediatek,mt8192-imp_iic_wrap_w";
        reg = <0x11e01000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imp_iic_wrap_n: clock-controller@11f02000 {
        compatible = "mediatek,mt8192-imp_iic_wrap_n";
        reg = <0x11f02000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    msdc_top: clock-controller@11f10000 {
        compatible = "mediatek,mt8192-msdc_top";
        reg = <0x11f10000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    mfgcfg: clock-controller@13fbf000 {
        compatible = "mediatek,mt8192-mfgcfg";
        reg = <0x13fbf000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imgsys: clock-controller@15020000 {
        compatible = "mediatek,mt8192-imgsys";
        reg = <0x15020000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    imgsys2: clock-controller@15820000 {
        compatible = "mediatek,mt8192-imgsys2";
        reg = <0x15820000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    vdecsys_soc: clock-controller@1600f000 {
        compatible = "mediatek,mt8192-vdecsys_soc";
        reg = <0x1600f000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    vdecsys: clock-controller@1602f000 {
        compatible = "mediatek,mt8192-vdecsys";
        reg = <0x1602f000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    vencsys: clock-controller@17000000 {
        compatible = "mediatek,mt8192-vencsys";
        reg = <0x17000000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    camsys: clock-controller@1a000000 {
        compatible = "mediatek,mt8192-camsys";
        reg = <0x1a000000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    camsys_rawa: clock-controller@1a04f000 {
        compatible = "mediatek,mt8192-camsys_rawa";
        reg = <0x1a04f000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    camsys_rawb: clock-controller@1a06f000 {
        compatible = "mediatek,mt8192-camsys_rawb";
        reg = <0x1a06f000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    camsys_rawc: clock-controller@1a08f000 {
        compatible = "mediatek,mt8192-camsys_rawc";
        reg = <0x1a08f000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    ipesys: clock-controller@1b000000 {
        compatible = "mediatek,mt8192-ipesys";
        reg = <0x1b000000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    mdpsys: clock-controller@1f000000 {
        compatible = "mediatek,mt8192-mdpsys";
        reg = <0x1f000000 0x1000>;
        #clock-cells = <1>;
    };
