Mediatek ethsys controller
============================

The Mediatek ethsys controller provides various clocks to the system.

Required Properties:

- compatible: Should be:
	- "mediatek,mt2701-ethsys", "syscon"
	- "mediatek,mt7622-ethsys", "syscon"
	- "mediatek,mt7623-ethsys", "mediatek,mt2701-ethsys", "syscon"
	- "mediatek,mt7629-ethsys", "syscon"
	- "mediatek,mt7981-ethsys", "syscon"
	- "mediatek,mt7986-ethsys", "syscon"
- #clock-cells: Must be 1
- #reset-cells: Must be 1

The ethsys controller uses the common clk binding from
Documentation/devicetree/bindings/clock/clock-bindings.txt
The available clocks are defined in dt-bindings/clock/mt*-clk.h.

Example:

ethsys: clock-controller@1b000000 {
	compatible = "mediatek,mt2701-ethsys", "syscon";
	reg = <0 0x1b000000 0 0x1000>;
	#clock-cells = <1>;
	#reset-cells = <1>;
};
