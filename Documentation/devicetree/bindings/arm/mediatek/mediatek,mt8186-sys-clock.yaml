# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/mediatek/mediatek,mt8186-sys-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek System Clock Controller for MT8186

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The clock architecture in MediaTek like below
  PLLs -->
          dividers -->
                      muxes
                           -->
                              clock gate

  The apmixedsys provides most of PLLs which generated from SoC 26m.
  The topckgen provides dividers and muxes which provide the clock source to other IP blocks.
  The infracfg_ao provides clock gate in peripheral and infrastructure IP blocks.
  The mcusys provides mux control to select the clock source in AP MCU.
  The device nodes also provide the system control capacity for configuration.

properties:
  compatible:
    items:
      - enum:
          - mediatek,mt8186-mcusys
          - mediatek,mt8186-topckgen
          - mediatek,mt8186-infracfg_ao
          - mediatek,mt8186-apmixedsys
      - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    topckgen: syscon@10000000 {
        compatible = "mediatek,mt8186-topckgen", "syscon";
        reg = <0x10000000 0x1000>;
        #clock-cells = <1>;
    };
