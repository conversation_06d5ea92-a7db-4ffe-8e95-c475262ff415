# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/mediatek/mediatek,mt8195-sys-clock.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: MediaTek System Clock Controller for MT8195

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description:
  The clock architecture in Mediatek like below
  PLLs -->
          dividers -->
                      muxes
                           -->
                              clock gate

  The apmixedsys provides most of PLLs which generated from SoC 26m.
  The topckgen provides dividers and muxes which provide the clock source to other IP blocks.
  The infracfg_ao and pericfg_ao provides clock gate in peripheral and infrastructure IP blocks.

properties:
  compatible:
    items:
      - enum:
          - mediatek,mt8195-topckgen
          - mediatek,mt8195-infracfg_ao
          - mediatek,mt8195-apmixedsys
          - mediatek,mt8195-pericfg_ao
      - const: syscon

  reg:
    maxItems: 1

  '#clock-cells':
    const: 1

  '#reset-cells':
    const: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    topckgen: syscon@10000000 {
        compatible = "mediatek,mt8195-topckgen", "syscon";
        reg = <0x10000000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    infracfg_ao: syscon@10001000 {
        compatible = "mediatek,mt8195-infracfg_ao", "syscon";
        reg = <0x10001000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    apmixedsys: syscon@1000c000 {
        compatible = "mediatek,mt8195-apmixedsys", "syscon";
        reg = <0x1000c000 0x1000>;
        #clock-cells = <1>;
    };

  - |
    pericfg_ao: syscon@11003000 {
        compatible = "mediatek,mt8195-pericfg_ao", "syscon";
        reg = <0x11003000 0x1000>;
        #clock-cells = <1>;
    };
