# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
# Copyright 2019 Bay<PERSON>ibre, SAS
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/amlogic/amlogic,meson-gx-ao-secure.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Amlogic Meson Firmware registers Interface

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Meson SoCs have a register bank with status and data shared with the
  secure firmware.

# We need a select here so we don't match all nodes with 'syscon'
select:
  properties:
    compatible:
      contains:
        const: amlogic,meson-gx-ao-secure
  required:
    - compatible

properties:
  compatible:
    items:
      - const: amlogic,meson-gx-ao-secure
      - const: syscon

  reg:
    maxItems: 1

  amlogic,has-chip-id:
    description: |
      A firmware register encodes the SoC type, package and revision
      information on the Meson GX SoCs. If present, the interface gives
      the current SoC version.
    type: boolean

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    ao-secure@140 {
          compatible = "amlogic,meson-gx-ao-secure", "syscon";
          reg = <0x140 0x140>;
          amlogic,has-chip-id;
    };
