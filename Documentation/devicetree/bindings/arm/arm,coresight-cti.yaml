# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
# Copyright 2019 Linaro Ltd.
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/arm,coresight-cti.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM Coresight Cross Trigger Interface (CTI) device.

description: |
  The CoreSight Embedded Cross Trigger (ECT) consists of CTI devices connected
  to one or more CoreSight components and/or a CPU, with CTIs interconnected in
  a star topology via the Cross Trigger Matrix (CTM), which is not programmable.
  The ECT components are not part of the trace generation data path and are thus
  not part of the CoreSight graph.

  The CTI component properties define the connections between the individual
  CTI and the components it is directly connected to, consisting of input and
  output hardware trigger signals. CTIs can have a maximum number of input and
  output hardware trigger signals (8 each for v1 CTI, 32 each for v2 CTI). The
  number is defined at design time, the maximum of each defined in the DEVID
  register.

  CTIs are interconnected in a star topology via the CTM, using a number of
  programmable channels, usually 4, but again implementation defined and
  described in the DEVID register. The star topology is not required to be
  described in the bindings as the actual connections are software
  programmable.

  In general the connections between CTI and components via the trigger signals
  are implementation defined, except when the CTI is connected to an ARM v8
  architecture core and optional ETM.

  In this case the ARM v8 architecture defines the required signal connections
  between CTI and the CPU core and ETM if present. In the case of a v8
  architecturally connected CTI an additional compatible string is used to
  indicate this feature (arm,coresight-cti-v8-arch).

  When CTI trigger connection information is unavailable then a minimal driver
  binding can be declared with no explicit trigger signals. This will result
  the driver detecting the maximum available triggers and channels from the
  DEVID register and make them all available for use as a single default
  connection. Any user / client application will require additional information
  on the connections between the CTI and other components for correct operation.
  This information might be found by enabling the Integration Test registers in
  the driver (set CONFIG_CORESIGHT_CTI_INTEGRATION_TEST in Kernel
  configuration). These registers may be used to explore the trigger connections
  between CTI and other CoreSight components.

  Certain triggers between CoreSight devices and the CTI have specific types
  and usages. These can be defined along with the signal indexes with the
  constants defined in <dt-bindings/arm/coresight-cti-dt.h>

  For example a CTI connected to a core will usually have a DBGREQ signal. This
  is defined in the binding as type PE_EDBGREQ. These types will appear in an
  optional array alongside the signal indexes. Omitting types will default all
  signals to GEN_IO.

  Note that some hardware trigger signals can be connected to non-CoreSight
  components (e.g. UART etc) depending on hardware implementation.

maintainers:
  - Mike Leach <<EMAIL>>

allOf:
  - $ref: /schemas/arm/primecell.yaml#

# Need a custom select here or 'arm,primecell' will match on lots of nodes
select:
  properties:
    compatible:
      contains:
        enum:
          - arm,coresight-cti
  required:
    - compatible

properties:
  $nodename:
    pattern: "^cti(@[0-9a-f]+)$"
  compatible:
    oneOf:
      - items:
          - const: arm,coresight-cti
          - const: arm,primecell
      - items:
          - const: arm,coresight-cti-v8-arch
          - const: arm,coresight-cti
          - const: arm,primecell

  reg:
    maxItems: 1

  cpu:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      Handle to cpu this device is associated with. This must appear in the
      base cti node if compatible string arm,coresight-cti-v8-arch is used,
      or may appear in a trig-conns child node when appropriate.

  power-domains:
    maxItems: 1

  arm,cti-ctm-id:
    $ref: /schemas/types.yaml#/definitions/uint32
    description:
      Defines the CTM this CTI is connected to, in large systems with multiple
      separate CTI/CTM nets. Typically multi-socket systems where the CTM is
      propagated between sockets.

  arm,cs-dev-assoc:
    $ref: /schemas/types.yaml#/definitions/phandle
    description:
      defines a phandle reference to an associated CoreSight trace device.
      When the associated trace device is enabled, then the respective CTI
      will be enabled. Use in a trig-conns node, or in CTI base node when
      compatible string arm,coresight-cti-v8-arch used. If the associated
      device has not been registered then the node name will be stored as
      the connection name for later resolution. If the associated device is
      not a CoreSight device or not registered then the node name will remain
      the connection name and automatic enabling will not occur.

  # size cells and address cells required if trig-conns node present.
  "#size-cells":
    const: 0

  "#address-cells":
    const: 1

patternProperties:
  '^trig-conns@([0-9]+)$':
    type: object
    description:
      A trigger connections child node which describes the trigger signals
      between this CTI and another hardware device. This device may be a CPU,
      CoreSight device, any other hardware device or simple external IO lines.
      The connection may have both input and output triggers, or only one or the
      other.

    properties:
      reg:
        maxItems: 1

      arm,trig-in-sigs:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 32
        description:
          List of CTI trigger in signal numbers in use by a trig-conns node.

      arm,trig-in-types:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 32
        description:
          List of constants representing the types for the CTI trigger in
          signals. Types in this array match to the corresponding signal in the
          arm,trig-in-sigs array. If the -types array is smaller, or omitted
          completely, then the types will default to GEN_IO.

      arm,trig-out-sigs:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 32
        description:
          List of CTI trigger out signal numbers in use by a trig-conns node.

      arm,trig-out-types:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 32
        description:
          List of constants representing the types for the CTI trigger out
          signals. Types in this array match to the corresponding signal
          in the arm,trig-out-sigs array. If the "-types" array is smaller,
          or omitted completely, then the types will default to GEN_IO.

      arm,trig-filters:
        $ref: /schemas/types.yaml#/definitions/uint32-array
        minItems: 1
        maxItems: 32
        description:
          List of CTI trigger out signals that will be blocked from becoming
          active, unless filtering is disabled on the driver.

      arm,trig-conn-name:
        $ref: /schemas/types.yaml#/definitions/string
        description:
          Defines a connection name that will be displayed, if the cpu or
          arm,cs-dev-assoc properties are not being used in this connection.
          Principle use for CTI that are connected to non-CoreSight devices, or
          external IO.

    anyOf:
      - required:
          - arm,trig-in-sigs
      - required:
          - arm,trig-out-sigs
    oneOf:
      - required:
          - arm,trig-conn-name
      - required:
          - cpu
      - required:
          - arm,cs-dev-assoc
    required:
      - reg

required:
  - compatible
  - reg
  - clocks
  - clock-names

if:
  properties:
    compatible:
      contains:
        const: arm,coresight-cti-v8-arch

then:
  required:
    - cpu

unevaluatedProperties: false

examples:
  # minimum CTI definition. DEVID register used to set number of triggers.
  - |
    cti@20020000 {
      compatible = "arm,coresight-cti", "arm,primecell";
      reg = <0x20020000 0x1000>;

      clocks = <&soc_smc50mhz>;
      clock-names = "apb_pclk";
    };
  #  v8 architecturally defined CTI - CPU + ETM connections generated by the
  #  driver according to the v8 architecture specification.
  - |
    cti@859000 {
      compatible = "arm,coresight-cti-v8-arch", "arm,coresight-cti",
                   "arm,primecell";
      reg = <0x859000 0x1000>;

      clocks = <&soc_smc50mhz>;
      clock-names = "apb_pclk";

      cpu = <&CPU1>;
      arm,cs-dev-assoc = <&etm1>;
    };
  # Implementation defined CTI - CPU + ETM connections explicitly defined..
  # Shows use of type constants from dt-bindings/arm/coresight-cti-dt.h
  # #size-cells and #address-cells are required if trig-conns@ nodes present.
  - |
    #include <dt-bindings/arm/coresight-cti-dt.h>

    cti@858000 {
      compatible = "arm,coresight-cti", "arm,primecell";
      reg = <0x858000 0x1000>;

      clocks = <&soc_smc50mhz>;
      clock-names = "apb_pclk";

      arm,cti-ctm-id = <1>;

      #address-cells = <1>;
      #size-cells = <0>;

      trig-conns@0 {
            reg = <0>;
            arm,trig-in-sigs = <4 5 6 7>;
            arm,trig-in-types = <ETM_EXTOUT
                                 ETM_EXTOUT
                                 ETM_EXTOUT
                                 ETM_EXTOUT>;
            arm,trig-out-sigs = <4 5 6 7>;
            arm,trig-out-types = <ETM_EXTIN
                                  ETM_EXTIN
                                  ETM_EXTIN
                                  ETM_EXTIN>;
            arm,cs-dev-assoc = <&etm0>;
      };

      trig-conns@1 {
            reg = <1>;
            cpu = <&CPU0>;
            arm,trig-in-sigs = <0 1>;
            arm,trig-in-types = <PE_DBGTRIGGER
                                 PE_PMUIRQ>;
            arm,trig-out-sigs = <0 1 2 >;
            arm,trig-out-types = <PE_EDBGREQ
                                  PE_DBGRESTART
                                  PE_CTIIRQ>;

            arm,trig-filters = <0>;
      };
    };
  # Implementation defined CTI - non CoreSight component connections.
  - |
    cti@20110000 {
      compatible = "arm,coresight-cti", "arm,primecell";
      reg = <0x20110000 0x1000>;

      clocks = <&soc_smc50mhz>;
      clock-names = "apb_pclk";

      #address-cells = <1>;
      #size-cells = <0>;

      trig-conns@0 {
        reg = <0>;
        arm,trig-in-sigs = <0>;
        arm,trig-in-types = <GEN_INTREQ>;
        arm,trig-out-sigs = <0>;
        arm,trig-out-types = <GEN_HALTREQ>;
        arm,trig-conn-name = "sys_profiler";
      };

      trig-conns@1 {
        reg = <1>;
        arm,trig-out-sigs = <2 3>;
        arm,trig-out-types = <GEN_HALTREQ GEN_RESTARTREQ>;
        arm,trig-conn-name = "watchdog";
      };

      trig-conns@2 {
        reg = <2>;
        arm,trig-in-sigs = <1 6>;
        arm,trig-in-types = <GEN_HALTREQ GEN_RESTARTREQ>;
        arm,trig-conn-name = "g_counter";
      };
    };

...
