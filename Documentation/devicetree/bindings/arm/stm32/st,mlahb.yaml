# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/stm32/st,mlahb.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 ML-AHB interconnect

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  These bindings describe the STM32 SoCs ML-AHB interconnect bus which connects
  a Cortex-M subsystem with dedicated memories. The MCU SRAM and RETRAM memory
  parts can be accessed through different addresses (see "RAM aliases" in [1])
  using different buses (see [2]): balancing the Cortex-M firmware accesses
  among those ports allows to tune the system performance.
  [1]: https://www.st.com/resource/en/reference_manual/dm00327659.pdf
  [2]: https://wiki.st.com/stm32mpu/wiki/STM32MP15_RAM_mapping

allOf:
  - $ref: /schemas/simple-bus.yaml#

properties:
  compatible:
    contains:
      enum:
        - st,mlahb

  dma-ranges:
    description: |
      Describe memory addresses translation between the local CPU and the
      remote Cortex-M processor. Each memory region, is declared with
      3 parameters:
      - param 1: device base address (Cortex-M processor address)
      - param 2: physical base address (local CPU address)
      - param 3: size of the memory region.
    maxItems: 3

  '#address-cells':
    const: 1

  '#size-cells':
    const: 1

required:
  - compatible
  - '#address-cells'
  - '#size-cells'
  - dma-ranges

unevaluatedProperties: false

examples:
  - |
    mlahb: ahb@38000000 {
      compatible = "st,mlahb", "simple-bus";
      #address-cells = <1>;
      #size-cells = <1>;
      reg = <0x10000000 0x40000>;
      ranges;
      dma-ranges = <0x00000000 0x38000000 0x10000>,
                   <0x10000000 0x10000000 0x60000>,
                   <0x30000000 0x30000000 0x60000>;

      m4_rproc: m4@10000000 {
       reg = <0x10000000 0x40000>;
      };
    };

...
