# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/stm32/stm32.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: STMicroelectronics STM32 Platforms

maintainers:
  - <PERSON> <<EMAIL>>

properties:
  $nodename:
    const: "/"
  compatible:
    oneOf:
      - description: emtrion STM32MP1 Argon based Boards
        items:
          - const: emtrion,stm32mp157c-emsbc-argon
          - const: emtrion,stm32mp157c-emstamp-argon
          - const: st,stm32mp157
      - items:
          - enum:
              - st,stm32f429i-disco
              - st,stm32429i-eval
          - const: st,stm32f429
      - items:
          - enum:
              - st,stm32f469i-disco
          - const: st,stm32f469
      - items:
          - enum:
              - st,stm32f746-disco
              - st,stm32746g-eval
          - const: st,stm32f746
      - items:
          - enum:
              - st,stm32f769-disco
          - const: st,stm32f769
      - items:
          - enum:
              - st,stm32h743i-disco
              - st,stm32h743i-eval
          - const: st,stm32h743
      - items:
          - enum:
              - st,stm32h750i-art-pi
          - const: st,stm32h750
      - items:
          - enum:
              - st,stm32mp135f-dk
          - const: st,stm32mp135

      - description: ST STM32MP151 based Boards
        items:
          - enum:
              - prt,prtt1a   # Protonic PRTT1A
              - prt,prtt1c   # Protonic PRTT1C
              - prt,prtt1s   # Protonic PRTT1S
          - const: st,stm32mp151

      - description: DH STM32MP151 DHCOR SoM based Boards
        items:
          - const: dh,stm32mp151a-dhcor-testbench
          - const: dh,stm32mp151a-dhcor-som
          - const: st,stm32mp151

      - description: DH STM32MP153 DHCOM SoM based Boards
        items:
          - const: dh,stm32mp153c-dhcom-drc02
          - const: dh,stm32mp153c-dhcom-som
          - const: st,stm32mp153

      - description: DH STM32MP153 DHCOR SoM based Boards
        items:
          - const: dh,stm32mp153c-dhcor-drc-compact
          - const: dh,stm32mp153c-dhcor-som
          - const: st,stm32mp153

      - items:
          - enum:
              - shiratech,stm32mp157a-iot-box # IoT Box
              - shiratech,stm32mp157a-stinger96 # Stinger96
              - st,stm32mp157c-ed1
              - st,stm32mp157a-dk1
              - st,stm32mp157c-dk2
          - const: st,stm32mp157

      - items:
          - const: st,stm32mp157a-dk1-scmi
          - const: st,stm32mp157a-dk1
          - const: st,stm32mp157
      - items:
          - const: st,stm32mp157c-dk2-scmi
          - const: st,stm32mp157c-dk2
          - const: st,stm32mp157
      - items:
          - const: st,stm32mp157c-ed1-scmi
          - const: st,stm32mp157c-ed1
          - const: st,stm32mp157
      - items:
          - const: st,stm32mp157c-ev1
          - const: st,stm32mp157c-ed1
          - const: st,stm32mp157
      - items:
          - const: st,stm32mp157c-ev1-scmi
          - const: st,stm32mp157c-ev1
          - const: st,stm32mp157c-ed1
          - const: st,stm32mp157

      - description: DH STM32MP1 SoM based Boards
        items:
          - enum:
              - arrow,stm32mp157a-avenger96 # Avenger96
          - const: dh,stm32mp157a-dhcor-som
          - const: st,stm32mp157

      - description: DH STM32MP1 SoM based Boards
        items:
          - enum:
              - dh,stm32mp157c-dhcom-pdk2
              - dh,stm32mp157c-dhcom-picoitx
          - const: dh,stm32mp157c-dhcom-som
          - const: st,stm32mp157

      - description: Engicam i.Core STM32MP1 SoM based Boards
        items:
          - enum:
              - engicam,icore-stm32mp1-ctouch2       # STM32MP1 Engicam i.Core STM32MP1 C.TOUCH 2.0
              - engicam,icore-stm32mp1-ctouch2-of10  # STM32MP1 Engicam i.Core STM32MP1 C.TOUCH 2.0 10.1" OF
              - engicam,icore-stm32mp1-edimm2.2      # STM32MP1 Engicam i.Core STM32MP1 EDIMM2.2 Starter Kit
          - const: engicam,icore-stm32mp1            # STM32MP1 Engicam i.Core STM32MP1 SoM
          - const: st,stm32mp157

      - description: Engicam MicroGEA STM32MP1 SoM based Boards
        items:
          - enum:
              - engicam,microgea-stm32mp1-microdev2.0
              - engicam,microgea-stm32mp1-microdev2.0-of7
          - const: engicam,microgea-stm32mp1
          - const: st,stm32mp157

      - description: Octavo OSD32MP15x System-in-Package based boards
        items:
          - enum:
              - lxa,stm32mp157c-mc1      # Linux Automation MC-1
              - lxa,stm32mp157c-tac-gen1 # Linux Automation TAC (Generation 1)
              - lxa,stm32mp157c-tac-gen2 # Linux Automation TAC (Generation 2)
          - const: oct,stm32mp15xx-osd32
          - enum:
              - st,stm32mp157

      - description: Odyssey STM32MP1 SoM based Boards
        items:
          - enum:
              - seeed,stm32mp157c-odyssey
          - const: seeed,stm32mp157c-odyssey-som
          - const: st,stm32mp157

      - description: Phytec STM32MP1 SoM based Boards
        items:
          - const: phytec,phycore-stm32mp1-3
          - const: phytec,phycore-stm32mp157c-som
          - const: st,stm32mp157

      - description: ST STM32MP257 based Boards
        items:
          - enum:
              - st,stm32mp257f-ev1
          - const: st,stm32mp257

additionalProperties: true

...
