# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
# Copyright (c) 2023 Qualcomm Innovation Center, Inc. All rights reserved.
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/qcom,coresight-tpdm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Trace, Profiling and Diagnostics Monitor - TPDM

description: |
  The TPDM or Monitor serves as data collection component for various dataset
  types specified in the QPMDA spec. It covers Implementation defined ((ImplDef),
  Basic Counts (BC), Tenure Counts (TC), Continuous Multi-Bit (CMB), and Discrete
  Single Bit (DSB). It performs data collection in the data producing clock
  domain and transfers it to the data collection time domain, generally ATB
  clock domain.

  The primary use case of the TPDM is to collect data from different data
  sources and send it to a TPDA for packetization, timestamping, and funneling.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

# Need a custom select here or 'arm,primecell' will match on lots of nodes
select:
  properties:
    compatible:
      contains:
        enum:
          - qcom,coresight-tpdm
  required:
    - compatible

properties:
  $nodename:
    pattern: "^tpdm(@[0-9a-f]+)$"
  compatible:
    items:
      - const: qcom,coresight-tpdm
      - const: arm,primecell

  reg:
    minItems: 1
    maxItems: 2

  clocks:
    maxItems: 1

  clock-names:
    items:
      - const: apb_pclk

  out-ports:
    description: |
      Output connections from the TPDM to coresight funnel/TPDA.
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port:
        description: Output connection from the TPDM to coresight
            funnel/TPDA.
        $ref: /schemas/graph.yaml#/properties/port

required:
  - compatible
  - reg
  - clocks
  - clock-names

additionalProperties: false

examples:
  # minimum TPDM definition. TPDM connect to coresight TPDA.
  - |
    tpdm@684c000 {
      compatible = "qcom,coresight-tpdm", "arm,primecell";
      reg = <0x0684c000 0x1000>;

      clocks = <&aoss_qmp>;
      clock-names = "apb_pclk";

      out-ports {
        port {
          tpdm_prng_out_tpda_qdss: endpoint {
            remote-endpoint =
              <&tpda_qdss_in_tpdm_prng>;
          };
        };
      };
    };

...
