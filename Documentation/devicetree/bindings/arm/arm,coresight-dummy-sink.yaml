# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/arm,coresight-dummy-sink.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM Coresight Dummy sink component

description: |
  CoreSight components are compliant with the ARM CoreSight architecture
  specification and can be connected in various topologies to suit a particular
  SoCs tracing needs. These trace components can generally be classified as
  sinks, links and sources. Trace data produced by one or more sources flows
  through the intermediate links connecting the source to the currently selected
  sink.

  The Coresight dummy sink component is for the specific coresight sink devices
  kernel don't have permission to access or configure, e.g., CoreSight EUD on
  Qualcomm platforms. It is a mini-USB hub implemented to support the USB-based
  debug and trace capabilities. For this device, a dummy driver is needed to
  register it as Coresight sink device in kernel side, so that path can be
  created in the driver. Then the trace flow would be transferred to EUD via
  coresight link of AP processor. It provides Coresight API for operations on
  dummy source devices, such as enabling and disabling them. It also provides
  the Coresight dummy source paths for debugging.

  The primary use case of the coresight dummy sink is to build path in kernel
  side for dummy sink component.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> Po<PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - Mao Jinlong <<EMAIL>>
  - Hao Zhang <<EMAIL>>

properties:
  compatible:
    enum:
      - arm,coresight-dummy-sink

  in-ports:
    $ref: /schemas/graph.yaml#/properties/ports

    properties:
      port:
        description: Input connection from the Coresight Trace bus to
          dummy sink, such as Embedded USB debugger(EUD).

        $ref: /schemas/graph.yaml#/properties/port

required:
  - compatible
  - in-ports

additionalProperties: false

examples:
  # Minimum dummy sink definition. Dummy sink connect to coresight replicator.
  - |
    sink {
      compatible = "arm,coresight-dummy-sink";

      in-ports {
        port {
          eud_in_replicator_swao: endpoint {
            remote-endpoint = <&replicator_swao_out_eud>;
          };
        };
      };
    };

...
