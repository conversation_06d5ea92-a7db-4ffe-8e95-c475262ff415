# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/arm/pmu.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: ARM Performance Monitor Units

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |+
  ARM cores often have a PMU for counting cpu and cache events like cache misses
  and hits. The interface to the PMU is part of the ARM ARM. The ARM PMU
  representation in the device tree should be done as under:-

properties:
  compatible:
    items:
      - enum:
          - apm,potenza-pmu
          - apple,avalanche-pmu
          - apple,blizzard-pmu
          - apple,firestorm-pmu
          - apple,icestorm-pmu
          - arm,armv8-pmuv3 # Only for s/w models
          - arm,arm1136-pmu
          - arm,arm1176-pmu
          - arm,arm11mpcore-pmu
          - arm,cortex-a5-pmu
          - arm,cortex-a7-pmu
          - arm,cortex-a8-pmu
          - arm,cortex-a9-pmu
          - arm,cortex-a12-pmu
          - arm,cortex-a15-pmu
          - arm,cortex-a17-pmu
          - arm,cortex-a32-pmu
          - arm,cortex-a34-pmu
          - arm,cortex-a35-pmu
          - arm,cortex-a53-pmu
          - arm,cortex-a55-pmu
          - arm,cortex-a57-pmu
          - arm,cortex-a65-pmu
          - arm,cortex-a72-pmu
          - arm,cortex-a73-pmu
          - arm,cortex-a75-pmu
          - arm,cortex-a76-pmu
          - arm,cortex-a77-pmu
          - arm,cortex-a78-pmu
          - arm,cortex-a510-pmu
          - arm,cortex-a520-pmu
          - arm,cortex-a710-pmu
          - arm,cortex-a715-pmu
          - arm,cortex-a720-pmu
          - arm,cortex-x1-pmu
          - arm,cortex-x2-pmu
          - arm,cortex-x3-pmu
          - arm,cortex-x4-pmu
          - arm,neoverse-e1-pmu
          - arm,neoverse-n1-pmu
          - arm,neoverse-n2-pmu
          - arm,neoverse-v1-pmu
          - brcm,vulcan-pmu
          - cavium,thunder-pmu
          - nvidia,denver-pmu
          - nvidia,carmel-pmu
          - qcom,krait-pmu
          - qcom,scorpion-pmu
          - qcom,scorpion-mp-pmu

  interrupts:
    # Don't know how many CPUs, so no constraints to specify
    description: 1 per-cpu interrupt (PPI) or 1 interrupt per core.

  interrupt-affinity:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      maxItems: 1
    description:
      When using SPIs, specifies a list of phandles to CPU
      nodes corresponding directly to the affinity of
      the SPIs listed in the interrupts property.

      When using a PPI, specifies a list of phandles to CPU
      nodes corresponding to the set of CPUs which have
      a PMU of this type signalling the PPI listed in the
      interrupts property, unless this is already specified
      by the PPI interrupt specifier itself (in which case
      the interrupt-affinity property shouldn't be present).

      This property should be present when there is more than
      a single SPI.

  qcom,no-pc-write:
    type: boolean
    description:
      Indicates that this PMU doesn't support the 0xc and 0xd events.

  secure-reg-access:
    type: boolean
    description:
      Indicates that the ARMv7 Secure Debug Enable Register
      (SDER) is accessible. This will cause the driver to do
      any setup required that is only possible in ARMv7 secure
      state. If not present the ARMv7 SDER will not be touched,
      which means the PMU may fail to operate unless external
      code (bootloader or security monitor) has performed the
      appropriate initialisation. Note that this property is
      not valid for non-ARMv7 CPUs or ARMv7 CPUs booting Linux
      in Non-secure state.

required:
  - compatible

additionalProperties: false

...
