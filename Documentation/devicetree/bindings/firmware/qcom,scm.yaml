# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/firmware/qcom,scm.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: QCOM Secure Channel Manager (SCM)

description: |
  Qualcomm processors include an interface to communicate to the secure firmware.
  This interface allows for clients to request different types of actions.
  These can include CPU power up/down, HDCP requests, loading of firmware,
  and other assorted actions.

maintainers:
  - <PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>
  - <PERSON> <rob<PERSON>rk<PERSON>@gmail.com>
  - <PERSON> <<EMAIL>>

properties:
  compatible:
    items:
      - enum:
          - qcom,scm-apq8064
          - qcom,scm-apq8084
          - qcom,scm-ipq4019
          - qcom,scm-ipq5332
          - qcom,scm-ipq6018
          - qcom,scm-ipq806x
          - qcom,scm-ipq8074
          - qcom,scm-ipq9574
          - qcom,scm-mdm9607
          - qcom,scm-msm8226
          - qcom,scm-msm8660
          - qcom,scm-msm8916
          - qcom,scm-msm8953
          - qcom,scm-msm8960
          - qcom,scm-msm8974
          - qcom,scm-msm8976
          - qcom,scm-msm8994
          - qcom,scm-msm8996
          - qcom,scm-msm8998
          - qcom,scm-qcm2290
          - qcom,scm-qdu1000
          - qcom,scm-sa8775p
          - qcom,scm-sc7180
          - qcom,scm-sc7280
          - qcom,scm-sc8180x
          - qcom,scm-sc8280xp
          - qcom,scm-sdm670
          - qcom,scm-sdm845
          - qcom,scm-sdx55
          - qcom,scm-sdx65
          - qcom,scm-sdx75
          - qcom,scm-sm6115
          - qcom,scm-sm6125
          - qcom,scm-sm6350
          - qcom,scm-sm6375
          - qcom,scm-sm8150
          - qcom,scm-sm8250
          - qcom,scm-sm8350
          - qcom,scm-sm8450
          - qcom,scm-sm8550
          - qcom,scm-qcs404
      - const: qcom,scm

  clocks:
    minItems: 1
    maxItems: 3

  clock-names:
    minItems: 1
    maxItems: 3

  dma-coherent: true

  interconnects:
    maxItems: 1

  interconnect-names:
    maxItems: 1

  '#reset-cells':
    const: 1

  interrupts:
    description:
      The wait-queue interrupt that firmware raises as part of handshake
      protocol to handle sleeping SCM calls.
    maxItems: 1

  qcom,dload-mode:
    $ref: /schemas/types.yaml#/definitions/phandle-array
    items:
      - items:
          - description: phandle to TCSR hardware block
          - description: offset of the download mode control register
    description: TCSR hardware block

allOf:
  # Clocks
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,scm-apq8064
              - qcom,scm-apq8084
              - qcom,scm-mdm9607
              - qcom,scm-msm8226
              - qcom,scm-msm8660
              - qcom,scm-msm8916
              - qcom,scm-msm8953
              - qcom,scm-msm8960
              - qcom,scm-msm8974
              - qcom,scm-msm8976
              - qcom,scm-qcm2290
              - qcom,scm-sm6375
    then:
      required:
        - clocks
        - clock-names
    else:
      properties:
        clock-names: false
        clocks: false

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,scm-apq8064
              - qcom,scm-msm8660
              - qcom,scm-msm8960
              - qcom,scm-qcm2290
              - qcom,scm-sm6375
    then:
      properties:
        clock-names:
          items:
            - const: core

        clocks:
          maxItems: 1

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,scm-apq8084
              - qcom,scm-mdm9607
              - qcom,scm-msm8226
              - qcom,scm-msm8916
              - qcom,scm-msm8953
              - qcom,scm-msm8974
              - qcom,scm-msm8976
    then:
      properties:
        clock-names:
          items:
            - const: core
            - const: bus
            - const: iface

        clocks:
          minItems: 3
          maxItems: 3

  # Interconnects
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - qcom,scm-qdu1000
                - qcom,scm-sc8280xp
                - qcom,scm-sm8450
                - qcom,scm-sm8550
    then:
      properties:
        interconnects: false

  # Interrupts
  - if:
      not:
        properties:
          compatible:
            contains:
              enum:
                - qcom,scm-sm8450
                - qcom,scm-sm8550
    then:
      properties:
        interrupts: false

required:
  - compatible

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-msm8916.h>

    firmware {
        scm {
            compatible = "qcom,scm-msm8916", "qcom,scm";
            clocks = <&gcc GCC_CRYPTO_CLK>,
                     <&gcc GCC_CRYPTO_AXI_CLK>,
                     <&gcc GCC_CRYPTO_AHB_CLK>;
            clock-names = "core", "bus", "iface";
        };
    };
