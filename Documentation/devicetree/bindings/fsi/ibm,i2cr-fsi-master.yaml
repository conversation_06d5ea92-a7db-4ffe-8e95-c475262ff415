# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/fsi/ibm,i2cr-fsi-master.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: IBM I2C Responder virtual FSI master

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The I2C Responder (I2CR) is a an I2C device that's connected to an FSI CFAM
  (see fsi.txt). The I2CR translates I2C bus operations to FSI CFAM reads and
  writes or SCOM operations, thereby acting as an FSI master.

properties:
  compatible:
    enum:
      - ibm,i2cr-fsi-master

  reg:
    maxItems: 1

required:
  - compatible
  - reg

additionalProperties: false

examples:
  - |
    i2c {
      #address-cells = <1>;
      #size-cells = <0>;

      i2cr@20 {
        compatible = "ibm,i2cr-fsi-master";
        reg = <0x20>;
      };
    };
