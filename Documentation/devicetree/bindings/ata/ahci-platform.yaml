# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/ata/ahci-platform.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: AHCI SATA Controller

description: |
  SATA nodes are defined to describe on-chip Serial ATA controllers.
  Each SATA controller should have its own node.

  It is possible, but not required, to represent each port as a sub-node.
  It allows to enable each port independently when dealing with multiple
  PHYs.

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

select:
  properties:
    compatible:
      contains:
        enum:
          - brcm,iproc-ahci
          - cavium,octeon-7130-ahci
          - hisilicon,hisi-ahci
          - ibm,476gtr-ahci
          - marvell,armada-3700-ahci
          - marvell,armada-8k-ahci
          - marvell,berlin2q-ahci
          - socionext,uniphier-pro4-ahci
          - socionext,uniphier-pxs2-ahci
          - socionext,uniphier-pxs3-ahci
  required:
    - compatible

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - brcm,iproc-ahci
              - marvell,armada-8k-ahci
              - marvell,berlin2-ahci
              - marvell,berlin2q-ahci
              - socionext,uniphier-pro4-ahci
              - socionext,uniphier-pxs2-ahci
              - socionext,uniphier-pxs3-ahci
          - const: generic-ahci
      - enum:
          - cavium,octeon-7130-ahci
          - hisilicon,hisi-ahci
          - ibm,476gtr-ahci
          - marvell,armada-3700-ahci

  reg:
    minItems: 1
    maxItems: 2

  reg-names:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 3

  clock-names:
    minItems: 1
    maxItems: 3

  interrupts:
    maxItems: 1

  power-domains:
    maxItems: 1

  resets:
    minItems: 1
    maxItems: 3

patternProperties:
  "^sata-port@[0-9a-f]+$":
    $ref: /schemas/ata/ahci-common.yaml#/$defs/ahci-port

    anyOf:
      - required: [ phys ]
      - required: [ target-supply ]

    unevaluatedProperties: false

required:
  - compatible
  - reg
  - interrupts

allOf:
  - $ref: ahci-common.yaml#
  - if:
      properties:
        compatible:
          contains:
            const: socionext,uniphier-pro4-ahci
    then:
      properties:
        resets:
          items:
            - description: reset line for the parent
            - description: reset line for the glue logic
            - description: reset line for the controller
      required:
        - resets
    else:
      if:
        properties:
          compatible:
            contains:
              enum:
                - socionext,uniphier-pxs2-ahci
                - socionext,uniphier-pxs3-ahci
      then:
        properties:
          resets:
            items:
              - description: reset for the glue logic
              - description: reset for the controller
        required:
          - resets
      else:
        properties:
          resets:
            maxItems: 1

unevaluatedProperties: false

examples:
  - |
    sata@ffe08000 {
        compatible = "snps,spear-ahci";
        reg = <0xffe08000 0x1000>;
        interrupts = <115>;
    };
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/berlin2q.h>
    #include <dt-bindings/ata/ahci.h>

    sata@f7e90000 {
        compatible = "marvell,berlin2q-ahci", "generic-ahci";
        reg = <0xf7e90000 0x1000>;
        interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&chip CLKID_SATA>;
        #address-cells = <1>;
        #size-cells = <0>;

        hba-cap = <HBA_SMPS>;

        sata0: sata-port@0 {
            reg = <0>;

            phys = <&sata_phy 0>;
            target-supply = <&reg_sata0>;

            hba-port-cap = <(HBA_PORT_FBSCP | HBA_PORT_ESP)>;
        };

        sata1: sata-port@1 {
            reg = <1>;

            phys = <&sata_phy 1>;
            target-supply = <&reg_sata1>;

            hba-port-cap = <(HBA_PORT_HPCP | HBA_PORT_MPSP | HBA_PORT_FBSCP)>;
        };
    };
