# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/ata/ceva,ahci-1v84.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Ceva AHCI SATA Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

description: |
  The Ceva SATA controller mostly conforms to the AHCI interface with some
  special extensions to add functionality, is a high-performance dual-port
  SATA host controller with an AHCI compliant command layer which supports
  advanced features such as native command queuing and frame information
  structure (FIS) based switching for systems employing port multipliers.

properties:
  compatible:
    const: ceva,ahci-1v84

  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  dma-coherent: true

  interrupts:
    maxItems: 1

  iommus:
    maxItems: 4

  power-domains:
    maxItems: 1

  ceva,p0-cominit-params:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    description: |
      OOB timing value for COMINIT parameter for port 0.
      The fields for the above parameter must be as shown below:-
      ceva,p0-cominit-params = /bits/ 8 <CIBGMN CIBGMX CIBGN CINMP>;
    items:
      - description: CINMP - COMINIT Negate Minimum Period.
      - description: CIBGN - COMINIT Burst Gap Nominal.
      - description: CIBGMX - COMINIT Burst Gap Maximum.
      - description: CIBGMN - COMINIT Burst Gap Minimum.

  ceva,p0-comwake-params:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    description: |
      OOB timing value for COMWAKE parameter for port 0.
      The fields for the above parameter must be as shown below:-
      ceva,p0-comwake-params = /bits/ 8 <CWBGMN CWBGMX CWBGN CWNMP>;
    items:
      - description: CWBGMN - COMWAKE Burst Gap Minimum.
      - description: CWBGMX - COMWAKE Burst Gap Maximum.
      - description: CWBGN - COMWAKE Burst Gap Nominal.
      - description: CWNMP - COMWAKE Negate Minimum Period.

  ceva,p0-burst-params:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    description: |
      Burst timing value for COM parameter for port 0.
      The fields for the above parameter must be as shown below:-
      ceva,p0-burst-params = /bits/ 8 <BMX BNM SFD PTST>;
    items:
      - description: BMX - COM Burst Maximum.
      - description: BNM - COM Burst Nominal.
      - description: SFD - Signal Failure Detection value.
      - description: PTST - Partial to Slumber timer value.

  ceva,p0-retry-params:
    $ref: /schemas/types.yaml#/definitions/uint16-array
    description: |
      Retry interval timing value for port 0.
      The fields for the above parameter must be as shown below:-
      ceva,p0-retry-params = /bits/ 16 <RIT RCT>;
    items:
      - description: RIT - Retry Interval Timer.
      - description: RCT - Rate Change Timer.

  ceva,p1-cominit-params:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    description: |
      OOB timing value for COMINIT parameter for port 1.
      The fields for the above parameter must be as shown below:-
      ceva,p1-cominit-params = /bits/ 8 <CIBGMN CIBGMX CIBGN CINMP>;
    items:
      - description: CINMP - COMINIT Negate Minimum Period.
      - description: CIBGN - COMINIT Burst Gap Nominal.
      - description: CIBGMX - COMINIT Burst Gap Maximum.
      - description: CIBGMN - COMINIT Burst Gap Minimum.

  ceva,p1-comwake-params:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    description: |
      OOB timing value for COMWAKE parameter for port 1.
      The fields for the above parameter must be as shown below:-
      ceva,p1-comwake-params = /bits/ 8 <CWBGMN CWBGMX CWBGN CWNMP>;
    items:
      - description: CWBGMN - COMWAKE Burst Gap Minimum.
      - description: CWBGMX - COMWAKE Burst Gap Maximum.
      - description: CWBGN - COMWAKE Burst Gap Nominal.
      - description: CWNMP - COMWAKE Negate Minimum Period.

  ceva,p1-burst-params:
    $ref: /schemas/types.yaml#/definitions/uint8-array
    description: |
      Burst timing value for COM parameter for port 1.
      The fields for the above parameter must be as shown below:-
      ceva,p1-burst-params = /bits/ 8 <BMX BNM SFD PTST>;
    items:
      - description: BMX - COM Burst Maximum.
      - description: BNM - COM Burst Nominal.
      - description: SFD - Signal Failure Detection value.
      - description: PTST - Partial to Slumber timer value.

  ceva,p1-retry-params:
    $ref: /schemas/types.yaml#/definitions/uint16-array
    description: |
      Retry interval timing value for port 1.
      The fields for the above parameter must be as shown below:-
      ceva,pN-retry-params = /bits/ 16 <RIT RCT>;
    items:
      - description: RIT - Retry Interval Timer.
      - description: RCT - Rate Change Timer.

  ceva,broken-gen2:
    $ref: /schemas/types.yaml#/definitions/flag
    description: |
      limit to gen1 speed instead of gen2.

  phys:
    maxItems: 1

  phy-names:
    items:
      - const: sata-phy

  resets:
    maxItems: 1

required:
  - compatible
  - reg
  - clocks
  - interrupts
  - ceva,p0-cominit-params
  - ceva,p0-comwake-params
  - ceva,p0-burst-params
  - ceva,p0-retry-params
  - ceva,p1-cominit-params
  - ceva,p1-comwake-params
  - ceva,p1-burst-params
  - ceva,p1-retry-params

additionalProperties: false

examples:
  - |
    #include <dt-bindings/clock/xlnx-zynqmp-clk.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    #include <dt-bindings/power/xlnx-zynqmp-power.h>
    #include <dt-bindings/reset/xlnx-zynqmp-resets.h>
    #include <dt-bindings/clock/xlnx-zynqmp-clk.h>
    #include <dt-bindings/phy/phy.h>

    sata: ahci@fd0c0000 {
        compatible = "ceva,ahci-1v84";
        reg = <0xfd0c0000 0x200>;
        interrupt-parent = <&gic>;
        interrupts = <0 133 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&zynqmp_clk SATA_REF>;
        ceva,p0-cominit-params = /bits/ 8 <0x0F 0x25 0x18 0x29>;
        ceva,p0-comwake-params = /bits/ 8 <0x04 0x0B 0x08 0x0F>;
        ceva,p0-burst-params = /bits/ 8 <0x0A 0x08 0x4A 0x06>;
        ceva,p0-retry-params = /bits/ 16 <0x0216 0x7F06>;
        ceva,p1-cominit-params = /bits/ 8 <0x0F 0x25 0x18 0x29>;
        ceva,p1-comwake-params = /bits/ 8 <0x04 0x0B 0x08 0x0F>;
        ceva,p1-burst-params = /bits/ 8 <0x0A 0x08 0x4A 0x06>;
        ceva,p1-retry-params = /bits/ 16 <0x0216 0x7F06>;
        ceva,broken-gen2;
        phys = <&psgtr 1 PHY_TYPE_SATA 1 1>;
        resets = <&zynqmp_reset ZYNQMP_RESET_SATA>;
    };
