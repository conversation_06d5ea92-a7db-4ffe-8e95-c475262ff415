# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/sifive,fu540-c000-pdma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: SiFive Unleashed Rev C000 Platform DMA

maintainers:
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>
  - <PERSON> <<EMAIL>>

description: |
  Platform DMA is a DMA engine of SiFive Unleashed. It supports 4
  channels. Each channel has 2 interrupts. One is for DMA done and
  the other is for DME error.

  In different SoC, DMA could be attached to different IRQ line.
  DT file need to be changed to meet the difference. For technical
  doc,

  https://static.dev.sifive.com/FU540-C000-v1.0.pdf

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    items:
      - enum:
          - sifive,fu540-c000-pdma
      - const: sifive,pdma0
    description:
      Should be "sifive,<chip>-pdma" and "sifive,pdma<version>".
      Supported compatible strings are -
      "sifive,fu540-c000-pdma" for the SiFive PDMA v0 as integrated onto the
      SiFive FU540 chip resp and "sifive,pdma0" for the SiFive PDMA v0 IP block
      with no chip integration tweaks.

  reg:
    maxItems: 1

  interrupts:
    minItems: 1
    maxItems: 8

  dma-channels:
    description: For backwards-compatibility, the default value is 4
    minimum: 1
    maximum: 4
    default: 4

  '#dma-cells':
    const: 1

required:
  - compatible
  - reg
  - interrupts

unevaluatedProperties: false

examples:
  - |
    dma-controller@3000000 {
      compatible = "sifive,fu540-c000-pdma", "sifive,pdma0";
      reg = <0x3000000 0x8000>;
      dma-channels = <4>;
      interrupts = <23>, <24>, <25>, <26>, <27>, <28>, <29>, <30>;
      #dma-cells = <1>;
    };

...
