# SPDX-License-Identifier: GPL-2.0-only
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/snps,dma-spear1340.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Synopsys Designware DMA Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON> <and<PERSON><PERSON>.<EMAIL>>

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    oneOf:
      - const: snps,dma-spear1340
      - items:
          - enum:
              - renesas,r9a06g032-dma
          - const: renesas,rzn1-dma


  "#dma-cells":
    minimum: 3
    maximum: 4
    description: |
      First cell is a phandle pointing to the DMA controller. Second one is
      the DMA request line number. Third cell is the memory master identifier
      for transfers on dynamically allocated channel. Fourth cell is the
      peripheral master identifier for transfers on an allocated channel. Fifth
      cell is an optional mask of the DMA channels permitted to be allocated
      for the corresponding client device.

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    maxItems: 1

  clock-names:
    description: AHB interface reference clock.
    const: hclk

  dma-channels:
    description: |
      Number of DMA channels supported by the controller. In case if
      not specified the driver will try to auto-detect this and
      the rest of the optional parameters.
    minimum: 1
    maximum: 8

  dma-requests:
    minimum: 1
    maximum: 16

  dma-masters:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Number of DMA masters supported by the controller. In case if
      not specified the driver will try to auto-detect this and
      the rest of the optional parameters.
    minimum: 1
    maximum: 4

  chan_allocation_order:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      DMA channels allocation order specifier. Zero means ascending order
      (first free allocated), while one - descending (last free allocated).
    default: 0
    enum: [0, 1]

  chan_priority:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      DMA channels priority order. Zero means ascending channels priority
      so the very first channel has the highest priority. While 1 means
      descending priority (the last channel has the highest priority).
    default: 0
    enum: [0, 1]

  block_size:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: Maximum block size supported by the DMA controller.
    enum: [3, 7, 15, 31, 63, 127, 255, 511, 1023, 2047, 4095]

  data-width:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: Data bus width per each DMA master in bytes.
    items:
      maxItems: 4
      items:
        enum: [4, 8, 16, 32]

  data_width:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    deprecated: true
    description: |
      Data bus width per each DMA master in (2^n * 8) bits. This property is
      deprecated. It' usage is discouraged in favor of data-width one. Moreover
      the property incorrectly permits to define data-bus width of 8 and 16
      bits, which is impossible in accordance with DW DMAC IP-core data book.
    items:
      maxItems: 4
      items:
        enum:
          - 0 # 8 bits
          - 1 # 16 bits
          - 2 # 32 bits
          - 3 # 64 bits
          - 4 # 128 bits
          - 5 # 256 bits
        default: 0

  multi-block:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      LLP-based multi-block transfer supported by hardware per
      each DMA channel.
    items:
      maxItems: 8
      items:
        enum: [0, 1]
        default: 1

  snps,max-burst-len:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      Maximum length of the burst transactions supported by the controller.
      This property defines the upper limit of the run-time burst setting
      (CTLx.SRC_MSIZE/CTLx.DST_MSIZE fields) so the allowed burst length
      will be from 1 to max-burst-len words. It's an array property with one
      cell per channel in the units determined by the value set in the
      CTLx.SRC_TR_WIDTH/CTLx.DST_TR_WIDTH fields (data width).
    items:
      maxItems: 8
      items:
        enum: [4, 8, 16, 32, 64, 128, 256]
        default: 256

  snps,dma-protection-control:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: |
      Bits one-to-one passed to the AHB HPROT[3:1] bus. Each bit setting
      indicates the following features: bit 0 - privileged mode,
      bit 1 - DMA is bufferable, bit 2 - DMA is cacheable.
    default: 0
    minimum: 0
    maximum: 7

unevaluatedProperties: false

required:
  - compatible
  - "#dma-cells"
  - reg
  - interrupts

examples:
  - |
    dma-controller@fc000000 {
      compatible = "snps,dma-spear1340";
      reg = <0xfc000000 0x1000>;
      interrupt-parent = <&vic1>;
      interrupts = <12>;

      dma-channels = <8>;
      dma-requests = <16>;
      dma-masters = <4>;
      #dma-cells = <3>;

      chan_allocation_order = <1>;
      chan_priority = <1>;
      block_size = <0xfff>;
      data-width = <8 8>;
      multi-block = <0 0 0 0 0 0 0 0>;
      snps,max-burst-len = <16 16 4 4 4 4 4 4>;
    };
...
