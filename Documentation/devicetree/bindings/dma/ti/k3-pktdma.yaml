# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2020 Texas Instruments Incorporated
# Author: <PERSON> <<EMAIL>>
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/ti/k3-pktdma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments K3 DMSS PKTDMA

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The Packet DMA (PKTDMA) is intended to perform similar functions as the packet
  mode channels of K3 UDMA-P.
  PKTDMA only includes Split channels to service PSI-L based peripherals.

  The peripherals can be PSI-L native or legacy, non PSI-L native peripherals
  with PDMAs. PDMA is tasked to act as a bridge between the PSI-L fabric and the
  legacy peripheral.

  PDMAs can be configured via PKTDMA split channel's peer registers to match
  with the configuration of the legacy peripheral.

allOf:
  - $ref: /schemas/dma/dma-controller.yaml#
  - $ref: /schemas/arm/keystone/ti,k3-sci-common.yaml#

properties:
  compatible:
    const: ti,am64-dmss-pktdma

  "#dma-cells":
    const: 2
    description: |
      The first cell is the PSI-L  thread ID of the remote (to PKTDMA) end.
      Valid ranges for thread ID depends on the data movement direction:
      for source thread IDs (rx): 0 - 0x7fff
      for destination thread IDs (tx): 0x8000 - 0xffff

      Please refer to the device documentation for the PSI-L thread map and also
      the PSI-L peripheral chapter for the correct thread ID.

      The second cell is the ASEL value for the channel

  reg:
    maxItems: 4

  reg-names:
    items:
      - const: gcfg
      - const: rchanrt
      - const: tchanrt
      - const: ringrt

  msi-parent: true

  ti,sci-rm-range-tchan:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      Array of PKTDMA split tx channel resource subtypes for resource allocation
      for this host
    minItems: 1
    # Should be enough
    maxItems: 255
    items:
      maximum: 0x3f

  ti,sci-rm-range-tflow:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      Array of PKTDMA split tx flow resource subtypes for resource allocation
      for this host
    minItems: 1
    # Should be enough
    maxItems: 255
    items:
      maximum: 0x3f

  ti,sci-rm-range-rchan:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      Array of PKTDMA split rx channel resource subtypes for resource allocation
      for this host
    minItems: 1
    # Should be enough
    maxItems: 255
    items:
      maximum: 0x3f

  ti,sci-rm-range-rflow:
    $ref: /schemas/types.yaml#/definitions/uint32-array
    description: |
      Array of PKTDMA split rx flow resource subtypes for resource allocation
      for this host
    minItems: 1
    # Should be enough
    maxItems: 255
    items:
      maximum: 0x3f

required:
  - compatible
  - "#dma-cells"
  - reg
  - reg-names
  - msi-parent
  - ti,sci
  - ti,sci-dev-id
  - ti,sci-rm-range-tchan
  - ti,sci-rm-range-tflow
  - ti,sci-rm-range-rchan
  - ti,sci-rm-range-rflow

unevaluatedProperties: false

examples:
  - |+
    cbass_main {
        #address-cells = <2>;
        #size-cells = <2>;

        main_dmss {
            compatible = "simple-mfd";
            #address-cells = <2>;
            #size-cells = <2>;
            dma-ranges;
            ranges;

            ti,sci-dev-id = <25>;

            main_pktdma: dma-controller@485c0000 {
                compatible = "ti,am64-dmss-pktdma";

                reg = <0x0 0x485c0000 0x0 0x100>,
                      <0x0 0x4a800000 0x0 0x20000>,
                      <0x0 0x4aa00000 0x0 0x40000>,
                      <0x0 0x4b800000 0x0 0x400000>;
                reg-names = "gcfg", "rchanrt", "tchanrt", "ringrt";
                msi-parent = <&inta_main_dmss>;
                #dma-cells = <2>;

                ti,sci = <&dmsc>;
                ti,sci-dev-id = <30>;

                ti,sci-rm-range-tchan = <0x23>, /* UNMAPPED_TX_CHAN */
                                        <0x24>, /* CPSW_TX_CHAN */
                                        <0x25>, /* SAUL_TX_0_CHAN */
                                        <0x26>, /* SAUL_TX_1_CHAN */
                                        <0x27>, /* ICSSG_0_TX_CHAN */
                                        <0x28>; /* ICSSG_1_TX_CHAN */
                ti,sci-rm-range-tflow = <0x10>, /* RING_UNMAPPED_TX_CHAN */
                                        <0x11>, /* RING_CPSW_TX_CHAN */
                                        <0x12>, /* RING_SAUL_TX_0_CHAN */
                                        <0x13>, /* RING_SAUL_TX_1_CHAN */
                                        <0x14>, /* RING_ICSSG_0_TX_CHAN */
                                        <0x15>; /* RING_ICSSG_1_TX_CHAN */
                ti,sci-rm-range-rchan = <0x29>, /* UNMAPPED_RX_CHAN */
                                        <0x2b>, /* CPSW_RX_CHAN */
                                        <0x2d>, /* SAUL_RX_0_CHAN */
                                        <0x2f>, /* SAUL_RX_1_CHAN */
                                        <0x31>, /* SAUL_RX_2_CHAN */
                                        <0x33>, /* SAUL_RX_3_CHAN */
                                        <0x35>, /* ICSSG_0_RX_CHAN */
                                        <0x37>; /* ICSSG_1_RX_CHAN */
                ti,sci-rm-range-rflow = <0x2a>, /* FLOW_UNMAPPED_RX_CHAN */
                                        <0x2c>, /* FLOW_CPSW_RX_CHAN */
                                        <0x2e>, /* FLOW_SAUL_RX_0/1_CHAN */
                                        <0x32>, /* FLOW_SAUL_RX_2/3_CHAN */
                                        <0x36>, /* FLOW_ICSSG_0_RX_CHAN */
                                        <0x38>; /* FLOW_ICSSG_1_RX_CHAN */
            };
        };
    };
