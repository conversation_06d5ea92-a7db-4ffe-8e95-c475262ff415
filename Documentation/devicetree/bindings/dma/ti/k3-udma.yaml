# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
# Copyright (C) 2019 Texas Instruments Incorporated
# Author: <PERSON> <<EMAIL>>
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/ti/k3-udma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Texas Instruments K3 NAVSS Unified DMA

maintainers:
  - <PERSON> <<EMAIL>>

description: |
  The UDMA-P is intended to perform similar (but significantly upgraded)
  functions as the packet-oriented DMA used on previous SoC devices. The UDMA-P
  module supports the transmission and reception of various packet types.
  The UDMA-P architecture facilitates the segmentation and reassembly of SoC DMA
  data structure compliant packets to/from smaller data blocks that are natively
  compatible with the specific requirements of each connected peripheral.
  Multiple Tx and Rx channels are provided within the DMA which allow multiple
  segmentation or reassembly operations to be ongoing. The DMA controller
  maintains state information for each of the channels which allows packet
  segmentation and reassembly operations to be time division multiplexed between
  channels in order to share the underlying DMA hardware. An external DMA
  scheduler is used to control the ordering and rate at which this multiplexing
  occurs for Transmit operations. The ordering and rate of Receive operations
  is indirectly controlled by the order in which blocks are pushed into the DMA
  on the Rx PSI-L interface.

  The UDMA-P also supports acting as both a UTC and UDMA-C for its internal
  channels. Channels in the UDMA-P can be configured to be either Packet-Based
  or Third-Party channels on a channel by channel basis.

  All transfers within NAVSS is done between PSI-L source and destination
  threads.
  The peripherals serviced by UDMA can be PSI-L native (sa2ul, cpsw, etc) or
  legacy, non PSI-L native peripherals. In the later case a special, small PDMA
  is tasked to act as a bridge between the PSI-L fabric and the legacy
  peripheral.

  PDMAs can be configured via UDMAP peer registers to match with the
  configuration of the legacy peripheral.

allOf:
  - $ref: ../dma-controller.yaml#
  - $ref: /schemas/arm/keystone/ti,k3-sci-common.yaml#

properties:
  "#dma-cells":
    minimum: 1
    maximum: 2
    description: |
      The cell is the PSI-L  thread ID of the remote (to UDMAP) end.
      Valid ranges for thread ID depends on the data movement direction:
      for source thread IDs (rx): 0 - 0x7fff
      for destination thread IDs (tx): 0x8000 - 0xffff

      Please refer to the device documentation for the PSI-L thread map and also
      the PSI-L peripheral chapter for the correct thread ID.

      When #dma-cells is 2, the second parameter is the channel ATYPE.

  compatible:
    enum:
      - ti,am654-navss-main-udmap
      - ti,am654-navss-mcu-udmap
      - ti,j721e-navss-main-udmap
      - ti,j721e-navss-mcu-udmap

  reg:
    maxItems: 3

  reg-names:
    items:
      - const: gcfg
      - const: rchanrt
      - const: tchanrt

  msi-parent: true

  ti,ringacc:
    description: phandle to the ring accelerator node
    $ref: /schemas/types.yaml#/definitions/phandle

  ti,sci-rm-range-tchan:
    description: |
      Array of UDMA tchan resource subtypes for resource allocation for this
      host
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    # Should be enough
    maxItems: 255

  ti,sci-rm-range-rchan:
    description: |
      Array of UDMA rchan resource subtypes for resource allocation for this
      host
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    # Should be enough
    maxItems: 255

  ti,sci-rm-range-rflow:
    description: |
      Array of UDMA rflow resource subtypes for resource allocation for this
      host
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    # Should be enough
    maxItems: 255

required:
  - compatible
  - "#dma-cells"
  - reg
  - reg-names
  - msi-parent
  - ti,sci
  - ti,sci-dev-id
  - ti,ringacc
  - ti,sci-rm-range-tchan
  - ti,sci-rm-range-rchan
  - ti,sci-rm-range-rflow

if:
  properties:
    "#dma-cells":
      const: 2
then:
  properties:
    ti,udma-atype:
      description: ATYPE value which should be used by non slave channels
      $ref: /schemas/types.yaml#/definitions/uint32

  required:
    - ti,udma-atype

unevaluatedProperties: false

examples:
  - |+
    cbass_main {
        #address-cells = <2>;
        #size-cells = <2>;

        cbass_main_navss: navss@30800000 {
            compatible = "simple-mfd";
            #address-cells = <2>;
            #size-cells = <2>;
            dma-coherent;
            dma-ranges;
            ranges = <0x0 0x30800000 0x0 0x30800000 0x0 0x05000000>;

            ti,sci-dev-id = <118>;

            main_udmap: dma-controller@31150000 {
                compatible = "ti,am654-navss-main-udmap";
                reg = <0x0 0x31150000 0x0 0x100>,
                      <0x0 0x34000000 0x0 0x100000>,
                      <0x0 0x35000000 0x0 0x100000>;
                reg-names = "gcfg", "rchanrt", "tchanrt";
                #dma-cells = <1>;

                ti,ringacc = <&ringacc>;

                msi-parent = <&inta_main_udmass>;

                ti,sci = <&dmsc>;
                ti,sci-dev-id = <188>;

                ti,sci-rm-range-tchan = <0x1>, /* TX_HCHAN */
                                        <0x2>; /* TX_CHAN */
                ti,sci-rm-range-rchan = <0x4>, /* RX_HCHAN */
                                        <0x5>; /* RX_CHAN */
                ti,sci-rm-range-rflow = <0x6>; /* GP RFLOW */
            };
        };
    };
