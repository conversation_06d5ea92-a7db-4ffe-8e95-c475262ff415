# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/qcom,gpi.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies Inc GPI DMA controller

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

description: |
  QCOM GPI DMA controller provides DMA capabilities for
  peripheral buses such as I2C, UART, and SPI.

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - qcom,sdm845-gpi-dma
          - qcom,sm6350-gpi-dma
      - items:
          - enum:
              - qcom,qcm2290-gpi-dma
              - qcom,qdu1000-gpi-dma
              - qcom,sc7280-gpi-dma
              - qcom,sm6115-gpi-dma
              - qcom,sm6375-gpi-dma
              - qcom,sm8350-gpi-dma
              - qcom,sm8450-gpi-dma
              - qcom,sm8550-gpi-dma
          - const: qcom,sm6350-gpi-dma
      - items:
          - enum:
              - qcom,sdm670-gpi-dma
              - qcom,sm6125-gpi-dma
              - qcom,sm8150-gpi-dma
              - qcom,sm8250-gpi-dma
          - const: qcom,sdm845-gpi-dma

  reg:
    maxItems: 1

  interrupts:
    description:
      Interrupt lines for each GPI instance
    minItems: 1
    maxItems: 13

  "#dma-cells":
    const: 3
    description: >
      DMA clients must use the format described in dma.txt, giving a phandle
      to the DMA controller plus the following 3 integer cells:
      - channel: if set to 0xffffffff, any available channel will be allocated
        for the client. Otherwise, the exact channel specified will be used.
      - seid: serial id of the client as defined in the SoC documentation.
      - client: type of the client as defined in dt-bindings/dma/qcom-gpi.h

  iommus:
    maxItems: 1

  dma-channels:
    maximum: 31

  dma-channel-mask:
    maxItems: 1

required:
  - compatible
  - reg
  - interrupts
  - "#dma-cells"
  - iommus
  - dma-channels
  - dma-channel-mask

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/dma/qcom-gpi.h>
    gpi_dma0: dma-controller@800000 {
        compatible = "qcom,sdm845-gpi-dma";
        #dma-cells = <3>;
        reg = <0x00800000 0x60000>;
        iommus = <&apps_smmu 0x0016 0x0>;
        dma-channels = <13>;
        dma-channel-mask = <0xfa>;
        interrupts = <GIC_SPI 244 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 245 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 246 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 247 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 248 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 249 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 250 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 251 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 252 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 253 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 254 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 255 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 256 IRQ_TYPE_LEVEL_HIGH>;
    };

...
