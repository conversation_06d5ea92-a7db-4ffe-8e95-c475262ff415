# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/fsl,mxs-dma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale Direct Memory Access (DMA) Controller from i.MX23/i.MX28

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - fsl,imx6q-dma-apbh
              - fsl,imx6sx-dma-apbh
              - fsl,imx7d-dma-apbh
          - const: fsl,imx28-dma-apbh
      - enum:
          - fsl,imx23-dma-apbh
          - fsl,imx23-dma-apbx
          - fsl,imx28-dma-apbh
          - fsl,imx28-dma-apbx
  reg:
    maxItems: 1

  clocks:
    maxItems: 1

  interrupts:
    minItems: 4
    maxItems: 16

  "#dma-cells":
    const: 1

  dma-channels:
    enum: [4, 8, 16]

required:
  - compatible
  - reg
  - "#dma-cells"
  - dma-channels
  - interrupts

additionalProperties: false

examples:
  - |
    interrupt-parent = <&irqc>;

    dma-controller@80004000 {
      compatible = "fsl,imx28-dma-apbh";
      reg = <0x80004000 0x2000>;
      interrupts = <82 83 84 85
                    88 88 88 88
                    88 88 88 88
                    87 86 0 0>;
      #dma-cells = <1>;
      dma-channels = <16>;
    };

    dma-controller@80024000 {
      compatible = "fsl,imx28-dma-apbx";
      reg = <0x80024000 0x2000>;
      interrupts = <78 79 66 0
                    80 81 68 69
                    70 71 72 73
                    74 75 76 77>;
      #dma-cells = <1>;
      dma-channels = <16>;
    };

...
