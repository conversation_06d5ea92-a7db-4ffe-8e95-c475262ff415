# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/renesas,rz-dmac.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Renesas RZ/{G2L,G2UL,V2L} DMA Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    items:
      - enum:
          - renesas,r9a07g043-dmac # RZ/G2UL
          - renesas,r9a07g044-dmac # RZ/G2{L,LC}
          - renesas,r9a07g054-dmac # RZ/V2L
      - const: renesas,rz-dmac

  reg:
    items:
      - description: Control and channel register block
      - description: DMA extended resource selector block

  interrupts:
    maxItems: 17

  interrupt-names:
    items:
      - const: error
      - const: ch0
      - const: ch1
      - const: ch2
      - const: ch3
      - const: ch4
      - const: ch5
      - const: ch6
      - const: ch7
      - const: ch8
      - const: ch9
      - const: ch10
      - const: ch11
      - const: ch12
      - const: ch13
      - const: ch14
      - const: ch15

  clocks:
    items:
      - description: DMA main clock
      - description: DMA register access clock

  clock-names:
    items:
      - const: main
      - const: register

  '#dma-cells':
    const: 1
    description:
      The cell specifies the encoded MID/RID values of the DMAC port
      connected to the DMA client and the slave channel configuration
      parameters.
      bits[0:9] - Specifies MID/RID value
      bit[10] - Specifies DMA request high enable (HIEN)
      bit[11] - Specifies DMA request detection type (LVL)
      bits[12:14] - Specifies DMAACK output mode (AM)
      bit[15] - Specifies Transfer Mode (TM)

  dma-channels:
    const: 16

  power-domains:
    maxItems: 1

  resets:
    items:
      - description: Reset for DMA ARESETN reset terminal
      - description: Reset for DMA RST_ASYNC reset terminal

  reset-names:
    items:
      - const: arst
      - const: rst_async

required:
  - compatible
  - reg
  - interrupts
  - interrupt-names
  - clocks
  - clock-names
  - '#dma-cells'
  - dma-channels
  - power-domains
  - resets
  - reset-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/r9a07g044-cpg.h>

    dmac: dma-controller@11820000 {
        compatible = "renesas,r9a07g044-dmac",
                     "renesas,rz-dmac";
        reg = <0x11820000 0x10000>,
              <0x11830000 0x10000>;
        interrupts = <GIC_SPI 141 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 125 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 126 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 127 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 128 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 129 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 130 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 131 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 132 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 133 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 134 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 135 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 136 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 137 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 138 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 139 IRQ_TYPE_EDGE_RISING>,
                     <GIC_SPI 140 IRQ_TYPE_EDGE_RISING>;
        interrupt-names = "error",
                          "ch0", "ch1", "ch2", "ch3",
                          "ch4", "ch5", "ch6", "ch7",
                          "ch8", "ch9", "ch10", "ch11",
                          "ch12", "ch13", "ch14", "ch15";
        clocks = <&cpg CPG_MOD R9A07G044_DMAC_ACLK>,
                 <&cpg CPG_MOD R9A07G044_DMAC_PCLK>;
        clock-names = "main", "register";
        power-domains = <&cpg>;
        resets = <&cpg R9A07G044_DMAC_ARESETN>,
                 <&cpg R9A07G044_DMAC_RST_ASYNC>;
        reset-names = "arst", "rst_async";
        #dma-cells = <1>;
        dma-channels = <16>;
    };
