# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/allwinner,sun50i-a64-dma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Allwinner A64 DMA Controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dma-controller.yaml#

properties:
  "#dma-cells":
    const: 1
    description: The cell is the request line number.

  compatible:
    oneOf:
      - enum:
          - allwinner,sun20i-d1-dma
          - allwinner,sun50i-a64-dma
          - allwinner,sun50i-a100-dma
          - allwinner,sun50i-h6-dma
      - items:
          - const: allwinner,sun8i-r40-dma
          - const: allwinner,sun50i-a64-dma

  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    items:
      - const: bus
      - const: mbus

  resets:
    maxItems: 1

required:
  - "#dma-cells"
  - compatible
  - reg
  - interrupts
  - clocks
  - resets
  - dma-channels

if:
  properties:
    compatible:
      enum:
        - allwinner,sun20i-d1-dma
        - allwinner,sun50i-a100-dma
        - allwinner,sun50i-h6-dma

then:
  properties:
    clocks:
      minItems: 2

  required:
    - clock-names

else:
  properties:
    clocks:
      maxItems: 1

unevaluatedProperties: false

examples:
  - |
    dma: dma-controller@1c02000 {
        compatible = "allwinner,sun50i-a64-dma";
        reg = <0x01c02000 0x1000>;
        interrupts = <0 50 4>;
        clocks = <&ccu 30>;
        dma-channels = <8>;
        dma-requests = <27>;
        resets = <&ccu 7>;
        #dma-cells = <1>;
    };

...
