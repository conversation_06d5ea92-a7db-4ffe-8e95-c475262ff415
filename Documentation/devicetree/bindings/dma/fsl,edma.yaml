# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/fsl,edma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale enhanced Direct Memory Access(eDMA) Controller

description: |
  The eDMA channels have multiplex capability by programmable
  memory-mapped registers. channels are split into two groups, called
  DMAMUX0 and DMAMUX1, specific DMA request source can only be multiplexed
  by any channel of certain group, DMAMUX0 or DMAMUX1, but not both.

maintainers:
  - Peng Fan <<EMAIL>>

properties:
  compatible:
    oneOf:
      - enum:
          - fsl,vf610-edma
          - fsl,imx7ulp-edma
          - fsl,imx8qm-adma
          - fsl,imx8qm-edma
          - fsl,imx93-edma3
          - fsl,imx93-edma4
      - items:
          - const: fsl,ls1028a-edma
          - const: fsl,vf610-edma

  reg:
    minItems: 1
    maxItems: 3

  interrupts:
    minItems: 1
    maxItems: 64

  interrupt-names:
    minItems: 1
    maxItems: 64

  "#dma-cells":
    enum:
      - 2
      - 3

  dma-channels:
    minItems: 1
    maxItems: 64

  clocks:
    minItems: 1
    maxItems: 2

  clock-names:
    minItems: 1
    maxItems: 2

  big-endian:
    description: |
      If present registers and hardware scatter/gather descriptors of the
      eDMA are implemented in big endian mode, otherwise in little mode.
    type: boolean

required:
  - "#dma-cells"
  - compatible
  - reg
  - interrupts
  - clocks
  - dma-channels

allOf:
  - $ref: dma-controller.yaml#
  - if:
      properties:
        compatible:
          contains:
            enum:
              - fsl,imx8qm-adma
              - fsl,imx8qm-edma
              - fsl,imx93-edma3
              - fsl,imx93-edma4
    then:
      properties:
        "#dma-cells":
          const: 3
        # It is not necessary to write the interrupt name for each channel.
        # instead, you can simply maintain the sequential IRQ numbers as
        # defined for the DMA channels.
        interrupt-names: false
        clock-names:
          items:
            - const: dma
        clocks:
          maxItems: 1

  - if:
      properties:
        compatible:
          contains:
            const: fsl,vf610-edma
    then:
      properties:
        clocks:
          minItems: 2
        clock-names:
          items:
            - const: dmamux0
            - const: dmamux1
        interrupts:
          minItems: 2
          maxItems: 2
        interrupt-names:
          items:
            - const: edma-tx
            - const: edma-err
        reg:
          minItems: 2
          maxItems: 3
        "#dma-cells":
          const: 2
        dma-channels:
          const: 32

  - if:
      properties:
        compatible:
          contains:
            const: fsl,imx7ulp-edma
    then:
      properties:
        clock:
          minItems: 2
        clock-names:
          items:
            - const: dma
            - const: dmamux0
        interrupts:
          minItems: 2
          maxItems: 17
        reg:
          minItems: 2
          maxItems: 2
        "#dma-cells":
          const: 2
        dma-channels:
          const: 32

unevaluatedProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/vf610-clock.h>

    edma0: dma-controller@40018000 {
      #dma-cells = <2>;
      compatible = "fsl,vf610-edma";
      reg = <0x40018000 0x2000>,
            <0x40024000 0x1000>,
            <0x40025000 0x1000>;
      interrupts = <0 8 IRQ_TYPE_LEVEL_HIGH>,
                   <0 9 IRQ_TYPE_LEVEL_HIGH>;
      interrupt-names = "edma-tx", "edma-err";
      dma-channels = <32>;
      clock-names = "dmamux0", "dmamux1";
      clocks = <&clks VF610_CLK_DMAMUX0>, <&clks VF610_CLK_DMAMUX1>;
    };

  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/imx7ulp-clock.h>

    edma1: dma-controller@40080000 {
      #dma-cells = <2>;
      compatible = "fsl,imx7ulp-edma";
      reg = <0x40080000 0x2000>,
            <0x40210000 0x1000>;
      dma-channels = <32>;
      interrupts = <GIC_SPI 0 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 1 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>,
                   /* last is eDMA2-ERR interrupt */
                   <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
       clock-names = "dma", "dmamux0";
       clocks = <&pcc2 IMX7ULP_CLK_DMA1>, <&pcc2 IMX7ULP_CLK_DMA_MUX1>;
    };

  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/clock/imx93-clock.h>

    dma-controller@44000000 {
      compatible = "fsl,imx93-edma3";
      reg = <0x44000000 0x200000>;
      #dma-cells = <3>;
      dma-channels = <31>;
      interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
                   <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&clk IMX93_CLK_EDMA1_GATE>;
        clock-names = "dma";
    };
