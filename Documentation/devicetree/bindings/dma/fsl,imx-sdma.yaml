# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/fsl,imx-sdma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Freescale Smart Direct Memory Access (SDMA) Controller for i.MX

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - fsl,imx50-sdma
              - fsl,imx51-sdma
              - fsl,imx53-sdma
              - fsl,imx6q-sdma
              - fsl,imx7d-sdma
          - const: fsl,imx35-sdma
      - items:
          - enum:
              - fsl,imx6sx-sdma
              - fsl,imx6sl-sdma
          - const: fsl,imx6q-sdma
      - items:
          - const: fsl,imx6ul-sdma
          - const: fsl,imx6q-sdma
          - const: fsl,imx35-sdma
      - items:
          - const: fsl,imx6sll-sdma
          - const: fsl,imx6ul-sdma
      - items:
          - const: fsl,imx8mq-sdma
          - const: fsl,imx7d-sdma
      - items:
          - enum:
              - fsl,imx8mp-sdma
              - fsl,imx8mn-sdma
              - fsl,imx8mm-sdma
          - const: fsl,imx8mq-sdma
      - items:
          - enum:
              - fsl,imx25-sdma
              - fsl,imx31-sdma
              - fsl,imx35-sdma
  reg:
    maxItems: 1

  interrupts:
    maxItems: 1

  fsl,sdma-ram-script-name:
    $ref: /schemas/types.yaml#/definitions/string
    description: Should contain the full path of SDMA RAM scripts firmware.

  "#dma-cells":
    const: 3
    description: |
      The first cell: request/event ID

      The second cell: peripheral types ID
        enum:
          - MCU domain SSI: 0
          - Shared SSI: 1
          - MMC: 2
          - SDHC: 3
          - MCU domain UART: 4
          - Shared UART: 5
          - FIRI: 6
          - MCU domain CSPI: 7
          - Shared CSPI: 8
          - SIM: 9
          - ATA: 10
          - CCM: 11
          - External peripheral: 12
          - Memory Stick Host Controller: 13
          - Shared Memory Stick Host Controller: 14
          - DSP: 15
          - Memory: 16
          - FIFO type Memory: 17
          - SPDIF: 18
          - IPU Memory: 19
          - ASRC: 20
          - ESAI: 21
          - SSI Dual FIFO: 22
              description: needs firmware more than ver 2
          - Shared ASRC: 23
          - SAI: 24
          - HDMI Audio: 25

       The third cell: transfer priority ID
         enum:
           - High: 0
           - Medium: 1
           - Low: 2

  gpr:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: The phandle to the General Purpose Register (GPR) node

  fsl,sdma-event-remap:
    $ref: /schemas/types.yaml#/definitions/uint32-matrix
    maxItems: 2
    items:
      items:
        - description: GPR register offset
        - description: GPR register shift
        - description: GPR register value
    description: |
      Register bits of sdma event remap, the format is <reg shift val>.
      The order is <RX>, <TX>.

  clocks:
    maxItems: 2

  clock-names:
    items:
      - const: ipg
      - const: ahb

  iram:
    $ref: /schemas/types.yaml#/definitions/phandle
    description: The phandle to the On-chip RAM (OCRAM) node.

required:
  - compatible
  - reg
  - interrupts
  - fsl,sdma-ram-script-name

additionalProperties: false

examples:
  - |
    sdma: dma-controller@83fb0000 {
      compatible = "fsl,imx51-sdma", "fsl,imx35-sdma";
      reg = <0x83fb0000 0x4000>;
      interrupts = <6>;
      #dma-cells = <3>;
      fsl,sdma-ram-script-name = "sdma-imx51.bin";
    };

...
