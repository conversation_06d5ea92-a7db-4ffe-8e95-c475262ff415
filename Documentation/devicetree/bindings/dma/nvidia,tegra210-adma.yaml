# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/nvidia,tegra210-adma.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: NVIDIA Tegra Audio DMA (ADMA) controller

description: |
  The Tegra Audio DMA controller is used for transferring data
  between system memory and the Audio Processing Engine (APE).

maintainers:
  - <PERSON> <<EMAIL>>

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    oneOf:
      - enum:
          - nvidia,tegra210-adma
          - nvidia,tegra186-adma
      - items:
          - enum:
              - nvidia,tegra234-adma
              - nvidia,tegra194-adma
          - const: nvidia,tegra186-adma

  reg:
    maxItems: 1

  interrupts:
    description: |
      Should contain all of the per-channel DMA interrupts in
      ascending order with respect to the DMA channel index.
    minItems: 1
    maxItems: 32

  clocks:
    description: Must contain one entry for the ADMA module clock
    maxItems: 1

  clock-names:
    const: d_audio

  "#dma-cells":
    description: |
      The first cell denotes the receive/transmit request number and
      should be between 1 and the maximum number of requests supported.
      This value corresponds to the RX/TX_REQUEST_SELECT fields in the
      ADMA_CHn_CTRL register.
    const: 1

required:
  - compatible
  - reg
  - interrupts
  - clocks
  - clock-names

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include<dt-bindings/clock/tegra210-car.h>

    dma-controller@702e2000 {
        compatible = "nvidia,tegra210-adma";
        reg = <0x702e2000 0x2000>;
        interrupt-parent = <&tegra_agic>;
        interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>,
                     <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
        clocks = <&tegra_car TEGRA210_CLK_D_AUDIO>;
        clock-names = "d_audio";
        #dma-cells = <1>;
    };

...
