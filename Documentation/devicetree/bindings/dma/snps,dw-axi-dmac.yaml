# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/snps,dw-axi-dmac.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Synopsys DesignWare AXI DMA Controller

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <Eugeniy.<PERSON><PERSON><PERSON>@synopsys.com>

description:
  Synopsys DesignWare AXI DMA Controller DT Binding

allOf:
  - $ref: dma-controller.yaml#

properties:
  compatible:
    enum:
      - snps,axi-dma-1.01a
      - intel,kmb-axi-dma
      - starfive,jh7110-axi-dma

  reg:
    minItems: 1
    items:
      - description: Address range of the DMAC registers
      - description: Address range of the DMAC APB registers

  reg-names:
    items:
      - const: axidma_ctrl_regs
      - const: axidma_apb_regs

  interrupts:
    description:
      If the IP-core synthesis parameter DMAX_INTR_IO_TYPE is set to 1, this
      will be per-channel interrupts. Otherwise, this is a single combined IRQ
      for all channels.
    minItems: 1
    maxItems: 8

  clocks:
    items:
      - description: Bus Clock
      - description: Module Clock

  clock-names:
    items:
      - const: core-clk
      - const: cfgr-clk

  '#dma-cells':
    const: 1

  dma-channels:
    minimum: 1
    maximum: 8

  resets:
    minItems: 1
    maxItems: 2

  snps,dma-masters:
    description: |
      Number of AXI masters supported by the hardware.
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [1, 2]

  snps,data-width:
    description: |
      AXI data width supported by hardware.
      (0 - 8bits, 1 - 16bits, 2 - 32bits, ..., 6 - 512bits)
    $ref: /schemas/types.yaml#/definitions/uint32
    enum: [0, 1, 2, 3, 4, 5, 6]

  snps,priority:
    description: |
      Channel priority specifier associated with the DMA channels.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 8

  snps,block-size:
    description: |
      Channel block size specifier associated with the DMA channels.
    $ref: /schemas/types.yaml#/definitions/uint32-array
    minItems: 1
    maxItems: 8

  snps,axi-max-burst-len:
    description: |
      Restrict master AXI burst length by value specified in this property.
      If this property is missing the maximum AXI burst length supported by
      DMAC is used.
    $ref: /schemas/types.yaml#/definitions/uint32
    minimum: 1
    maximum: 256

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - interrupts
  - '#dma-cells'
  - dma-channels
  - snps,dma-masters
  - snps,data-width
  - snps,priority
  - snps,block-size

if:
  properties:
    compatible:
      contains:
        enum:
          - starfive,jh7110-axi-dma
then:
  properties:
    resets:
      minItems: 2
      items:
        - description: AXI reset line
        - description: AHB reset line
        - description: module reset
else:
  properties:
    resets:
      maxItems: 1

additionalProperties: false

examples:
  - |
    #include <dt-bindings/interrupt-controller/arm-gic.h>
    #include <dt-bindings/interrupt-controller/irq.h>
    /* example with snps,dw-axi-dmac */
    dma-controller@80000 {
        compatible = "snps,axi-dma-1.01a";
        reg = <0x80000 0x400>;
        clocks = <&core_clk>, <&cfgr_clk>;
        clock-names = "core-clk", "cfgr-clk";
        interrupt-parent = <&intc>;
        interrupts = <27>;
        #dma-cells = <1>;
        dma-channels = <4>;
        snps,dma-masters = <2>;
        snps,data-width = <3>;
        snps,block-size = <4096 4096 4096 4096>;
        snps,priority = <0 1 2 3>;
        snps,axi-max-burst-len = <16>;
    };
