# SPDX-License-Identifier: GPL-2.0
%YAML 1.2
---
$id: http://devicetree.org/schemas/dma/dma-controller.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: DMA Controller Common Properties

maintainers:
  - <PERSON><PERSON> <<EMAIL>>

allOf:
  - $ref: dma-common.yaml#

# Everything else is described in the common file
properties:
  $nodename:
    pattern: "^dma-controller(@.*)?$"

additionalProperties: true

examples:
  - |
    dma: dma-controller@48000000 {
        compatible = "ti,omap-sdma";
        reg = <0x48000000 0x1000>;
        interrupts = <0 12 0x4>,
                     <0 13 0x4>,
                     <0 14 0x4>,
                     <0 15 0x4>;
        #dma-cells = <1>;
        dma-channels = <32>;
        dma-requests = <127>;
        dma-channel-mask = <0xfffe>;
    };

...
