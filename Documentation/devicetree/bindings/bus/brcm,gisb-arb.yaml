# SPDX-License-Identifier: (GPL-2.0-only OR BSD-2-Clause)
%YAML 1.2
---
$id: http://devicetree.org/schemas/bus/brcm,gisb-arb.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Broadcom GISB bus Arbiter controller

maintainers:
  - <PERSON><PERSON><PERSON> <<EMAIL>>

properties:
  compatible:
    oneOf:
      - items:
          - enum:
              - brcm,bcm7445-gisb-arb  # for other 28nm chips
          - const: brcm,gisb-arb
      - items:
          - enum:
              - brcm,bcm7278-gisb-arb  # for V7 28nm chips
              - brcm,bcm7435-gisb-arb  # for newer 40nm chips
              - brcm,bcm7400-gisb-arb  # for older 40nm chips and all 65nm chips
              - brcm,bcm7038-gisb-arb  # for 130nm chips
              - brcm,gisb-arb          # fallback compatible

  reg:
    maxItems: 1

  interrupts:
    minItems: 2
    items:
      - description: timeout interrupt line
      - description: target abort interrupt line
      - description: breakpoint interrupt line

  brcm,gisb-arb-master-mask:
    $ref: /schemas/types.yaml#/definitions/uint32
    description: >
      32-bits wide bitmask used to specify which GISB masters are valid at the
      system level

  brcm,gisb-arb-master-names:
    $ref: /schemas/types.yaml#/definitions/string-array
    description: >
      String list of the literal name of the GISB masters. Should match the
      number of bits set in brcm,gisb-master-mask and the order in which they
      appear from MSB to LSB.

required:
  - compatible
  - reg
  - interrupts

additionalProperties: false

examples:
  - |
    gisb-arb@f0400000 {
      compatible = "brcm,gisb-arb";
      reg = <0xf0400000 0x800>;
      interrupts = <0>, <2>;
      interrupt-parent = <&sun_l2_intc>;
      brcm,gisb-arb-master-mask = <0x7>;
      brcm,gisb-arb-master-names = "bsp_0", "scpu_0", "cpu_0";
    };
