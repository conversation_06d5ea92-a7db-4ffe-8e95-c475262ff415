# SPDX-License-Identifier: GPL-2.0-only OR BSD-2-Clause
%YAML 1.2
---
$id: http://devicetree.org/schemas/cpufreq/cpufreq-qcom-hw.yaml#
$schema: http://devicetree.org/meta-schemas/core.yaml#

title: Qualcomm Technologies, Inc. CPUFREQ

maintainers:
  - <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

description: |

  CPUFREQ HW is a hardware engine used by some Qualcomm Technologies, Inc. (QTI)
  SoCs to manage frequency in hardware. It is capable of controlling frequency
  for multiple clusters.

properties:
  compatible:
    oneOf:
      - description: v1 of CPUFREQ HW
        items:
          - enum:
              - qcom,qcm2290-cpufreq-hw
              - qcom,sc7180-cpufreq-hw
              - qcom,sdm845-cpufreq-hw
              - qcom,sm6115-cpufreq-hw
              - qcom,sm6350-cpufreq-hw
              - qcom,sm8150-cpufreq-hw
          - const: qcom,cpufreq-hw

      - description: v2 of CPUFREQ HW (EPSS)
        items:
          - enum:
              - qcom,qdu1000-cpufreq-epss
              - qcom,sa8775p-cpufreq-epss
              - qcom,sc7280-cpufreq-epss
              - qcom,sc8280xp-cpufreq-epss
              - qcom,sm6375-cpufreq-epss
              - qcom,sm8250-cpufreq-epss
              - qcom,sm8350-cpufreq-epss
              - qcom,sm8450-cpufreq-epss
              - qcom,sm8550-cpufreq-epss
          - const: qcom,cpufreq-epss

  reg:
    minItems: 1
    items:
      - description: Frequency domain 0 register region
      - description: Frequency domain 1 register region
      - description: Frequency domain 2 register region
      - description: Frequency domain 3 register region

  reg-names:
    minItems: 1
    items:
      - const: freq-domain0
      - const: freq-domain1
      - const: freq-domain2
      - const: freq-domain3

  clocks:
    items:
      - description: XO Clock
      - description: GPLL0 Clock

  clock-names:
    items:
      - const: xo
      - const: alternate

  interrupts:
    minItems: 1
    maxItems: 4

  interrupt-names:
    minItems: 1
    items:
      - const: dcvsh-irq-0
      - const: dcvsh-irq-1
      - const: dcvsh-irq-2
      - const: dcvsh-irq-3

  '#freq-domain-cells':
    const: 1

  '#clock-cells':
    const: 1

required:
  - compatible
  - reg
  - clocks
  - clock-names
  - '#freq-domain-cells'

additionalProperties: false

allOf:
  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,qcm2290-cpufreq-hw
    then:
      properties:
        reg:
          minItems: 1
          maxItems: 1

        reg-names:
          minItems: 1
          maxItems: 1

        interrupts:
          minItems: 1
          maxItems: 1

        interrupt-names:
          minItems: 1

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,qdu1000-cpufreq-epss
              - qcom,sc7180-cpufreq-hw
              - qcom,sc8280xp-cpufreq-epss
              - qcom,sdm845-cpufreq-hw
              - qcom,sm6115-cpufreq-hw
              - qcom,sm6350-cpufreq-hw
              - qcom,sm6375-cpufreq-epss
    then:
      properties:
        reg:
          minItems: 2
          maxItems: 2

        reg-names:
          minItems: 2
          maxItems: 2

        interrupts:
          minItems: 2
          maxItems: 2

        interrupt-names:
          minItems: 2

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sc7280-cpufreq-epss
              - qcom,sm8250-cpufreq-epss
              - qcom,sm8350-cpufreq-epss
              - qcom,sm8450-cpufreq-epss
              - qcom,sm8550-cpufreq-epss
    then:
      properties:
        reg:
          minItems: 3
          maxItems: 3

        reg-names:
          minItems: 3
          maxItems: 3

        interrupts:
          minItems: 3
          maxItems: 3

        interrupt-names:
          minItems: 3

  - if:
      properties:
        compatible:
          contains:
            enum:
              - qcom,sm8150-cpufreq-hw
    then:
      properties:
        reg:
          minItems: 3
          maxItems: 3

        reg-names:
          minItems: 3
          maxItems: 3

        # On some SoCs the Prime core shares the LMH irq with Big cores
        interrupts:
          minItems: 2
          maxItems: 2

        interrupt-names:
          minItems: 2


examples:
  - |
    #include <dt-bindings/clock/qcom,gcc-sdm845.h>
    #include <dt-bindings/clock/qcom,rpmh.h>

    // Example 1: Dual-cluster, Quad-core per cluster. CPUs within a cluster
    // switch DCVS state together.
    cpus {
      #address-cells = <2>;
      #size-cells = <0>;

      CPU0: cpu@0 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x0>;
        enable-method = "psci";
        next-level-cache = <&L2_0>;
        qcom,freq-domain = <&cpufreq_hw 0>;
        clocks = <&cpufreq_hw 0>;
        L2_0: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
          L3_0: l3-cache {
            compatible = "cache";
            cache-unified;
            cache-level = <3>;
          };
        };
      };

      CPU1: cpu@100 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x100>;
        enable-method = "psci";
        next-level-cache = <&L2_100>;
        qcom,freq-domain = <&cpufreq_hw 0>;
        clocks = <&cpufreq_hw 0>;
        L2_100: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };

      CPU2: cpu@200 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x200>;
        enable-method = "psci";
        next-level-cache = <&L2_200>;
        qcom,freq-domain = <&cpufreq_hw 0>;
        clocks = <&cpufreq_hw 0>;
        L2_200: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };

      CPU3: cpu@300 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x300>;
        enable-method = "psci";
        next-level-cache = <&L2_300>;
        qcom,freq-domain = <&cpufreq_hw 0>;
        clocks = <&cpufreq_hw 0>;
        L2_300: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };

      CPU4: cpu@400 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x400>;
        enable-method = "psci";
        next-level-cache = <&L2_400>;
        qcom,freq-domain = <&cpufreq_hw 1>;
        clocks = <&cpufreq_hw 1>;
        L2_400: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };

      CPU5: cpu@500 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x500>;
        enable-method = "psci";
        next-level-cache = <&L2_500>;
        qcom,freq-domain = <&cpufreq_hw 1>;
        clocks = <&cpufreq_hw 1>;
        L2_500: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };

      CPU6: cpu@600 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x600>;
        enable-method = "psci";
        next-level-cache = <&L2_600>;
        qcom,freq-domain = <&cpufreq_hw 1>;
        clocks = <&cpufreq_hw 1>;
        L2_600: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };

      CPU7: cpu@700 {
        device_type = "cpu";
        compatible = "qcom,kryo385";
        reg = <0x0 0x700>;
        enable-method = "psci";
        next-level-cache = <&L2_700>;
        qcom,freq-domain = <&cpufreq_hw 1>;
        clocks = <&cpufreq_hw 1>;
        L2_700: l2-cache {
          compatible = "cache";
          cache-unified;
          cache-level = <2>;
          next-level-cache = <&L3_0>;
        };
      };
    };

    soc {
      #address-cells = <1>;
      #size-cells = <1>;

      cpufreq@17d43000 {
        compatible = "qcom,sdm845-cpufreq-hw", "qcom,cpufreq-hw";
        reg = <0x17d43000 0x1400>, <0x17d45800 0x1400>;
        reg-names = "freq-domain0", "freq-domain1";

        clocks = <&rpmhcc RPMH_CXO_CLK>, <&gcc GPLL0>;
        clock-names = "xo", "alternate";

        #freq-domain-cells = <1>;
        #clock-cells = <1>;
      };
    };
...
