==========
Crypto API
==========

:Author: <PERSON>
:Author: <PERSON><PERSON>

This documentation outlines the Linux kernel crypto API with its
concepts, details about developing cipher implementations, employment of the API
for cryptographic use cases, as well as programming examples.

.. class:: toc-title

	   Table of contents

.. toctree::
   :maxdepth: 2

   intro
   api-intro
   architecture

   async-tx-api
   asymmetric-keys
   devel-algos
   userspace-if
   crypto_engine
   api
   api-samples
   descore-readme
