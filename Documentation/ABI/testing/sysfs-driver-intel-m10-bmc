What:		/sys/bus/.../drivers/intel-m10-bmc/.../bmc_version
Date:		June 2020
KernelVersion:	5.10
Contact:	<PERSON> <<EMAIL>>
Description:	Read only. Returns the hardware build version of Intel
		MAX10 BMC chip.
		Format: "0x%x".

What:		/sys/bus/.../drivers/intel-m10-bmc/.../bmcfw_version
Date:		June 2020
KernelVersion:	5.10
Contact:	<PERSON> <<EMAIL>>
Description:	Read only. Returns the firmware version of Intel MAX10
		BMC chip.
		Format: "0x%x".

What:		/sys/bus/.../drivers/intel-m10-bmc/.../mac_address
Date:		January 2021
KernelVersion:  5.12
Contact:	Russ Weight <<EMAIL>>
Description:	Read only. Returns the first MAC address in a block
		of sequential MAC addresses assigned to the board
		that is managed by the Intel MAX10 BMC. It is stored in
		FLASH storage and is mirrored in the MAX10 BMC register
		space.
		Format: "%02x:%02x:%02x:%02x:%02x:%02x".

What:		/sys/bus/.../drivers/intel-m10-bmc/.../mac_count
Date:		January 2021
KernelVersion:  5.12
Contact:	Russ Weight <<EMAIL>>
Description:	Read only. Returns the number of sequential MAC
		addresses assigned to the board managed by the Intel
		MAX10 BMC. This value is stored in FLASH and is mirrored
		in the MAX10 BMC register space.
		Format: "%u".
