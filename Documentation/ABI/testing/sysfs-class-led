What:		/sys/class/leds/<led>/brightness
Date:		March 2006
KernelVersion:	2.6.17
Contact:	<PERSON> <<EMAIL>>
Description:
		Set the brightness of the LED.

		Most LEDs don't have hardware brightness support, so will
		just be turned on for non-zero brightness settings.

		.. Note::

		  For multicolor LEDs, writing to this file will update all
		  LEDs within the group to a calculated percentage of what
		  each color LED intensity is set to.

		  The percentage is calculated for each grouped LED via
		  the equation below::

		    led_brightness = brightness * multi_intensity/max_brightness

		  For additional details please refer to
		  Documentation/leds/leds-class-multicolor.rst.

		The value is between 0 and
		/sys/class/leds/<led>/max_brightness.

		Writing 0 to this file clears active trigger.

		Writing non-zero to this file while trigger is active changes the
		top brightness trigger is going to use.



What:		/sys/class/leds/<led>/max_brightness
Date:		March 2006
KernelVersion:	2.6.17
Contact:	<PERSON> <rpur<PERSON>@rpsys.net>
Description:
		Maximum brightness level for this LED, default is 255 (LED_FULL).

		If the LED does not support different brightness levels, this
		should be 1.

What:		/sys/class/leds/<led>/brightness_hw_changed
Date:		January 2017
KernelVersion:	4.11
Description:
		Last hardware set brightness level for this LED. Some LEDs
		may be changed autonomously by hardware/firmware. Only LEDs
		where this happens and the driver can detect this, will have
		this file.

		This file supports poll() to detect when the hardware changes
		the brightness.

		Reading this file will return the last brightness level set
		by the hardware, this may be different from the current
		brightness. Reading this file when no hw brightness change
		event has happened will return an ENODATA error.

What:		/sys/class/leds/<led>/color
Date:		June 2023
KernelVersion:	6.5
Description:
		Color of the LED.

		This is a read-only file. Reading this file returns the color
		of the LED as a string (e.g: "red", "green", "multicolor").

What:		/sys/class/leds/<led>/trigger
Date:		March 2006
KernelVersion:	2.6.17
Contact:	Richard Purdie <<EMAIL>>
Description:
		Set the trigger for this LED. A trigger is a kernel based source
		of LED events.

		You can change triggers in a similar manner to the way an IO
		scheduler is chosen. Trigger specific parameters can appear in
		/sys/class/leds/<led> once a given trigger is selected. For
		their documentation see `sysfs-class-led-trigger-*`.

What:		/sys/class/leds/<led>/inverted
Date:		January 2011
KernelVersion:	2.6.38
Contact:	Richard Purdie <<EMAIL>>
Description:
		Invert the LED on/off state. This parameter is specific to
		gpio and backlight triggers. In case of the backlight trigger,
		it is useful when driving a LED which is intended to indicate
		a device in a standby like state.
