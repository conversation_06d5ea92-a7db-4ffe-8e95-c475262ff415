What:		/sys/devices/cpu/events/
		/sys/devices/cpu/events/branch-misses
		/sys/devices/cpu/events/cache-references
		/sys/devices/cpu/events/cache-misses
		/sys/devices/cpu/events/stalled-cycles-frontend
		/sys/devices/cpu/events/branch-instructions
		/sys/devices/cpu/events/stalled-cycles-backend
		/sys/devices/cpu/events/instructions
		/sys/devices/cpu/events/cpu-cycles

Date:		2013/01/08

Contact:	Linux kernel mailing list <<EMAIL>>

Description:	Generic performance monitoring events

		A collection of performance monitoring events that may be
		supported by many/most CPUs. These events can be monitored
		using the 'perf(1)' tool.

		The contents of each file would look like:

			event=0xNNNN

		where 'N' is a hex digit and the number '0xNNNN' shows the
		"raw code" for the perf event identified by the file's
		"basename".


What: /sys/bus/event_source/devices/<pmu>/events/<event>
Date: 2014/02/24
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Per-pmu performance monitoring events specific to the running system

		Each file (except for some of those with a '.' in them, '.unit'
		and '.scale') in the 'events' directory describes a single
		performance monitoring event supported by the <pmu>. The name
		of the file is the name of the event.

		File contents:

			<term>[=<value>][,<term>[=<value>]]...

		Where <term> is one of the terms listed under
		/sys/bus/event_source/devices/<pmu>/format/ and <value> is
		a number is base-16 format with a '0x' prefix (lowercase only).
		If a <term> is specified alone (without an assigned value), it
		is implied that 0x1 is assigned to that <term>.

		Examples (each of these lines would be in a separate file):

			event=0x2abc
			event=0x423,inv,cmask=0x3
			domain=0x1,offset=0x8,starting_index=0xffff
			domain=0x1,offset=0x8,core=?

		Each of the assignments indicates a value to be assigned to a
		particular set of bits (as defined by the format file
		corresponding to the <term>) in the perf_event structure passed
		to the perf_open syscall.

		In the case of the last example, a value replacing "?" would
		need to be provided by the user selecting the particular event.
		This is referred to as "event parameterization". Event
		parameters have the format 'param=?'.

What: /sys/bus/event_source/devices/<pmu>/events/<event>.unit
Date: 2014/02/24
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Perf event units

		A string specifying the English plural numerical unit that <event>
		(once multiplied by <event>.scale) represents.

		Example:

			Joules

What: /sys/bus/event_source/devices/<pmu>/events/<event>.scale
Date: 2014/02/24
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Perf event scaling factors

		A string representing a floating point value expressed in
		scientific notation to be multiplied by the event count
		received from the kernel to match the unit specified in the
		<event>.unit file.

		Example:

			2.3283064365386962890625e-10

		This is provided to avoid performing floating point arithmetic
		in the kernel.
