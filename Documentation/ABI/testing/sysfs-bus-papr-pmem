What:		/sys/bus/nd/devices/nmemX/papr/flags
Date:		Apr, 2020
KernelVersion:	v5.8
Contact:	linuxppc-dev <<EMAIL>>, <EMAIL>,
Description:
		(RO) Report flags indicating various states of a
		papr-pmem NVDIMM device. Each flag maps to a one or
		more bits set in the dimm-health-bitmap retrieved in
		response to H_SCM_HEALTH hcall. The details of the bit
		flags returned in response to this hcall is available
		at 'Documentation/powerpc/papr_hcalls.rst' . Below are
		the flags reported in this sysfs file:

		* "not_armed"
				  Indicates that NVDIMM contents will not
				  survive a power cycle.
		* "flush_fail"
				  Indicates that NVDIMM contents
				  couldn't be flushed during last
				  shut-down event.
		* "restore_fail"
				  Indicates that NVDIMM contents
				  couldn't be restored during NVDIMM
				  initialization.
		* "encrypted"
				  NVDIMM contents are encrypted.
		* "smart_notify"
				  There is health event for the NVDIMM.
		* "scrubbed"
				  Indicating that contents of the
				  NVDIMM have been scrubbed.
		* "locked"
				  Indicating that NVDIMM contents can't
				  be modified until next power cycle.

What:		/sys/bus/nd/devices/nmemX/papr/perf_stats
Date:		May, 2020
KernelVersion:	v5.9
Contact:	linuxppc-dev <<EMAIL>>, <EMAIL>,
Description:
		(RO) Report various performance stats related to papr-scm NVDIMM
		device. This attribute is only available for NVDIMM devices
		that support reporting NVDIMM performance stats. Each stat is
		reported on a new line with each line composed of a
		stat-identifier followed by it value. Below are currently known
		dimm performance stats which are reported:

		* "CtlResCt" : Controller Reset Count
		* "CtlResTm" : Controller Reset Elapsed Time
		* "PonSecs " : Power-on Seconds
		* "MemLife " : Life Remaining
		* "CritRscU" : Critical Resource Utilization
		* "HostLCnt" : Host Load Count
		* "HostSCnt" : Host Store Count
		* "HostSDur" : Host Store Duration
		* "HostLDur" : Host Load Duration
		* "MedRCnt " : Media Read Count
		* "MedWCnt " : Media Write Count
		* "MedRDur " : Media Read Duration
		* "MedWDur " : Media Write Duration
		* "CchRHCnt" : Cache Read Hit Count
		* "CchWHCnt" : Cache Write Hit Count
		* "FastWCnt" : Fast Write Count

What:		/sys/bus/nd/devices/nmemX/papr/health_bitmap_inject
Date:		Jan, 2022
KernelVersion:	v5.17
Contact:	linuxppc-dev <<EMAIL>>, <EMAIL>,
Description:
		(RO) Reports the health bitmap inject bitmap that is applied to
		bitmap received from PowerVM via the H_SCM_HEALTH. This is used
		to forcibly set specific bits returned from Hcall. These is then
		used to simulate various health or shutdown states for an nvdimm
		and are set by user-space tools like ndctl by issuing a PAPR DSM.

