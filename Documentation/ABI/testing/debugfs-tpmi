What:		/sys/kernel/debug/tpmi-<n>/pfs_dump
Date:		November 2023
KernelVersion:	6.6
Contact:	<EMAIL>
Description:
The PFS (PM Feature Structure) table, shows details of each power
management feature. This includes:
tpmi_id, number of entries, entry size, offset, vsec offset, lock status
and disabled status.
Users:		Debugging, any user space test suite

What:		/sys/kernel/debug/tpmi-<n>/tpmi-id-<n>/mem_dump
Date:		November 2023
KernelVersion:	6.6
Contact:	<EMAIL>
Description:
Shows the memory dump of the MMIO region for a TPMI ID.
Users:		Debugging, any user space test suite

What:		/sys/kernel/debug/tpmi-<n>/tpmi-id-<n>/mem_write
Date:		November 2023
KernelVersion:	6.6
Contact:	<EMAIL>
Description:
Allows to write at any offset. It doesn't check for Read/Write access
as hardware will not allow to write at read-only memory. This write is
at offset multiples of 4. The format is instance,offset,contents.
Example:
echo 0,0x20,0xff > mem_write
echo 1,64,64 > mem_write
Users:		Debugging, any user space test suite
