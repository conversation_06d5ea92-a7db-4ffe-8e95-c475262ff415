What:		/sys/devices/platform/HISI04Bx:00/chipX/all_linked
What:		/sys/devices/platform/HISI04Bx:00/chipX/linked_full_lane
What:		/sys/devices/platform/HISI04Bx:00/chipX/crc_err_cnt
Date:		November 2023
KernelVersion:	6.6
Contact:	Huisong Li <<EMAIL>>
Description:
		The /sys/devices/platform/HISI04Bx:00/chipX/ directory
		contains read-only attributes exposing some summarization
		information of all HCCS ports under a specified chip.
		The X in 'chipX' indicates the Xth chip on platform.

		There are following attributes in this directory:

		================= ==== =========================================
		all_linked:       (RO) if all enabled ports on this chip are
				       linked (bool).
		linked_full_lane: (RO) if all linked ports on this chip are full
				       lane (bool).
		crc_err_cnt:      (RO) total CRC err count for all ports on this
				       chip.
		================= ==== =========================================

What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/all_linked
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/linked_full_lane
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/crc_err_cnt
Date:		November 2023
KernelVersion:	6.6
Contact:	Huisong Li <<EMAIL>>
Description:
		The /sys/devices/platform/HISI04Bx:00/chipX/dieY/ directory
		contains read-only attributes exposing some summarization
		information of all HCCS ports under a specified die.
		The Y in 'dieY' indicates the hardware id of the die on chip who
		has chip id X.

		There are following attributes in this directory:

		================= ==== =========================================
		all_linked:       (RO) if all enabled ports on this die are
				       linked (bool).
		linked_full_lane: (RO) if all linked ports on this die are full
				       lane (bool).
		crc_err_cnt:      (RO) total CRC err count for all ports on this
				       die.
		================= ==== =========================================

What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/type
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/lane_mode
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/enable
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/cur_lane_num
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/link_fsm
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/lane_mask
What:		/sys/devices/platform/HISI04Bx:00/chipX/dieY/hccsN/crc_err_cnt
Date:		November 2023
KernelVersion:	6.6
Contact:	Huisong Li <<EMAIL>>
Description:
		The /sys/devices/platform/HISI04Bx/chipX/dieX/hccsN/ directory
		contains read-only attributes exposing information about
		a HCCS port. The N value in 'hccsN' indicates this port id.
		The X in 'chipX' indicates the ID of the chip to which the
		HCCS port belongs. For example, X ranges from to 'n - 1' if the
		chip number on platform is n.
		The Y in 'dieY' indicates the hardware id of the die to which
		the hccs port belongs.
		Note: type, lane_mode and enable are fixed attributes on running
		platform.

		The HCCS port have the following attributes:

		============= ==== =============================================
		type:         (RO) port type (string), e.g. HCCS-v1 -> H32
		lane_mode:    (RO) the lane mode of this port (string), e.g. x8
		enable:       (RO) indicate if this port is enabled (bool).
		cur_lane_num: (RO) current lane number of this port.
		link_fsm:     (RO) link finite state machine of this port.
		lane_mask:    (RO) current lane mask of this port, every bit
			           indicates a lane.
		crc_err_cnt:  (RO) CRC err count on this port.
		============= ==== =============================================
