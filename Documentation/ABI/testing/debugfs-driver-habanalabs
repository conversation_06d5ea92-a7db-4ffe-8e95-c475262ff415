What:           /sys/kernel/debug/habanalabs/hl<n>/addr
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets the device address to be used for read or write through
                PCI bar, or the device VA of a host mapped memory to be read or
                written directly from the host. The latter option is allowed
                only when the IOMMU is disabled.
                The acceptable value is a string that starts with "0x"

What:           /sys/kernel/debug/habanalabs/hl<n>/clk_gate
Date:           May 2020
KernelVersion:  5.8
Contact:        <EMAIL>
Description:    This setting is now deprecated as clock gating is handled solely by the f/w

What:           /sys/kernel/debug/habanalabs/hl<n>/command_buffers
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Displays a list with information about the currently allocated
                command buffers

What:           /sys/kernel/debug/habanalabs/hl<n>/command_submission
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Displays a list with information about the currently active
                command submissions

What:           /sys/kernel/debug/habanalabs/hl<n>/command_submission_jobs
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Displays a list with detailed information about each JOB (CB) of
                each active command submission

What:           /sys/kernel/debug/habanalabs/hl<n>/data32
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Allows the root user to read or write directly through the
                device's PCI bar. Writing to this file generates a write
                transaction while reading from the file generates a read
                transaction. This custom interface is needed (instead of using
                the generic Linux user-space PCI mapping) because the DDR bar
                is very small compared to the DDR memory and only the driver can
                move the bar before and after the transaction.

                If the IOMMU is disabled, it also allows the root user to read
                or write from the host a device VA of a host mapped memory

What:           /sys/kernel/debug/habanalabs/hl<n>/data64
Date:           Jan 2020
KernelVersion:  5.6
Contact:        <EMAIL>
Description:    Allows the root user to read or write 64 bit data directly
                through the device's PCI bar. Writing to this file generates a
                write transaction while reading from the file generates a read
                transaction. This custom interface is needed (instead of using
                the generic Linux user-space PCI mapping) because the DDR bar
                is very small compared to the DDR memory and only the driver can
                move the bar before and after the transaction.

                If the IOMMU is disabled, it also allows the root user to read
                or write from the host a device VA of a host mapped memory

What:           /sys/kernel/debug/habanalabs/hl<n>/data_dma
Date:           Apr 2021
KernelVersion:  5.13
Contact:        <EMAIL>
Description:    Allows the root user to read from the device's internal
                memory (DRAM/SRAM) through a DMA engine.
                This property is a binary blob that contains the result of the
                DMA transfer.
                This custom interface is needed (instead of using the generic
                Linux user-space PCI mapping) because the amount of internal
                memory is huge (>32GB) and reading it via the PCI bar will take
                a very long time.
                This interface doesn't support concurrency in the same device.
                In GAUDI and GOYA, this action can cause undefined behavior
                in case the it is done while the device is executing user
                workloads.
                Only supported on GAUDI at this stage.

What:           /sys/kernel/debug/habanalabs/hl<n>/device
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Enables the root user to set the device to specific state.
                Valid values are "disable", "enable", "suspend", "resume".
                User can read this property to see the valid values

What:           /sys/kernel/debug/habanalabs/hl<n>/device_release_watchdog_timeout
Date:           Oct 2022
KernelVersion:  6.2
Contact:        <EMAIL>
Description:    The watchdog timeout value in seconds for a device release upon
                certain error cases, after which the device is reset.

What:           /sys/kernel/debug/habanalabs/hl<n>/dma_size
Date:           Apr 2021
KernelVersion:  5.13
Contact:        <EMAIL>
Description:    Specify the size of the DMA transaction when using DMA to read
                from the device's internal memory. The value can not be larger
                than 128MB. Writing to this value initiates the DMA transfer.
                When the write is finished, the user can read the "data_dma"
                blob

What:           /sys/kernel/debug/habanalabs/hl<n>/dump_razwi_events
Date:           Aug 2022
KernelVersion:  5.20
Contact:        <EMAIL>
Description:    Dumps all razwi events to dmesg if exist.
                After reading the status register of an existing event
                the routine will clear the status register.
                Usage: cat dump_razwi_events

What:           /sys/kernel/debug/habanalabs/hl<n>/dump_security_violations
Date:           Jan 2021
KernelVersion:  5.12
Contact:        <EMAIL>
Description:    Dumps all security violations to dmesg. This will also ack
                all security violations meanings those violations will not be
                dumped next time user calls this API

What:           /sys/kernel/debug/habanalabs/hl<n>/engines
Date:           Jul 2019
KernelVersion:  5.3
Contact:        <EMAIL>
Description:    Displays the status registers values of the device engines and
                their derived idle status

What:           /sys/kernel/debug/habanalabs/hl<n>/i2c_addr
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets I2C device address for I2C transaction that is generated
                by the device's CPU, Not available when device is loaded with secured
                firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/i2c_bus
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets I2C bus address for I2C transaction that is generated by
                the device's CPU, Not available when device is loaded with secured
                firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/i2c_data
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Triggers an I2C transaction that is generated by the device's
                CPU. Writing to this file generates a write transaction while
                reading from the file generates a read transaction, Not available
                when device is loaded with secured firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/i2c_len
Date:           Dec 2021
KernelVersion:  5.17
Contact:        <EMAIL>
Description:    Sets I2C length in bytes for I2C transaction that is generated by
                the device's CPU, Not available when device is loaded with secured
                firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/i2c_reg
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets I2C register id for I2C transaction that is generated by
                the device's CPU, Not available when device is loaded with secured
                firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/led0
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets the state of the first S/W led on the device, Not available
                when device is loaded with secured firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/led1
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets the state of the second S/W led on the device, Not available
                when device is loaded with secured firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/led2
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets the state of the third S/W led on the device, Not available
                when device is loaded with secured firmware

What:           /sys/kernel/debug/habanalabs/hl<n>/memory_scrub
Date:           May 2022
KernelVersion:  5.19
Contact:        <EMAIL>
Description:    Allows the root user to scrub the dram memory. The scrubbing
                value can be set using the debugfs file memory_scrub_val.

What:           /sys/kernel/debug/habanalabs/hl<n>/memory_scrub_val
Date:           May 2022
KernelVersion:  5.19
Contact:        <EMAIL>
Description:    The value to which the dram will be set to when the user
                scrubs the dram using 'memory_scrub' debugfs file and
                the scrubbing value when using module param 'memory_scrub'

What:           /sys/kernel/debug/habanalabs/hl<n>/mmu
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Displays the hop values and physical address for a given ASID
                and virtual address. The user should write the ASID and VA into
                the file and then read the file to get the result.
                e.g. to display info about VA 0x1000 for ASID 1 you need to do:
                echo "1 0x1000" > /sys/kernel/debug/habanalabs/hl0/mmu

What:           /sys/kernel/debug/habanalabs/hl<n>/mmu_error
Date:           Mar 2021
KernelVersion:  5.12
Contact:        <EMAIL>
Description:    Check and display page fault or access violation mmu errors for
                all MMUs specified in mmu_cap_mask.
                e.g. to display error info for MMU hw cap bit 9, you need to do:
                echo "0x200" > /sys/kernel/debug/habanalabs/hl0/mmu_error
                cat /sys/kernel/debug/habanalabs/hl0/mmu_error

What:           /sys/kernel/debug/habanalabs/hl<n>/monitor_dump
Date:           Mar 2022
KernelVersion:  5.19
Contact:        <EMAIL>
Description:    Allows the root user to dump monitors status from the device's
                protected config space.
                This property is a binary blob that contains the result of the
                monitors registers dump.
                This custom interface is needed (instead of using the generic
                Linux user-space PCI mapping) because this space is protected
                and cannot be accessed using PCI read.
                This interface doesn't support concurrency in the same device.
                Only supported on GAUDI.

What:           /sys/kernel/debug/habanalabs/hl<n>/monitor_dump_trig
Date:           Mar 2022
KernelVersion:  5.19
Contact:        <EMAIL>
Description:    Triggers dump of monitor data. The value to trigger the operation
                must be 1. Triggering the monitor dump operation initiates dump of
                current registers values of all monitors.
                When the write is finished, the user can read the "monitor_dump"
                blob

What:           /sys/kernel/debug/habanalabs/hl<n>/set_power_state
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Sets the PCI power state. Valid values are "1" for D0 and "2"
                for D3Hot

What:           /sys/kernel/debug/habanalabs/hl<n>/skip_reset_on_timeout
Date:           Jun 2021
KernelVersion:  5.13
Contact:        <EMAIL>
Description:    Sets the skip reset on timeout option for the device. Value of
                "0" means device will be reset in case some CS has timed out,
                otherwise it will not be reset.

What:           /sys/kernel/debug/habanalabs/hl<n>/state_dump
Date:           Oct 2021
KernelVersion:  5.15
Contact:        <EMAIL>
Description:    Gets the state dump occurring on a CS timeout or failure.
                State dump is used for debug and is created each time in case of
                a problem in a CS execution, before reset.
                Reading from the node returns the newest state dump available.
                Writing an integer X discards X state dumps, so that the
                next read would return X+1-st newest state dump.

What:           /sys/kernel/debug/habanalabs/hl<n>/stop_on_err
Date:           Mar 2020
KernelVersion:  5.6
Contact:        <EMAIL>
Description:    Sets the stop-on_error option for the device engines. Value of
                "0" is for disable, otherwise enable.
                Relevant only for GOYA and GAUDI.

What:           /sys/kernel/debug/habanalabs/hl<n>/timeout_locked
Date:           Sep 2021
KernelVersion:  5.16
Contact:        <EMAIL>
Description:    Sets the command submission timeout value in seconds.

What:           /sys/kernel/debug/habanalabs/hl<n>/userptr
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Displays a list with information about the currently user
                pointers (user virtual addresses) that are pinned and mapped
                to DMA addresses

What:           /sys/kernel/debug/habanalabs/hl<n>/userptr_lookup
Date:           Oct 2021
KernelVersion:  5.15
Contact:        <EMAIL>
Description:    Allows to search for specific user pointers (user virtual
                addresses) that are pinned and mapped to DMA addresses, and see
                their resolution to the specific dma address.

What:           /sys/kernel/debug/habanalabs/hl<n>/vm
Date:           Jan 2019
KernelVersion:  5.1
Contact:        <EMAIL>
Description:    Displays a list with information about all the active virtual
                address mappings per ASID and all user mappings of HW blocks
