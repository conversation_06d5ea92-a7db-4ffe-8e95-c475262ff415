What:		/sys/kernel/debug/cxl/memX/inject_poison
Date:		April, 2023
KernelVersion:	v6.4
Contact:	<EMAIL>
Description:
		(WO) When a Device Physical Address (DPA) is written to this
		attribute, the memdev driver sends an inject poison command to
		the device for the specified address. The DPA must be 64-byte
		aligned and the length of the injected poison is 64-bytes. If
		successful, the device returns poison when the address is
		accessed through the CXL.mem bus. Injecting poison adds the
		address to the device's Poison List and the error source is set
		to Injected. In addition, the device adds a poison creation
		event to its internal Informational Event log, updates the
		Event Status register, and if configured, interrupts the host.
		It is not an error to inject poison into an address that
		already has poison present and no error is returned. The
		inject_poison attribute is only visible for devices supporting
		the capability.


What:		/sys/kernel/debug/memX/clear_poison
Date:		April, 2023
KernelVersion:	v6.4
Contact:	<EMAIL>
Description:
		(WO) When a Device Physical Address (DPA) is written to this
		attribute, the memdev driver sends a clear poison command to
		the device for the specified address. Clearing poison removes
		the address from the device's Poison List and writes 0 (zero)
		for 64 bytes starting at address. It is not an error to clear
		poison from an address that does not have poison set. If the
		device cannot clear poison from the address, -ENXIO is returned.
		The clear_poison attribute is only visible for devices
		supporting the capability.
