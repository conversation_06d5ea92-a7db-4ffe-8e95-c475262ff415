What:		/sys/bus/mhi/devices/.../serialnumber
Date:		Sept 2020
KernelVersion:	5.10
Contact:	<EMAIL>
Description:	The file holds the serial number of the client device obtained
		using a BHI (Boot Host Interface) register read after at least
		one attempt to power up the device has been done. If read
		without having the device power on at least once, the file will
		read all 0's.
Users:		Any userspace application or clients interested in device info.

What:		/sys/bus/mhi/devices/.../oem_pk_hash
Date:		Sept 2020
KernelVersion:	5.10
Contact:	<EMAIL>
Description:	The file holds the OEM PK Hash value of the endpoint device
		obtained using a BHI (Boot Host Interface) register read after
		at least one attempt to power up the device has been done. If
		read without having the device power on at least once, the file
		will read all 0's.
Users:		Any userspace application or clients interested in device info.

What:           /sys/bus/mhi/devices/.../soc_reset
Date:           April 2022
KernelVersion:  5.19
Contact:        <EMAIL>
Description:	Initiates a SoC reset on the MHI controller.  A SoC reset is
                a reset of last resort, and will require a complete re-init.
                This can be useful as a method of recovery if the device is
                non-responsive, or as a means of loading new firmware as a
                system administration task.
